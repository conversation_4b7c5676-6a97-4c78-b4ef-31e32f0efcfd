package com.image_service.controller.Impl;

import com.image_service.controller.IImageDataController;
import com.image_service.service.IImageDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;

@RestController
@RequestMapping("/image")
public class ImageDataControllerImpl implements IImageDataController {

    @Autowired
    private IImageDataService imageService;

    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> uploadImage(@RequestParam String path, @RequestPart("image") MultipartFile file) throws IOException {
        return ResponseEntity.status(HttpStatus.OK)
                .body(imageService.uploadImage(path, file));
    }

    @PostMapping(path = "/bulk", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> uploadBulkImage(@RequestParam String path, @RequestParam("images") MultipartFile[] files) throws IOException {
        return ResponseEntity.status(HttpStatus.OK)
                .body(imageService.uploadBulkImage(path, files));
    }

    @GetMapping("/{fileName}")
    public ResponseEntity<byte[]> getImageByName(@PathVariable String fileName) throws IOException {
        byte[] imageBytes = imageService.getImageByName(fileName);
        String contentType = imageService.getImageContentType(fileName);

        return ResponseEntity
                .status(HttpStatus.OK)
                .contentType(MediaType.parseMediaType(contentType))
                .body(imageBytes);
    }


    @DeleteMapping("/{fileName}")
    public void deleteImageByName(@PathVariable String fileName) {
        imageService.deleteImageByName(fileName);
    }
}
