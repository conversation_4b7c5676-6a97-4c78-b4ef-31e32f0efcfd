package com.image_service.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

public interface IImageDataController {
    ResponseEntity<?> getImageByName(String fileName) throws IOException;
    ResponseEntity<?> uploadImage(String path, MultipartFile image) throws IOException;
    ResponseEntity<?> uploadBulkImage(String path, MultipartFile[] files) throws IOException;
    void deleteImageByName(String fileName);
}
