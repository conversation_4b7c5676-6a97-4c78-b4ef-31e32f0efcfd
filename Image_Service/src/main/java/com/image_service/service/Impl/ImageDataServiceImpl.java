package com.image_service.service.Impl;

import com.image_service.model.ImageData;
import com.image_service.repository.ImageDataRepository;
import com.image_service.service.IImageDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Service
public class ImageDataServiceImpl implements IImageDataService {

    @Autowired
    private ImageDataRepository imageDataRepository;

    @Override
    public Map<String, Object> uploadImage(String path, MultipartFile image) throws IOException {
        createDirectory(path);
        return saveImage(path, image, 0);
    }

    @Override
    public Map<String, Object> uploadBulkImage(String path, MultipartFile[] files) throws IOException {
        createDirectory(path);

        Map<String, Object> response = new HashMap<>();
        int index = 1;

        for (MultipartFile file : files) {
            Map<String, Object> result = saveImage(path, file, index);
            response.putAll(result);
            index++;
        }
        response.put("status", true);
        return response;
    }

    private Map<String, Object> saveImage(String path, MultipartFile file, int index) throws IOException {
        Map<String, Object> response = new HashMap<>();

        String originalFilename = file.getOriginalFilename();
        String extension = "";
        int dotIndex = originalFilename.lastIndexOf('.');
        if (dotIndex >= 0) {
            extension = originalFilename.substring(dotIndex);
            originalFilename = originalFilename.substring(0, dotIndex);
        }

        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("ddMMyyyy_HHmmss"));
        String filename = originalFilename + "_" + timestamp + extension;
        String filePath = path + filename;

        ImageData fileData = imageDataRepository.save(
                ImageData.builder()
                        .name(filename)
                        .type(file.getContentType())
                        .filePath(filePath)
                        .build()
        );

        file.transferTo(new File(filePath));

        if (fileData != null) {
            // Eğer index 0 ise bu tekli yüklemedir, değilse çoklu yüklemedir
            response.put(index == 0 ? "filename" : "filename" + index, filename);
            response.put(index == 0 ? "path" : "path" + index, filePath);
        } else {
            response.put("status", false);
        }
        return response;
    }

    @Override
    public byte[] getImageByName(String fileName) throws IOException {
        Optional<ImageData> fileData = imageDataRepository.findByName(fileName);
        if (fileData.isEmpty()) {
            return null; // Dosya bulunamadıysa null dön
        }

        String filePath = fileData.get().getFilePath();
        File file = new File(filePath);

        if (!file.exists()) {
            return null; // Fiziksel dosya bulunamadıysa null dön
        }

        return Files.readAllBytes(new File(filePath).toPath());
    }

    @Override
    public void deleteImageByName(String fileName) {
        Optional<ImageData> fileData = imageDataRepository.findByName(fileName);
        String filePath = fileData.get().getFilePath();

        try {
            Files.deleteIfExists(Paths.get(filePath));
            imageDataRepository.deleteById(fileData.get().getId());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public String getImageContentType(String fileName) {
        return imageDataRepository.findByName(fileName)
                .map(ImageData::getType)
                .orElse("application/octet-stream"); // varsayılan
    }

    void createDirectory(String path) {
        File directory = new File(path);
        if (!directory.exists()) {
            directory.mkdirs();
        }
    }
}
