package com.image_service.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

public interface IImageDataService {
    byte[] getImageByName(String fileName) throws IOException;
    Map<String, Object> uploadImage(String path, MultipartFile image) throws IOException;
    Map<String, Object> uploadBulkImage(String path, MultipartFile[] files) throws IOException;
    void deleteImageByName(String fileName);
    String getImageContentType(String fileName);
}
