spring:
  application:
    name: mail-service

  cloud:
    config:
      uri: http://config-service:8888

  config:
    import: "configserver:"

  kafka:
    bootstrap-servers: kafka:9092
    consumer:
      group-id: mail-service-group
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      enable-auto-commit: true
      auto-commit-interval: 1000

  mail:
    host: smtp.office365.com
    port: 587
    username: <EMAIL>
    password: Test04042025.
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

  thymeleaf:
    cache: false
    check-template-location: false
    enabled: true
    mode: HTML
    encoding: UTF-8
    servlet:
      content-type: text/html

management:
  endpoints:
    web:
      exposure:
        include: "*" 