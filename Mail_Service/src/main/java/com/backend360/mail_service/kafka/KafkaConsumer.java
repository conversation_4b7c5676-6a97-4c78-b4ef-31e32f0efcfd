package com.backend360.mail_service.kafka;

import com.backend360.mail_service.dto.CampaignDto;
import com.backend360.mail_service.service.CampaignNotificationService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class KafkaConsumer {

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CampaignNotificationService campaignNotificationService;

    @KafkaListener(topics = "campaign-created", groupId = "mail-service-group")
    public void handleCampaignCreated(String campaignJson) {
        try {
            log.info("Kafka'dan kampanya oluşturma eventi alındı: {}", campaignJson);
            
            CampaignDto campaignDto = objectMapper.readValue(campaignJson, CampaignDto.class);
            
            log.info("Kampanya eventi işleniyor. Kampanya: {}", campaignDto.getName());
            
            // Asenkron olarak mail gönderimi yap
            campaignNotificationService.sendNewCampaignNotifications(campaignDto);
            
        } catch (JsonProcessingException e) {
            log.error("Campaign JSON parse edilirken hata oluştu: {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("Kampanya eventi işlenirken hata oluştu: {}", e.getMessage(), e);
        }
    }
} 