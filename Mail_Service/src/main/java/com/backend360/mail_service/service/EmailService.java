package com.backend360.mail_service.service;

import com.backend360.mail_service.dto.EmailRequestDto;
import com.backend360.mail_service.dto.EmailResponseDto;

public interface EmailService {
    EmailResponseDto sendFormToCampaignEmail(EmailRequestDto emailRequest);
    EmailResponseDto sendCustomerToCampaignEmail(EmailRequestDto emailRequest);
    EmailResponseDto sendFormSuccessEmail(EmailRequestDto emailRequest);
    EmailResponseDto sendNewCampaignNotificationEmail(EmailRequestDto emailRequest);
} 