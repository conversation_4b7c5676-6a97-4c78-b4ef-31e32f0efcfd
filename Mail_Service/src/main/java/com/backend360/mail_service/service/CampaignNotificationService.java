package com.backend360.mail_service.service;

import com.backend360.mail_service.dto.CampaignDto;
import com.backend360.mail_service.dto.EmailRequestDto;
import com.backend360.mail_service.dto.EmailResponseDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
public class CampaignNotificationService {

    @Autowired
    private EmailService emailService;

    @Autowired
    private CustomerService customerService;

    @Async
    public CompletableFuture<Void> sendNewCampaignNotifications(CampaignDto campaignDto) {
        try {
            log.info("Yeni kampanya bildirimleri gönderiliyor. Kampanya: {}", campaignDto.getName());
            
            // remindMe=true olan tüm müşterileri al
            List<String> remindMeCustomers = customerService.getRemindMeCustomers();
            
            log.info("Toplam {} müşteriye bildirim gönderilecek", remindMeCustomers.size());
            
            for (String customerEmail : remindMeCustomers) {
                try {
                    sendNotificationToCustomer(customerEmail, campaignDto);
                } catch (Exception e) {
                    log.error("Müşteriye bildirim gönderilirken hata oluştu. Email: {}, Hata: {}", 
                            customerEmail, e.getMessage(), e);
                }
            }
            
            log.info("Tüm kampanya bildirimleri gönderildi. Kampanya: {}", campaignDto.getName());
            
        } catch (Exception e) {
            log.error("Kampanya bildirimleri gönderilirken genel hata oluştu: {}", e.getMessage(), e);
        }
        
        return CompletableFuture.completedFuture(null);
    }

    private void sendNotificationToCustomer(String customerEmail, CampaignDto campaignDto) {
        try {
            log.info("Müşteriye kampanya bildirimi gönderiliyor. Email: {}", customerEmail);
            
            EmailRequestDto emailRequest = new EmailRequestDto(
                customerEmail,
                "Değerli Müşterimiz", // Müşteri adı bilgisi yoksa genel hitap
                campaignDto.getName() != null ? campaignDto.getName() : "Yeni Kampanya",
                campaignDto.getDescription() != null ? campaignDto.getDescription() : "Kampanya açıklaması",
                "NEW_CAMPAIGN_NOTIFICATION"
            );

            EmailResponseDto response = emailService.sendNewCampaignNotificationEmail(emailRequest);
            
            if (response.isSuccess()) {
                log.info("Kampanya bildirimi başarıyla gönderildi. Email: {}", customerEmail);
            } else {
                log.error("Kampanya bildirimi gönderilemedi. Email: {}, Hata: {}", 
                        customerEmail, response.getMessage());
            }
            
        } catch (Exception e) {
            log.error("Müşteriye kampanya bildirimi gönderilirken hata oluştu. Email: {}, Hata: {}", 
                    customerEmail, e.getMessage(), e);
        }
    }
} 