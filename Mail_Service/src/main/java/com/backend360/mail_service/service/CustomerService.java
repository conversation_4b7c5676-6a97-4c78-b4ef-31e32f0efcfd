package com.backend360.mail_service.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class CustomerService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    // Yeni endpoint
    private static final String CUSTOMER_API_URL = "https://360avantajli.com/api/Campaign_Service/customer";

    public List<String> getRemindMeCustomers() {
        List<String> emails = new ArrayList<>();
        try {
            String response = restTemplate.getForObject(CUSTOMER_API_URL, String.class);
            JsonNode root = objectMapper.readTree(response);

            for (JsonNode customer : root) {
                // remindMe == true ve isActive == true ise ekle
                boolean remindMe = customer.has("remindMe") && customer.get("remindMe").asBoolean(false);
                boolean isActive = customer.has("isActive") && customer.get("isActive").asBoolean(false);
                if (remindMe && isActive) {
                    String email = customer.get("email").asText();
                    emails.add(email);
                }
            }
            log.info("Toplam {} remindMe müşterisi bulundu", emails.size());
        } catch (Exception e) {
            log.error("RemindMe müşterileri alınırken hata oluştu: {}", e.getMessage(), e);
        }
        return emails;
    }
} 