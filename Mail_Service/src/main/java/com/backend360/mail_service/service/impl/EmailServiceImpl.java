package com.backend360.mail_service.service.impl;

import com.backend360.mail_service.dto.EmailRequestDto;
import com.backend360.mail_service.dto.EmailResponseDto;
import com.backend360.mail_service.service.EmailService;
import jakarta.mail.internet.MimeMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

@Service
public class EmailServiceImpl implements EmailService {
    
    private static final Logger logger = LoggerFactory.getLogger(EmailServiceImpl.class);
    
    private final JavaMailSender mailSender;

    @Autowired
    public EmailServiceImpl(JavaMailSender mailSender) {
        this.mailSender = mailSender;
    }

    @Override
    public EmailResponseDto sendFormToCampaignEmail(EmailRequestDto emailRequest) {
        try {
            logger.info("FormToCampaign email gönderimi başlıyor - Alıcı: {}", emailRequest.getTo());
            
            String htmlContent = createEmailTemplate(
                emailRequest.getFullName(), 
                emailRequest.getCampaignName(), 
                emailRequest.getCampaignDescription()
            );

            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setTo(emailRequest.getTo());
            helper.setSubject("Kampanya Katılımınız Alındı - 360avantajli.com");
            helper.setText(htmlContent, true);
            helper.setFrom("<EMAIL>");

            mailSender.send(message);
            logger.info("FormToCampaign email başarıyla gönderildi!");
            
            return new EmailResponseDto(true, "Email başarıyla gönderildi", "FORM_TO_CAMPAIGN", emailRequest.getTo());
            
        } catch (Exception e) {
            logger.error("FormToCampaign email gönderimi sırasında hata oluştu (Alıcı: {}): {}", emailRequest.getTo(), e.getMessage(), e);
            return new EmailResponseDto(false, "Email gönderimi başarısız: " + e.getMessage(), "FORM_TO_CAMPAIGN", emailRequest.getTo());
        }
    }

    @Override
    public EmailResponseDto sendCustomerToCampaignEmail(EmailRequestDto emailRequest) {
        try {
            logger.info("CustomerToCampaign email gönderimi başlıyor - Alıcı: {}", emailRequest.getTo());
            
            String htmlContent = createEmailTemplate(
                emailRequest.getFullName(), 
                emailRequest.getCampaignName(), 
                emailRequest.getCampaignDescription()
            );

            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setTo(emailRequest.getTo());
            helper.setSubject("Kampanya Katılımınız Alındı - 360avantajli.com");
            helper.setText(htmlContent, true);
            helper.setFrom("<EMAIL>");

            mailSender.send(message);
            logger.info("CustomerToCampaign email başarıyla gönderildi!");
            
            return new EmailResponseDto(true, "Email başarıyla gönderildi", "CUSTOMER_TO_CAMPAIGN", emailRequest.getTo());
            
        } catch (Exception e) {
            logger.error("CustomerToCampaign email gönderimi sırasında hata oluştu (Alıcı: {}): {}", emailRequest.getTo(), e.getMessage(), e);
            return new EmailResponseDto(false, "Email gönderimi başarısız: " + e.getMessage(), "CUSTOMER_TO_CAMPAIGN", emailRequest.getTo());
        }
    }

    @Override
    public EmailResponseDto sendFormSuccessEmail(EmailRequestDto emailRequest) {
        try {
            logger.info("FormSuccess email gönderimi başlıyor - Alıcı: {}", emailRequest.getTo());
            
            String htmlContent = createEmailTemplate(
                emailRequest.getFullName(), 
                emailRequest.getCampaignName(), 
                emailRequest.getCampaignDescription()
            );

            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setTo(emailRequest.getTo());
            helper.setSubject("Başvurunuz Alındı - 360avantajli.com");
            helper.setText(htmlContent, true);
            helper.setFrom("<EMAIL>");

            mailSender.send(message);
            logger.info("FormSuccess email başarıyla gönderildi!");
            
            return new EmailResponseDto(true, "Email başarıyla gönderildi", "FORM_SUCCESS", emailRequest.getTo());
            
        } catch (Exception e) {
            logger.error("FormSuccess email gönderimi sırasında hata oluştu (Alıcı: {}): {}", emailRequest.getTo(), e.getMessage(), e);
            return new EmailResponseDto(false, "Email gönderimi başarısız: " + e.getMessage(), "FORM_SUCCESS", emailRequest.getTo());
        }
    }

    @Override
    public EmailResponseDto sendNewCampaignNotificationEmail(EmailRequestDto emailRequest) {
        try {
            logger.info("NewCampaignNotification email gönderimi başlıyor - Alıcı: {}", emailRequest.getTo());
            
            String htmlContent = createNewCampaignNotificationTemplate(
                emailRequest.getFullName(), 
                emailRequest.getCampaignName(), 
                emailRequest.getCampaignDescription()
            );

            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setTo(emailRequest.getTo());
            helper.setSubject("Yeni Kampanya: " + emailRequest.getCampaignName() + " - 360avantajli.com");
            helper.setText(htmlContent, true);
            helper.setFrom("<EMAIL>");

            mailSender.send(message);
            logger.info("NewCampaignNotification email başarıyla gönderildi!");
            
            return new EmailResponseDto(true, "Email başarıyla gönderildi", "NEW_CAMPAIGN_NOTIFICATION", emailRequest.getTo());
            
        } catch (Exception e) {
            logger.error("NewCampaignNotification email gönderimi sırasında hata oluştu (Alıcı: {}): {}", emailRequest.getTo(), e.getMessage(), e);
            return new EmailResponseDto(false, "Email gönderimi başarısız: " + e.getMessage(), "NEW_CAMPAIGN_NOTIFICATION", emailRequest.getTo());
        }
    }
    
    private String createEmailTemplate(String fullName, String campaignName, String campaignDescription) {
        return """
            <!DOCTYPE html>
            <html lang="tr">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Kampanya Başvurunuz Alındı</title>
            </head>
            <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f0f8ff;">
            
            <table width="100%%" cellpadding="0" cellspacing="0" border="0" style="background-color: #f0f8ff; padding: 20px;">
                <tr>
                    <td align="center">
                        <!-- Main Container -->
                        <table width="600" cellpadding="0" cellspacing="0" border="0" style="background-color: #ffffff; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
            
                            <!-- Header -->
                            <tr>
                                <td style="background: linear-gradient(135deg, #2196F3, #1976D2); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                                    <!-- Success Icon -->
                                    <div style="background-color: #ffffff; width: 60px; height: 60px; border-radius: 50%%; margin: 0 auto 15px; display: table-cell; vertical-align: middle; text-align: center;">
                                        <span style="color: #4CAF50; font-size: 30px; font-weight: bold;">✓</span>
                                    </div>
                                    <h1 style="color: #ffffff; font-size: 24px; margin: 10px 0; font-weight: bold;">Başvurunuz Alındı!</h1>
                                    <p style="color: #e3f2fd; font-size: 16px; margin: 0;">360avantajli.com'a hoş geldiniz</p>
                                </td>
                            </tr>
            
                            <!-- Content -->
                            <tr>
                                <td style="padding: 30px;">
            
                                    <!-- Greeting -->
                                    <h2 style="color: #333333; font-size: 20px; text-align: center; margin: 0 0 15px 0;">
                                        Merhaba <span style="color: #2196F3;">%s</span>!
                                    </h2>
                                    <p style="color: #666666; font-size: 16px; text-align: center; line-height: 1.5; margin: 0 0 25px 0;">
                                        Formunuz başarıyla alınmıştır. En kısa sürede sizinle iletişime geçeceğiz.
                                    </p>
            
                                    <!-- Campaign Info -->
                                    <table width="100%%" cellpadding="0" cellspacing="0" border="0" style="background-color: #e3f2fd; border-radius: 8px; margin: 20px 0;">
                                        <tr>
                                            <td style="padding: 20px;">
                                                <h3 style="color: #1976D2; font-size: 16px; margin: 0 0 10px 0; font-weight: bold;">
                                                    🎯 Katıldığınız Kampanya
                                                </h3>
                                                <div style="background-color: #ffffff; padding: 15px; border-radius: 5px; border-left: 4px solid #2196F3;">
                                                    <p style="margin: 0 0 8px 0; color: #333333; font-size: 16px; font-weight: bold;">%s</p>
                                                    <p style="margin: 0; color: #666666; font-size: 14px;">%s</p>
                                                </div>
                                            </td>
                                        </tr>
                                    </table>
            
                                    <!-- Steps -->
                                    <table width="100%%" cellpadding="0" cellspacing="0" border="0" style="background-color: #f9f9f9; border-radius: 8px; margin: 20px 0;">
                                        <tr>
                                            <td style="padding: 20px; text-align: center;">
                                                <h3 style="color: #333333; font-size: 16px; margin: 0 0 15px 0; font-weight: bold;">Sıradaki Adımlar</h3>
            
                                                <table width="100%%" cellpadding="10" cellspacing="0" border="0">
                                                    <tr>
                                                        <td style="text-align: center; width: 33%%;">
                                                            <div style="background-color: #4CAF50; color: white; width: 30px; height: 30px; border-radius: 50%%; margin: 0 auto 5px; line-height: 30px; font-weight: bold;">1</div>
                                                            <p style="margin: 0; font-size: 12px; color: #666666;">İnceleme</p>
                                                        </td>
                                                        <td style="text-align: center; width: 33%%;">
                                                            <div style="background-color: #FF9800; color: white; width: 30px; height: 30px; border-radius: 50%%; margin: 0 auto 5px; line-height: 30px; font-weight: bold;">2</div>
                                                            <p style="margin: 0; font-size: 12px; color: #666666;">İletişim</p>
                                                        </td>
                                                        <td style="text-align: center; width: 33%%;">
                                                            <div style="background-color: #9C27B0; color: white; width: 30px; height: 30px; border-radius: 50%%; margin: 0 auto 5px; line-height: 30px; font-weight: bold;">3</div>
                                                            <p style="margin: 0; font-size: 12px; color: #666666;">Aktivasyon</p>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </table>
            
                                    <!-- Action Button -->
                                    <div style="text-align: center; margin: 25px 0;">
                                        <a href="https://360avantajli.com" style="background-color: #2196F3; color: white; padding: 12px 25px; border-radius: 25px; text-decoration: none; font-weight: bold; display: inline-block;">
                                            🌐 360avantajli.com'u Ziyaret Et
                                        </a>
                                    </div>
            
                                </td>
                            </tr>
            
                            <!-- Footer -->
                            <tr>
                                <td style="background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; border-top: 1px solid #e9ecef;">
                                    <div style="margin-bottom: 15px;">
                                        <div style="background-color: #2196F3; color: white; padding: 8px 15px; border-radius: 5px; display: inline-block; font-weight: bold;">
                                            360avantajli
                                        </div>
                                    </div>
                                    <p style="color: #6c757d; font-size: 14px; margin: 0 0 8px 0;">360avantajli.com ekibi olarak teşekkür ederiz!</p>
                                    <p style="color: #adb5bd; font-size: 12px; margin: 0;">
                                        Bu email otomatik olarak gönderilmiştir. Lütfen yanıtlamayınız.<br>
                                        Sorularınız için: <a href="mailto:<EMAIL>" style="color: #2196F3;"><EMAIL></a>
                                    </p>
                                </td>
                            </tr>
            
                        </table>
                    </td>
                </tr>
            </table>
            
            </body>
            </html>
            """.formatted(fullName, campaignName, campaignDescription);
    }

    private String createNewCampaignNotificationTemplate(String fullName, String campaignName, String campaignDescription) {
        return """
            <!DOCTYPE html>
            <html lang="tr">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Yeni Kampanya Bildirimi</title>
            </head>
            <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f0f8ff;">
            
            <table width="100%%" cellpadding="0" cellspacing="0" border="0" style="background-color: #f0f8ff; padding: 20px;">
                <tr>
                    <td align="center">
                        <!-- Main Container -->
                        <table width="600" cellpadding="0" cellspacing="0" border="0" style="background-color: #ffffff; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
            
                            <!-- Header -->
                            <tr>
                                <td style="background: linear-gradient(135deg, #2196F3, #1976D2); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                                    <!-- New Campaign Icon -->
                                    <div style="background-color: #ffffff; width: 60px; height: 60px; border-radius: 50%%; margin: 0 auto 15px; display: table-cell; vertical-align: middle; text-align: center;">
                                        <span style="color: #2196F3; font-size: 30px; font-weight: bold;">🎯</span>
                                    </div>
                                    <h1 style="color: #ffffff; font-size: 24px; margin: 10px 0; font-weight: bold;">Yeni Kampanya!</h1>
                                    <p style="color: #e3f2fd; font-size: 16px; margin: 0;">360avantajli.com'dan özel fırsat</p>
                                </td>
                            </tr>
            
                            <!-- Content -->
                            <tr>
                                <td style="padding: 30px;">
            
                                    <!-- Greeting -->
                                    <h2 style="color: #333333; font-size: 20px; text-align: center; margin: 0 0 15px 0;">
                                        Merhaba <span style="color: #2196F3;">%s</span>!
                                    </h2>
                                    <p style="color: #666666; font-size: 16px; text-align: center; line-height: 1.5; margin: 0 0 25px 0;">
                                        Sizin için yeni bir kampanya hazırladık! Bu fırsatı kaçırmayın.
                                    </p>
            
                                    <!-- Campaign Info -->
                                    <table width="100%%" cellpadding="0" cellspacing="0" border="0" style="background-color: #e3f2fd; border-radius: 8px; margin: 20px 0;">
                                        <tr>
                                            <td style="padding: 20px;">
                                                <h3 style="color: #1976D2; font-size: 16px; margin: 0 0 10px 0; font-weight: bold;">
                                                    🆕 Yeni Kampanya
                                                </h3>
                                                <div style="background-color: #ffffff; padding: 15px; border-radius: 5px; border-left: 4px solid #2196F3;">
                                                    <p style="margin: 0 0 8px 0; color: #333333; font-size: 16px; font-weight: bold;">%s</p>
                                                    <p style="margin: 0; color: #666666; font-size: 14px;">%s</p>
                                                </div>
                                            </td>
                                        </tr>
                                    </table>
            
                                    <!-- Action Button -->
                                    <div style="text-align: center; margin: 25px 0;">
                                        <a href="https://360avantajli.com" style="background-color: #2196F3; color: white; padding: 12px 25px; border-radius: 25px; text-decoration: none; font-weight: bold; display: inline-block;">
                                            🚀 Kampanyaya Katıl
                                        </a>
                                    </div>
            
                                    <!-- Unsubscribe -->
                                    <div style="text-align: center; margin: 20px 0;">
                                        <p style="color: #999999; font-size: 12px; margin: 0;">
                                            Bu bildirimleri almak istemiyorsanız 
                                            <a href="mailto:<EMAIL>" style="color: #2196F3;">buraya tıklayın</a>
                                        </p>
                                    </div>
            
                                </td>
                            </tr>
            
                            <!-- Footer -->
                            <tr>
                                <td style="background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; border-top: 1px solid #e9ecef;">
                                    <div style="margin-bottom: 15px;">
                                        <div style="background-color: #2196F3; color: white; padding: 8px 15px; border-radius: 5px; display: inline-block; font-weight: bold;">
                                            360avantajli
                                        </div>
                                    </div>
                                    <p style="color: #6c757d; font-size: 14px; margin: 0 0 8px 0;">360avantajli.com ekibi olarak teşekkür ederiz!</p>
                                    <p style="color: #adb5bd; font-size: 12px; margin: 0;">
                                        Bu email otomatik olarak gönderilmiştir. Lütfen yanıtlamayınız.<br>
                                        Sorularınız için: <a href="mailto:<EMAIL>" style="color: #2196F3;"><EMAIL></a>
                                    </p>
                                </td>
                            </tr>
            
                        </table>
                    </td>
                </tr>
            </table>
            
            </body>
            </html>
            """.formatted(fullName, campaignName, campaignDescription);
    }
} 