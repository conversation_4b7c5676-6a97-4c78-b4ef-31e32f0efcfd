package com.backend360.mail_service.controller;

import com.backend360.mail_service.dto.EmailRequestDto;
import com.backend360.mail_service.dto.EmailResponseDto;
import com.backend360.mail_service.service.EmailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/email")
public class EmailController {

    @Autowired
    private EmailService emailService;

    @PostMapping("/form-campaign")
    public ResponseEntity<EmailResponseDto> sendFormToCampaignEmail(@RequestBody EmailRequestDto emailRequest) {
        EmailResponseDto response = emailService.sendFormToCampaignEmail(emailRequest);
        
        if (response.isSuccess()) {
            return ResponseEntity.ok(response);
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @PostMapping("/customer-campaign")
    public ResponseEntity<EmailResponseDto> sendCustomerToCampaignEmail(@RequestBody EmailRequestDto emailRequest) {
        EmailResponseDto response = emailService.sendCustomerToCampaignEmail(emailRequest);
        
        if (response.isSuccess()) {
            return ResponseEntity.ok(response);
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @PostMapping("/form-success")
    public ResponseEntity<EmailResponseDto> sendFormSuccessEmail(@RequestBody EmailRequestDto emailRequest) {
        EmailResponseDto response = emailService.sendFormSuccessEmail(emailRequest);
        
        if (response.isSuccess()) {
            return ResponseEntity.ok(response);
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @PostMapping("/new-campaign-notification")
    public ResponseEntity<EmailResponseDto> sendNewCampaignNotificationEmail(@RequestBody EmailRequestDto emailRequest) {
        EmailResponseDto response = emailService.sendNewCampaignNotificationEmail(emailRequest);
        
        if (response.isSuccess()) {
            return ResponseEntity.ok(response);
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("Mail Service is running!");
    }
} 