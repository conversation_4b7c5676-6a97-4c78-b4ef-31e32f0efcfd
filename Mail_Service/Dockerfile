FROM maven:3.8.6-eclipse-temurin-17 AS build

WORKDIR /app

COPY pom.xml /app/pom.xml
COPY Service_Parent /app/Service_Parent
COPY Mail_Service /app/Mail_Service

RUN mvn install -f Service_Parent/pom.xml

WORKDIR /app/Mail_Service

RUN mvn clean package -DskipTests

FROM openjdk:17-jdk-slim

WORKDIR /app

COPY --from=build /app/Mail_Service/target/*.jar app.jar
ENTRYPOINT ["java", "-jar", "/app/app.jar"]

EXPOSE 9003