FROM maven:3.8.6-eclipse-temurin-17 AS build
WORKDIR /app

# Gerekli klasör ve dosyaları kopyala
COPY pom.xml /app/pom.xml
COPY Service_Parent /app/Service_Parent
COPY AI_Call_Center_Service /app/AI_Call_Center_Service

# Parent pom'u yükle
RUN mvn install -f Service_Parent/pom.xml

# AI Call Center Service'i build et
WORKDIR /app/AI_Call_Center_Service
RUN mvn clean package -DskipTests

# Çalıştırma aşaması
FROM openjdk:17-jdk-slim
WORKDIR /app
COPY --from=build /app/AI_Call_Center_Service/target/*.jar app.jar

# Net GSM ve AI Engine için environment variables
ENV NETGSM_USERNAME=""
ENV NETGSM_PASSWORD=""
ENV AI_ENGINE_URL="http://localhost:8080"
ENV AI_ENGINE_API_KEY=""

ENTRYPOINT ["java", "-jar", "/app/app.jar"]
EXPOSE 7003
