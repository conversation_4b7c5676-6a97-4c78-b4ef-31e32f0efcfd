package com.backend360.ai_call_center.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

@Entity
@Table(name = "campaign_reports")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CampaignReport {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "campaign_id", unique = true, nullable = false)
    private String campaignId;

    @Column(name = "campaign_name", nullable = false)
    private String campaignName;

    @Column(name = "brand_name")
    private String brandName;

    @Column(name = "campaign_category")
    private String campaignCategory;

    @Column(name = "campaign_status")
    private String campaignStatus; // ACTIVE, PAUSED, COMPLETED

    // Call Statistics
    @Column(name = "total_calls")
    private Integer totalCalls = 0;

    @Column(name = "successful_calls")
    private Integer successfulCalls = 0;

    @Column(name = "failed_calls")
    private Integer failedCalls = 0;

    @Column(name = "no_answer_calls")
    private Integer noAnswerCalls = 0;

    @Column(name = "busy_calls")
    private Integer busyCalls = 0;

    // Outcome Statistics
    @Column(name = "campaign_interest_count")
    private Integer campaignInterestCount = 0;

    @Column(name = "test_drive_requests")
    private Integer testDriveRequests = 0;

    @Column(name = "detailed_info_requests")
    private Integer detailedInfoRequests = 0;

    // Sentiment Analysis
    @Column(name = "positive_sentiment_count")
    private Integer positiveSentimentCount = 0;

    @Column(name = "negative_sentiment_count")
    private Integer negativeSentimentCount = 0;

    @Column(name = "neutral_sentiment_count")
    private Integer neutralSentimentCount = 0;

    // Duration Statistics
    @Column(name = "total_call_duration_seconds")
    private Long totalCallDurationSeconds = 0L;

    @Column(name = "average_call_duration_seconds")
    private Double averageCallDurationSeconds = 0.0;

    // Performance Metrics
    @Column(name = "success_rate")
    private Double successRate = 0.0;

    @Column(name = "interest_rate")
    private Double interestRate = 0.0;

    @Column(name = "conversion_rate")
    private Double conversionRate = 0.0;

    // Timestamps
    @Column(name = "campaign_start_date")
    private LocalDateTime campaignStartDate;

    @Column(name = "campaign_end_date")
    private LocalDateTime campaignEndDate;

    @Column(name = "last_call_date")
    private LocalDateTime lastCallDate;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // Helper methods
    public void updateStatistics() {
        if (totalCalls > 0) {
            this.successRate = (double) successfulCalls / totalCalls * 100;
            this.interestRate = (double) campaignInterestCount / totalCalls * 100;
            this.conversionRate = (double) (testDriveRequests + detailedInfoRequests) / totalCalls * 100;
        }

        if (totalCallDurationSeconds > 0 && successfulCalls > 0) {
            this.averageCallDurationSeconds = (double) totalCallDurationSeconds / successfulCalls;
        }
    }

    public void incrementCallCount(String callResult, String sentiment, Integer duration, 
                                 boolean hasInterest, boolean wantsTestDrive, boolean wantsDetailedInfo) {
        this.totalCalls++;
        
        switch (callResult) {
            case "SUCCESS":
                this.successfulCalls++;
                break;
            case "NO_ANSWER":
                this.noAnswerCalls++;
                break;
            case "BUSY":
                this.busyCalls++;
                break;
            default:
                this.failedCalls++;
                break;
        }

        // Sentiment tracking
        switch (sentiment) {
            case "POSITIVE":
                this.positiveSentimentCount++;
                break;
            case "NEGATIVE":
                this.negativeSentimentCount++;
                break;
            case "NEUTRAL":
                this.neutralSentimentCount++;
                break;
        }

        // Outcome tracking
        if (hasInterest) this.campaignInterestCount++;
        if (wantsTestDrive) this.testDriveRequests++;
        if (wantsDetailedInfo) this.detailedInfoRequests++;

        // Duration tracking
        if (duration != null && duration > 0) {
            this.totalCallDurationSeconds += duration;
        }

        this.lastCallDate = LocalDateTime.now();
        updateStatistics();
    }
}
