package com.backend360.ai_call_center.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;

@Entity
@Table(name = "customer_interests")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerInterest {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "call_id", nullable = false)
    private String callId;

    @Column(name = "phone_number", nullable = false)
    private String phoneNumber;

    @Column(name = "customer_name")
    private String customerName;

    // İlgilenilen Kampanya/Marka Detayları
    @Column(name = "interest_type")
    private String interestType; // ORIGINAL_CAMPAIGN, MENTIONED_BRAND, SUGGESTED_CAMPAIGN, COMPETITOR

    @Column(name = "brand_name", nullable = false)
    private String brandName;

    @Column(name = "campaign_name")
    private String campaignName;

    @Column(name = "category")
    private String category; // OTOMOTİV, SİGORTA, YATIRIM, etc.

    @Column(name = "subcategory")
    private String subcategory; // SUV, SEDAN, HATCHBACK for automotive

    // İlgi Seviyesi ve Detayları
    @Column(name = "interest_level")
    private String interestLevel; // HIGH, MEDIUM, LOW

    @Column(name = "interest_score")
    private Double interestScore; // 0.0 - 1.0

    @Column(name = "mentioned_by")
    private String mentionedBy; // CUSTOMER, AGENT, AI_SUGGESTION

    @Column(name = "context", columnDefinition = "TEXT")
    private String context; // Nasıl gündeme geldi

    @Column(name = "customer_reaction")
    private String customerReaction; // POSITIVE, NEGATIVE, NEUTRAL, CURIOUS

    @Column(name = "specific_features_mentioned", columnDefinition = "TEXT")
    private String specificFeaturesMentioned; // JSON array

    // Aksiyon ve Sonuçlar
    @Column(name = "requested_info")
    private Boolean requestedInfo = false;

    @Column(name = "requested_test_drive")
    private Boolean requestedTestDrive = false;

    @Column(name = "requested_callback")
    private Boolean requestedCallback = false;

    @Column(name = "requested_brochure")
    private Boolean requestedBrochure = false;

    @Column(name = "scheduled_meeting")
    private Boolean scheduledMeeting = false;

    @Column(name = "meeting_date")
    private LocalDateTime meetingDate;

    // Fiyat ve Bütçe Bilgileri
    @Column(name = "budget_mentioned")
    private Boolean budgetMentioned = false;

    @Column(name = "budget_range")
    private String budgetRange; // 0-50K, 50K-100K, 100K-200K, 200K+

    @Column(name = "price_sensitivity")
    private String priceSensitivity; // HIGH, MEDIUM, LOW

    @Column(name = "financing_interest")
    private Boolean financingInterest = false;

    // Timing ve Urgency
    @Column(name = "purchase_timeline")
    private String purchaseTimeline; // IMMEDIATE, 1_MONTH, 3_MONTHS, 6_MONTHS, 1_YEAR, NO_TIMELINE

    @Column(name = "urgency_level")
    private String urgencyLevel; // HIGH, MEDIUM, LOW

    @Column(name = "decision_maker")
    private String decisionMaker; // SELF, SPOUSE, FAMILY, BUSINESS

    // Competitor Analysis
    @Column(name = "competitor_brands_mentioned", columnDefinition = "TEXT")
    private String competitorBrandsMentioned; // JSON array

    @Column(name = "competitor_comparison_made")
    private Boolean competitorComparisonMade = false;

    @Column(name = "our_advantage_highlighted", columnDefinition = "TEXT")
    private String ourAdvantageHighlighted; // JSON array

    // Follow-up Information
    @Column(name = "follow_up_required")
    private Boolean followUpRequired = false;

    @Column(name = "follow_up_type")
    private String followUpType; // CALL, EMAIL, SMS, MEETING

    @Column(name = "follow_up_priority")
    private String followUpPriority; // HIGH, MEDIUM, LOW

    @Column(name = "follow_up_notes", columnDefinition = "TEXT")
    private String followUpNotes;

    @Column(name = "assigned_to")
    private String assignedTo; // Sales rep or team

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    // Helper Methods
    public boolean isHotLead() {
        return "HIGH".equals(interestLevel) && 
               ("IMMEDIATE".equals(purchaseTimeline) || "1_MONTH".equals(purchaseTimeline)) &&
               (requestedTestDrive || scheduledMeeting);
    }

    public boolean isWarmLead() {
        return ("HIGH".equals(interestLevel) || "MEDIUM".equals(interestLevel)) &&
               (requestedInfo || requestedCallback);
    }

    public boolean needsImmediateAttention() {
        return "HIGH".equals(followUpPriority) && followUpRequired;
    }

    public String getLeadQuality() {
        if (isHotLead()) return "HOT";
        if (isWarmLead()) return "WARM";
        if ("LOW".equals(interestLevel)) return "COLD";
        return "LUKEWARM";
    }

    public Double getConversionProbability() {
        double score = 0.0;
        
        // Interest level weight
        switch (interestLevel) {
            case "HIGH": score += 0.4; break;
            case "MEDIUM": score += 0.2; break;
            case "LOW": score += 0.1; break;
        }
        
        // Timeline weight
        switch (purchaseTimeline) {
            case "IMMEDIATE": score += 0.3; break;
            case "1_MONTH": score += 0.2; break;
            case "3_MONTHS": score += 0.1; break;
        }
        
        // Action weight
        if (requestedTestDrive) score += 0.2;
        if (scheduledMeeting) score += 0.3;
        if (requestedInfo) score += 0.1;
        
        // Budget weight
        if (budgetMentioned && !"HIGH".equals(priceSensitivity)) score += 0.1;
        
        return Math.min(1.0, score);
    }
}
