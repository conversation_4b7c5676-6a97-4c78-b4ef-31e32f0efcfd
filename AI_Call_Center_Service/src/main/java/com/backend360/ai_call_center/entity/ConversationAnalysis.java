package com.backend360.ai_call_center.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "conversation_analysis")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConversationAnalysis {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "call_id", nullable = false)
    private String callId;

    @Column(name = "phone_number", nullable = false)
    private String phoneNumber;

    @Column(name = "customer_name")
    private String customerName;

    // Form Bilgileri
    @Column(name = "original_campaign_name")
    private String originalCampaignName;

    @Column(name = "original_brand_name")
    private String originalBrandName;

    @Column(name = "original_campaign_category")
    private String originalCampaignCategory;

    @Column(name = "form_submission_date")
    private LocalDateTime formSubmissionDate;

    // Konuşma Detayları
    @Column(name = "conversation_duration_seconds")
    private Integer conversationDurationSeconds;

    @Column(name = "total_turns")
    private Integer totalTurns; // Kaç kez konuştular

    @Column(name = "customer_talk_time_seconds")
    private Integer customerTalkTimeSeconds;

    @Column(name = "agent_talk_time_seconds")
    private Integer agentTalkTimeSeconds;

    // Ana Kampanya Tepkisi
    @Column(name = "original_campaign_interest")
    private String originalCampaignInterest; // HIGH, MEDIUM, LOW, NONE

    @Column(name = "original_campaign_feedback", columnDefinition = "TEXT")
    private String originalCampaignFeedback;

    @Column(name = "wants_test_drive")
    private Boolean wantsTestDrive = false;

    @Column(name = "wants_detailed_info")
    private Boolean wantsDetailedInfo = false;

    @Column(name = "scheduled_callback")
    private Boolean scheduledCallback = false;

    @Column(name = "callback_preferred_time")
    private String callbackPreferredTime;

    // Diğer Kampanya İlgileri
    @Column(name = "mentioned_other_campaigns", columnDefinition = "TEXT")
    private String mentionedOtherCampaigns; // JSON array

    @Column(name = "interested_other_brands", columnDefinition = "TEXT")
    private String interestedOtherBrands; // JSON array

    @Column(name = "interested_categories", columnDefinition = "TEXT")
    private String interestedCategories; // JSON array

    // Müşteri Profili
    @Column(name = "customer_age_range")
    private String customerAgeRange; // 18-25, 26-35, 36-45, 46-55, 55+

    @Column(name = "customer_income_level")
    private String customerIncomeLevel; // LOW, MEDIUM, HIGH, PREMIUM

    @Column(name = "customer_lifestyle", columnDefinition = "TEXT")
    private String customerLifestyle; // JSON array: family, business, sports, luxury

    @Column(name = "customer_preferences", columnDefinition = "TEXT")
    private String customerPreferences; // JSON object

    // Konuşma Analizi
    @Column(name = "conversation_sentiment")
    private String conversationSentiment; // POSITIVE, NEGATIVE, NEUTRAL, MIXED

    @Column(name = "customer_mood")
    private String customerMood; // ENTHUSIASTIC, INTERESTED, SKEPTICAL, ANNOYED, NEUTRAL

    @Column(name = "objections_raised", columnDefinition = "TEXT")
    private String objectionsRaised; // JSON array

    @Column(name = "questions_asked", columnDefinition = "TEXT")
    private String questionsAsked; // JSON array

    @Column(name = "concerns_mentioned", columnDefinition = "TEXT")
    private String concernsMentioned; // JSON array

    // AI Önerileri
    @Column(name = "recommended_campaigns", columnDefinition = "TEXT")
    private String recommendedCampaigns; // JSON array

    @Column(name = "recommended_brands", columnDefinition = "TEXT")
    private String recommendedBrands; // JSON array

    @Column(name = "next_best_action")
    private String nextBestAction; // IMMEDIATE_CALLBACK, SEND_BROCHURE, SCHEDULE_MEETING, NO_ACTION

    @Column(name = "follow_up_priority")
    private String followUpPriority; // HIGH, MEDIUM, LOW

    @Column(name = "estimated_conversion_probability")
    private Double estimatedConversionProbability; // 0.0 - 1.0

    // Konuşma Metni ve Özeti
    @Column(name = "full_transcript", columnDefinition = "TEXT")
    private String fullTranscript;

    @Column(name = "conversation_summary", columnDefinition = "TEXT")
    private String conversationSummary;

    @Column(name = "key_quotes", columnDefinition = "TEXT")
    private String keyQuotes; // JSON array

    @Column(name = "action_items", columnDefinition = "TEXT")
    private String actionItems; // JSON array

    // Teknik Detaylar
    @Column(name = "audio_quality_score")
    private Double audioQualityScore; // 0.0 - 1.0

    @Column(name = "speech_clarity_score")
    private Double speechClarityScore; // 0.0 - 1.0

    @Column(name = "ai_confidence_score")
    private Double aiConfidenceScore; // 0.0 - 1.0

    @Column(name = "analysis_version")
    private String analysisVersion = "1.0";

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // Helper Methods
    public boolean isHighValueProspect() {
        return "HIGH".equals(followUpPriority) && 
               estimatedConversionProbability != null && 
               estimatedConversionProbability > 0.7;
    }

    public boolean hasMultipleBrandInterest() {
        return interestedOtherBrands != null && 
               interestedOtherBrands.contains(",");
    }

    public boolean needsImmediateFollowUp() {
        return "IMMEDIATE_CALLBACK".equals(nextBestAction) || 
               "SCHEDULE_MEETING".equals(nextBestAction);
    }

    public String getCustomerSegment() {
        if ("HIGH".equals(customerIncomeLevel) && "ENTHUSIASTIC".equals(customerMood)) {
            return "PREMIUM_PROSPECT";
        } else if ("MEDIUM".equals(customerIncomeLevel) && wantsDetailedInfo) {
            return "INFORMED_BUYER";
        } else if (wantsTestDrive) {
            return "ACTIVE_SHOPPER";
        } else if ("SKEPTICAL".equals(customerMood)) {
            return "NEEDS_NURTURING";
        } else {
            return "STANDARD_PROSPECT";
        }
    }
}
