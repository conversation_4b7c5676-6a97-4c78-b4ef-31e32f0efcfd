package com.backend360.ai_call_center.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

@Entity
@Table(name = "call_reports")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CallReport {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "call_id", unique = true, nullable = false)
    private String callId;

    @Column(name = "phone_number", nullable = false)
    private String phoneNumber;

    @Column(name = "customer_name")
    private String customerName;

    @Column(name = "campaign_name")
    private String campaignName;

    @Column(name = "brand_name")
    private String brandName;

    @Column(name = "campaign_category")
    private String campaignCategory;

    @Column(name = "call_status")
    private String callStatus; // INITIATED, IN_PROGRESS, COMPLETED, FAILED

    @Column(name = "call_result")
    private String callResult; // SUCCESS, NO_ANSWER, BUSY, FAILED

    @Column(name = "call_duration_seconds")
    private Integer callDurationSeconds;

    @Column(name = "start_time")
    private LocalDateTime startTime;

    @Column(name = "end_time")
    private LocalDateTime endTime;

    // AI Analysis Results
    @Column(name = "sentiment")
    private String sentiment; // POSITIVE, NEGATIVE, NEUTRAL

    @Column(name = "interest_level")
    private String interestLevel; // HIGH, MEDIUM, LOW

    @Column(name = "next_action")
    private String nextAction; // FOLLOW_UP, SCHEDULE_MEETING, NO_ACTION

    @Column(name = "wants_test_drive")
    private Boolean wantsTestDrive = false;

    @Column(name = "wants_detailed_info")
    private Boolean wantsDetailedInfo = false;

    @Column(name = "campaign_interest")
    private Boolean campaignInterest = false;

    // Conversation Data
    @Column(name = "conversation_transcript", columnDefinition = "TEXT")
    private String conversationTranscript;

    @Column(name = "ai_summary", columnDefinition = "TEXT")
    private String aiSummary;

    @Column(name = "keywords")
    private String keywords; // JSON array as string

    @Column(name = "audio_url")
    private String audioUrl;

    // Technical Details
    @Column(name = "agent_id")
    private String agentId;

    @Column(name = "sip_gateway")
    private String sipGateway;

    @Column(name = "caller_id")
    private String callerId;

    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    // Timestamps
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // Helper methods
    public boolean isSuccessful() {
        return "SUCCESS".equals(callResult) && "COMPLETED".equals(callStatus);
    }

    public boolean hasPositiveOutcome() {
        return wantsTestDrive || wantsDetailedInfo || campaignInterest;
    }

    public String getOutcomeSummary() {
        if (!isSuccessful()) {
            return "Call Failed: " + (errorMessage != null ? errorMessage : callResult);
        }

        StringBuilder outcome = new StringBuilder();
        if (campaignInterest) outcome.append("Campaign Interest, ");
        if (wantsTestDrive) outcome.append("Test Drive Request, ");
        if (wantsDetailedInfo) outcome.append("Detailed Info Request, ");

        String result = outcome.toString();
        return result.isEmpty() ? "No Interest" : result.substring(0, result.length() - 2);
    }
}
