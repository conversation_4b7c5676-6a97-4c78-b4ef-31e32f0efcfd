package com.backend360.ai_call_center.service;

import com.backend360.ai_call_center.client.CampaignServiceClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class SmartCampaignService {

    private final CampaignServiceClient campaignServiceClient;
    private final AiApiService aiApiService;

    /**
     * Müşteri profiline göre akıllı kampanya önerisi
     */
    public List<Map<String, Object>> getSmartRecommendations(String customerProfile, String currentCampaign, 
                                                            String customerMood, String interestLevel, 
                                                            String customerCategory) {
        log.info("Akıllı kampanya önerisi oluşturuluyor - Profil: {}, Mood: {}, İlgi: {}", 
                customerProfile, customerMood, interestLevel);

        try {
            // 1. Campaign Service'ten aktif kampanyaları al
            List<Map<String, Object>> allCampaigns = campaignServiceClient.getActiveCampaigns();
            
            // 2. Müşteri kategorisine uygun kampanyaları filtrele
            List<Map<String, Object>> relevantCampaigns = filterCampaignsByCategory(allCampaigns, customerCategory);
            
            // 3. AI ile en uygun kampanyaları seç
            List<Map<String, Object>> aiRecommendations = getAiBasedRecommendations(
                relevantCampaigns, customerProfile, currentCampaign, customerMood, interestLevel);
            
            // 4. Skorlama ve sıralama
            return scoreAndRankCampaigns(aiRecommendations, customerProfile, customerMood, interestLevel);
            
        } catch (Exception e) {
            log.error("Smart recommendations oluşturulurken hata oluştu", e);
            return getFallbackRecommendations(customerCategory);
        }
    }

    /**
     * Konuşma sırasında dinamik kampanya önerisi
     */
    public String generateCampaignSuggestionScript(String customerResponse, String currentCampaign, 
                                                  String customerProfile, String conversationContext) {
        log.info("Dinamik kampanya önerisi script'i oluşturuluyor");

        try {
            // Müşteri cevabından ilgi alanlarını çıkar
            String detectedInterests = extractCustomerInterests(customerResponse);
            
            // İlgi alanına uygun kampanyaları bul
            List<Map<String, Object>> matchingCampaigns = findMatchingCampaigns(detectedInterests, customerProfile);
            
            if (matchingCampaigns.isEmpty()) {
                return generateGenericFollowUpScript(customerResponse);
            }
            
            // En uygun kampanyayı seç
            Map<String, Object> bestCampaign = matchingCampaigns.get(0);
            
            // AI ile doğal konuşma script'i oluştur
            return generateNaturalCampaignScript(bestCampaign, customerResponse, conversationContext);
            
        } catch (Exception e) {
            log.error("Campaign suggestion script oluşturulurken hata oluştu", e);
            return "Bu konuda size daha detaylı bilgi verebilirim. Başka hangi konularda bilgi almak istersiniz?";
        }
    }

    /**
     * Müşteri kategorisine göre kampanya filtreleme
     */
    private List<Map<String, Object>> filterCampaignsByCategory(List<Map<String, Object>> campaigns, String category) {
        if (category == null || campaigns == null) {
            return campaigns != null ? campaigns : new ArrayList<>();
        }

        return campaigns.stream()
                .filter(campaign -> {
                    String campaignCategory = (String) campaign.get("category");
                    return campaignCategory != null && 
                           (campaignCategory.equalsIgnoreCase(category) || 
                            "GENEL".equalsIgnoreCase(campaignCategory));
                })
                .collect(Collectors.toList());
    }

    /**
     * AI tabanlı kampanya önerileri
     */
    private List<Map<String, Object>> getAiBasedRecommendations(List<Map<String, Object>> campaigns, 
                                                               String customerProfile, String currentCampaign,
                                                               String customerMood, String interestLevel) {
        try {
            String campaignList = campaigns.stream()
                    .map(c -> String.format("- %s (%s): %s", 
                            c.get("name"), c.get("brandName"), c.get("description")))
                    .collect(Collectors.joining("\n"));

            String aiPrompt = String.format(
                "Müşteri Profili: %s\n" +
                "Mevcut Kampanya: %s\n" +
                "Müşteri Ruh Hali: %s\n" +
                "İlgi Seviyesi: %s\n\n" +
                "Mevcut Kampanyalar:\n%s\n\n" +
                "Bu müşteriye en uygun 3 kampanyayı seç ve neden uygun olduğunu açıkla. " +
                "JSON formatında yanıtla: {\"recommendations\": [{\"campaignName\": \"\", \"reason\": \"\", \"priority\": 1}]}",
                customerProfile, currentCampaign, customerMood, interestLevel, campaignList
            );

            String aiResponse = aiApiService.generateAiResponse(aiPrompt, "", "Kampanya Önerisi");
            
            // AI cevabını parse et ve kampanya detaylarıyla birleştir
            return parseAiRecommendations(aiResponse, campaigns);
            
        } catch (Exception e) {
            log.error("AI based recommendations alınırken hata oluştu", e);
            return campaigns.stream().limit(3).collect(Collectors.toList());
        }
    }

    /**
     * Müşteri cevabından ilgi alanlarını çıkar
     */
    private String extractCustomerInterests(String customerResponse) {
        String lowerResponse = customerResponse.toLowerCase();
        List<String> interests = new ArrayList<>();

        // Otomotiv ilgileri
        if (lowerResponse.contains("araba") || lowerResponse.contains("otomobil") || 
            lowerResponse.contains("araç") || lowerResponse.contains("sürücü")) {
            interests.add("OTOMOTİV");
        }

        // Sigorta ilgileri
        if (lowerResponse.contains("sigorta") || lowerResponse.contains("güvence") || 
            lowerResponse.contains("koruma")) {
            interests.add("SİGORTA");
        }

        // Yatırım ilgileri
        if (lowerResponse.contains("yatırım") || lowerResponse.contains("para") || 
            lowerResponse.contains("kazanç") || lowerResponse.contains("getiri")) {
            interests.add("YATIRIM");
        }

        // Marka ilgileri
        if (lowerResponse.contains("bmw") || lowerResponse.contains("mercedes") || 
            lowerResponse.contains("audi") || lowerResponse.contains("volkswagen")) {
            interests.add("PREMIUM_OTOMOTIV");
        }

        return String.join(",", interests);
    }

    /**
     * İlgi alanına uygun kampanyaları bul
     */
    private List<Map<String, Object>> findMatchingCampaigns(String interests, String customerProfile) {
        if (interests.isEmpty()) {
            return campaignServiceClient.getPopularCampaigns(3);
        }

        List<Map<String, Object>> matchingCampaigns = new ArrayList<>();
        String[] interestArray = interests.split(",");

        for (String interest : interestArray) {
            List<Map<String, Object>> categoryCampaigns = campaignServiceClient.getCampaignsByCategory(interest.trim());
            matchingCampaigns.addAll(categoryCampaigns);
        }

        return matchingCampaigns.stream()
                .distinct()
                .limit(5)
                .collect(Collectors.toList());
    }

    /**
     * Doğal kampanya script'i oluştur
     */
    private String generateNaturalCampaignScript(Map<String, Object> campaign, String customerResponse, String context) {
        try {
            String scriptPrompt = String.format(
                "Müşteri şöyle dedi: '%s'\n" +
                "Önerilecek kampanya: %s (%s)\n" +
                "Kampanya açıklaması: %s\n" +
                "Konuşma bağlamı: %s\n\n" +
                "Bu kampanyayı müşteriye doğal bir şekilde öner. Sinem gibi samimi ve profesyonel ol. " +
                "Maksimum 2-3 cümle kullan.",
                customerResponse, campaign.get("name"), campaign.get("brandName"), 
                campaign.get("description"), context
            );

            return aiApiService.generateAiResponse(scriptPrompt, context, "Kampanya Önerisi");
            
        } catch (Exception e) {
            log.error("Natural campaign script oluşturulurken hata oluştu", e);
            return String.format("Bu arada, %s %s kampanyamız da size uygun olabilir. %s", 
                    campaign.get("brandName"), campaign.get("name"), campaign.get("description"));
        }
    }

    /**
     * Kampanyaları skorla ve sırala
     */
    private List<Map<String, Object>> scoreAndRankCampaigns(List<Map<String, Object>> campaigns, 
                                                           String customerProfile, String customerMood, String interestLevel) {
        return campaigns.stream()
                .map(campaign -> {
                    double score = calculateCampaignScore(campaign, customerProfile, customerMood, interestLevel);
                    campaign.put("recommendationScore", score);
                    return campaign;
                })
                .sorted((c1, c2) -> Double.compare(
                        (Double) c2.get("recommendationScore"), 
                        (Double) c1.get("recommendationScore")))
                .limit(3)
                .collect(Collectors.toList());
    }

    /**
     * Kampanya skoru hesapla
     */
    private double calculateCampaignScore(Map<String, Object> campaign, String customerProfile, 
                                        String customerMood, String interestLevel) {
        double score = 0.5; // Base score

        // Interest level bonus
        switch (interestLevel) {
            case "HIGH": score += 0.3; break;
            case "MEDIUM": score += 0.2; break;
            case "LOW": score += 0.1; break;
        }

        // Mood bonus
        switch (customerMood) {
            case "ENTHUSIASTIC": score += 0.2; break;
            case "INTERESTED": score += 0.15; break;
            case "NEUTRAL": score += 0.1; break;
            case "SKEPTICAL": score -= 0.1; break;
        }

        // Campaign category match
        String campaignCategory = (String) campaign.get("category");
        if (customerProfile != null && customerProfile.contains(campaignCategory)) {
            score += 0.2;
        }

        return Math.min(1.0, Math.max(0.0, score));
    }

    private List<Map<String, Object>> parseAiRecommendations(String aiResponse, List<Map<String, Object>> campaigns) {
        // AI response'u parse et ve kampanya detaylarıyla eşleştir
        // Şimdilik basit implementation
        return campaigns.stream().limit(3).collect(Collectors.toList());
    }

    private String generateGenericFollowUpScript(String customerResponse) {
        return "Anlıyorum. Size uygun başka kampanyalarımız da var. Hangi konularda bilgi almak istersiniz?";
    }

    private List<Map<String, Object>> getFallbackRecommendations(String category) {
        Map<String, Object> fallbackCampaign = new HashMap<>();
        fallbackCampaign.put("name", "Özel Fırsat Kampanyası");
        fallbackCampaign.put("brandName", "360 Avantajlı");
        fallbackCampaign.put("category", category != null ? category : "GENEL");
        fallbackCampaign.put("description", "Size özel hazırlanmış avantajlı fırsat");
        fallbackCampaign.put("recommendationScore", 0.7);
        
        return List.of(fallbackCampaign);
    }
}
