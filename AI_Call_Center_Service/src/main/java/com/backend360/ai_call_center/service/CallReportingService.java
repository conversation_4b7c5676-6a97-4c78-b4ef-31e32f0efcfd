package com.backend360.ai_call_center.service;

import com.backend360.ai_call_center.entity.CallReport;
import com.backend360.ai_call_center.entity.CampaignReport;
import com.backend360.ai_call_center.repository.CallReportRepository;
import com.backend360.ai_call_center.repository.CampaignReportRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class CallReportingService {

    private final CallReportRepository callReportRepository;
    private final CampaignReportRepository campaignReportRepository;

    /**
     * Call tamamlandığında rapor oluştur
     */
    @Transactional
    public CallReport createCallReport(String callId, String phoneNumber, String customerName,
                                     String campaignName, String brandName, String campaignCategory,
                                     String callStatus, String callResult, Integer duration,
                                     LocalDateTime startTime, LocalDateTime endTime,
                                     String sentiment, String interestLevel, String nextAction,
                                     boolean wantsTestDrive, boolean wantsDetailedInfo, boolean campaignInterest,
                                     String transcript, String summary, String keywords, String audioUrl,
                                     String agentId, String errorMessage) {
        
        log.info("Call raporu oluşturuluyor: {}", callId);

        CallReport report = CallReport.builder()
                .callId(callId)
                .phoneNumber(phoneNumber)
                .customerName(customerName)
                .campaignName(campaignName)
                .brandName(brandName)
                .campaignCategory(campaignCategory)
                .callStatus(callStatus)
                .callResult(callResult)
                .callDurationSeconds(duration)
                .startTime(startTime)
                .endTime(endTime)
                .sentiment(sentiment)
                .interestLevel(interestLevel)
                .nextAction(nextAction)
                .wantsTestDrive(wantsTestDrive)
                .wantsDetailedInfo(wantsDetailedInfo)
                .campaignInterest(campaignInterest)
                .conversationTranscript(transcript)
                .aiSummary(summary)
                .keywords(keywords)
                .audioUrl(audioUrl)
                .agentId(agentId)
                .sipGateway("sip.netgsm.com.tr")
                .callerId("2129091697")
                .errorMessage(errorMessage)
                .build();

        CallReport savedReport = callReportRepository.save(report);
        
        // Campaign istatistiklerini güncelle
        updateCampaignStatistics(campaignName, brandName, campaignCategory, callResult, sentiment, 
                                duration, campaignInterest, wantsTestDrive, wantsDetailedInfo);

        log.info("Call raporu kaydedildi: {}", savedReport.getId());
        return savedReport;
    }

    /**
     * Campaign istatistiklerini güncelle
     */
    @Transactional
    public void updateCampaignStatistics(String campaignName, String brandName, String campaignCategory,
                                       String callResult, String sentiment, Integer duration,
                                       boolean hasInterest, boolean wantsTestDrive, boolean wantsDetailedInfo) {
        
        Optional<CampaignReport> existingReport = campaignReportRepository.findByCampaignName(campaignName);
        
        CampaignReport campaignReport;
        if (existingReport.isPresent()) {
            campaignReport = existingReport.get();
        } else {
            campaignReport = CampaignReport.builder()
                    .campaignId(java.util.UUID.randomUUID().toString())
                    .campaignName(campaignName)
                    .brandName(brandName)
                    .campaignCategory(campaignCategory)
                    .campaignStatus("ACTIVE")
                    .campaignStartDate(LocalDateTime.now())
                    .build();
        }

        campaignReport.incrementCallCount(callResult, sentiment, duration, hasInterest, wantsTestDrive, wantsDetailedInfo);
        campaignReportRepository.save(campaignReport);
        
        log.info("Campaign istatistikleri güncellendi: {}", campaignName);
    }

    /**
     * Genel dashboard istatistikleri
     */
    public Map<String, Object> getDashboardStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // Genel call istatistikleri
        Long totalCalls = callReportRepository.count();
        Long successfulCalls = callReportRepository.countSuccessfulCalls();
        Long todaysCalls = callReportRepository.countTodaysCalls();
        Long todaysSuccessfulCalls = callReportRepository.countTodaysSuccessfulCalls();
        
        stats.put("totalCalls", totalCalls);
        stats.put("successfulCalls", successfulCalls);
        stats.put("todaysCalls", todaysCalls);
        stats.put("todaysSuccessfulCalls", todaysSuccessfulCalls);
        stats.put("successRate", totalCalls > 0 ? (double) successfulCalls / totalCalls * 100 : 0.0);
        stats.put("todaysSuccessRate", todaysCalls > 0 ? (double) todaysSuccessfulCalls / todaysCalls * 100 : 0.0);
        
        // Outcome istatistikleri
        stats.put("testDriveRequests", callReportRepository.countTestDriveRequests());
        stats.put("detailedInfoRequests", callReportRepository.countDetailedInfoRequests());
        stats.put("campaignInterests", callReportRepository.countCampaignInterests());
        
        // Sentiment istatistikleri
        stats.put("positiveSentiment", callReportRepository.countBySentiment("POSITIVE"));
        stats.put("negativeSentiment", callReportRepository.countBySentiment("NEGATIVE"));
        stats.put("neutralSentiment", callReportRepository.countBySentiment("NEUTRAL"));
        
        // Ortalama call süresi
        stats.put("averageCallDuration", callReportRepository.getAverageCallDuration());
        
        // Aktif campaign sayısı
        stats.put("activeCampaigns", campaignReportRepository.findActiveCampaigns().size());
        
        return stats;
    }

    /**
     * Campaign performans raporu
     */
    public Map<String, Object> getCampaignPerformanceReport(String campaignName) {
        Optional<CampaignReport> campaignReport = campaignReportRepository.findByCampaignName(campaignName);
        
        if (campaignReport.isEmpty()) {
            return Map.of("error", "Campaign not found: " + campaignName);
        }
        
        CampaignReport report = campaignReport.get();
        List<CallReport> callReports = callReportRepository.findByCampaignNameOrderByCreatedAtDesc(campaignName);
        
        Map<String, Object> performance = new HashMap<>();
        performance.put("campaignInfo", report);
        performance.put("recentCalls", callReports.stream().limit(10).toList());
        performance.put("totalCalls", report.getTotalCalls());
        performance.put("successRate", report.getSuccessRate());
        performance.put("conversionRate", report.getConversionRate());
        performance.put("averageDuration", report.getAverageCallDurationSeconds());
        
        return performance;
    }

    /**
     * Saatlik call dağılımı (bugün)
     */
    public List<Object[]> getTodaysHourlyDistribution() {
        return callReportRepository.getTodaysHourlyCallDistribution();
    }

    /**
     * En iyi performans gösteren kampanyalar
     */
    public List<CampaignReport> getTopPerformingCampaigns(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return campaignReportRepository.findBySuccessRateDesc(pageable).getContent();
    }

    /**
     * Son call'lar
     */
    public List<CallReport> getRecentCalls(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return callReportRepository.findRecentCalls(pageable).getContent();
    }

    /**
     * Tarih aralığına göre call'lar
     */
    public List<CallReport> getCallsByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        return callReportRepository.findByDateRange(startDate, endDate);
    }

    /**
     * Retry edilecek failed call'lar
     */
    public List<CallReport> getFailedCallsForRetry(int hoursBack) {
        LocalDateTime since = LocalDateTime.now().minusHours(hoursBack);
        return callReportRepository.findFailedCallsForRetry(since);
    }

    /**
     * Call detayını getir
     */
    public Optional<CallReport> getCallReport(String callId) {
        return callReportRepository.findByCallId(callId);
    }

    /**
     * Campaign raporu güncelle
     */
    @Transactional
    public void updateCampaignStatus(String campaignName, String status) {
        Optional<CampaignReport> report = campaignReportRepository.findByCampaignName(campaignName);
        if (report.isPresent()) {
            CampaignReport campaignReport = report.get();
            campaignReport.setCampaignStatus(status);
            if ("COMPLETED".equals(status)) {
                campaignReport.setCampaignEndDate(LocalDateTime.now());
            }
            campaignReportRepository.save(campaignReport);
            log.info("Campaign status güncellendi: {} -> {}", campaignName, status);
        }
    }
}
