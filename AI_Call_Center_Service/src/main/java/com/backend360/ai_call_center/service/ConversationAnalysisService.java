package com.backend360.ai_call_center.service;

import com.backend360.ai_call_center.entity.ConversationAnalysis;
import com.backend360.ai_call_center.entity.CustomerInterest;
import com.backend360.ai_call_center.repository.ConversationAnalysisRepository;
import com.backend360.ai_call_center.repository.CustomerInterestRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@RequiredArgsConstructor
@Slf4j
public class ConversationAnalysisService {

    private final ConversationAnalysisRepository conversationAnalysisRepository;
    private final CustomerInterestRepository customerInterestRepository;
    private final AiApiService aiApiService;
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Konuşma tamamlandığında detaylı analiz yap
     */
    @Transactional
    public ConversationAnalysis analyzeConversation(String callId, String phoneNumber, String customerName,
                                                   String originalCampaignName, String originalBrandName, String originalCampaignCategory,
                                                   String fullTranscript, Integer conversationDuration, Integer totalTurns) {
        
        log.info("Detaylı konuşma analizi başlatılıyor: {}", callId);

        try {
            // AI ile detaylı analiz yap
            Map<String, Object> aiAnalysis = performAiAnalysis(fullTranscript, originalCampaignName, originalBrandName);
            
            // Konuşma analizi entity'si oluştur
            ConversationAnalysis analysis = ConversationAnalysis.builder()
                    .callId(callId)
                    .phoneNumber(phoneNumber)
                    .customerName(customerName)
                    .originalCampaignName(originalCampaignName)
                    .originalBrandName(originalBrandName)
                    .originalCampaignCategory(originalCampaignCategory)
                    .formSubmissionDate(LocalDateTime.now().minusMinutes(30)) // Tahmini form gönderim zamanı
                    .conversationDurationSeconds(conversationDuration)
                    .totalTurns(totalTurns)
                    .fullTranscript(fullTranscript)
                    .build();

            // AI analiz sonuçlarını entity'ye aktar
            populateAnalysisFromAi(analysis, aiAnalysis);
            
            // Konuşma metninden ek bilgiler çıkar
            extractAdditionalInsights(analysis, fullTranscript);
            
            // Analizi kaydet
            ConversationAnalysis savedAnalysis = conversationAnalysisRepository.save(analysis);
            
            // Müşteri ilgilerini ayrı ayrı kaydet
            saveCustomerInterests(savedAnalysis, aiAnalysis);
            
            log.info("Konuşma analizi tamamlandı: {}", savedAnalysis.getId());
            return savedAnalysis;
            
        } catch (Exception e) {
            log.error("Konuşma analizi sırasında hata oluştu: {}", callId, e);
            throw new RuntimeException("Conversation analysis failed", e);
        }
    }

    /**
     * AI ile detaylı konuşma analizi
     */
    private Map<String, Object> performAiAnalysis(String transcript, String campaignName, String brandName) {
        String analysisPrompt = String.format(
            "Aşağıdaki telefon konuşmasını detaylı analiz et. Orijinal kampanya: %s (%s)\n\n" +
            "Konuşma:\n%s\n\n" +
            "Lütfen şu bilgileri çıkar:\n" +
            "1. Müşterinin orijinal kampanyaya ilgi seviyesi (HIGH/MEDIUM/LOW/NONE)\n" +
            "2. Bahsedilen diğer markalar ve kampanyalar\n" +
            "3. Müşterinin yaş aralığı ve gelir seviyesi tahmini\n" +
            "4. Müşterinin ruh hali (ENTHUSIASTIC/INTERESTED/SKEPTICAL/ANNOYED/NEUTRAL)\n" +
            "5. Sorulan sorular ve endişeler\n" +
            "6. Test sürüşü, detaylı bilgi, geri arama talepleri\n" +
            "7. Bütçe ve satın alma zaman çizelgesi\n" +
            "8. Rakip markalarla karşılaştırma\n" +
            "9. Dönüşüm olasılığı (0.0-1.0)\n" +
            "10. Önerilen sonraki aksiyonlar\n" +
            "JSON formatında yanıtla.",
            campaignName, brandName, transcript
        );

        try {
            String aiResponse = aiApiService.generateAiResponse(analysisPrompt, "", "Konuşma Analizi");
            return parseAiAnalysisResponse(aiResponse);
        } catch (Exception e) {
            log.error("AI analizi başarısız, fallback kullanılıyor", e);
            return createFallbackAnalysis(transcript);
        }
    }

    /**
     * AI cevabını parse et
     */
    private Map<String, Object> parseAiAnalysisResponse(String aiResponse) {
        try {
            // JSON kısmını bul ve parse et
            String jsonPart = extractJsonFromResponse(aiResponse);
            return objectMapper.readValue(jsonPart, Map.class);
        } catch (Exception e) {
            log.error("AI response parse edilemedi", e);
            return createFallbackAnalysis("");
        }
    }

    /**
     * Response'dan JSON kısmını çıkar
     */
    private String extractJsonFromResponse(String response) {
        Pattern jsonPattern = Pattern.compile("\\{.*\\}", Pattern.DOTALL);
        Matcher matcher = jsonPattern.matcher(response);
        if (matcher.find()) {
            return matcher.group();
        }
        return "{}";
    }

    /**
     * AI analiz sonuçlarını entity'ye aktar
     */
    private void populateAnalysisFromAi(ConversationAnalysis analysis, Map<String, Object> aiAnalysis) {
        analysis.setOriginalCampaignInterest(getStringValue(aiAnalysis, "originalCampaignInterest", "MEDIUM"));
        analysis.setCustomerMood(getStringValue(aiAnalysis, "customerMood", "NEUTRAL"));
        analysis.setCustomerAgeRange(getStringValue(aiAnalysis, "ageRange", "26-45"));
        analysis.setCustomerIncomeLevel(getStringValue(aiAnalysis, "incomeLevel", "MEDIUM"));
        analysis.setConversationSentiment(getStringValue(aiAnalysis, "sentiment", "NEUTRAL"));
        analysis.setNextBestAction(getStringValue(aiAnalysis, "nextBestAction", "FOLLOW_UP"));
        analysis.setFollowUpPriority(getStringValue(aiAnalysis, "followUpPriority", "MEDIUM"));
        analysis.setEstimatedConversionProbability(getDoubleValue(aiAnalysis, "conversionProbability", 0.5));
        
        // Boolean değerler
        analysis.setWantsTestDrive(getBooleanValue(aiAnalysis, "wantsTestDrive", false));
        analysis.setWantsDetailedInfo(getBooleanValue(aiAnalysis, "wantsDetailedInfo", false));
        analysis.setScheduledCallback(getBooleanValue(aiAnalysis, "scheduledCallback", false));
        
        // JSON arrays
        try {
            analysis.setMentionedOtherCampaigns(objectMapper.writeValueAsString(aiAnalysis.get("mentionedCampaigns")));
            analysis.setInterestedOtherBrands(objectMapper.writeValueAsString(aiAnalysis.get("interestedBrands")));
            analysis.setObjectionsRaised(objectMapper.writeValueAsString(aiAnalysis.get("objections")));
            analysis.setQuestionsAsked(objectMapper.writeValueAsString(aiAnalysis.get("questions")));
            analysis.setRecommendedCampaigns(objectMapper.writeValueAsString(aiAnalysis.get("recommendedCampaigns")));
        } catch (JsonProcessingException e) {
            log.error("JSON serialization error", e);
        }
        
        // Özet ve anahtar cümleler
        analysis.setConversationSummary(getStringValue(aiAnalysis, "summary", "Konuşma özeti oluşturulamadı"));
        analysis.setAiConfidenceScore(getDoubleValue(aiAnalysis, "confidenceScore", 0.8));
    }

    /**
     * Konuşma metninden ek bilgiler çıkar
     */
    private void extractAdditionalInsights(ConversationAnalysis analysis, String transcript) {
        String lowerTranscript = transcript.toLowerCase();
        
        // Talk time hesaplama (basit tahmin)
        String[] turns = transcript.split("(Müşteri:|AI:|Sinem:)");
        int customerTurns = 0, agentTurns = 0;
        
        for (String turn : turns) {
            if (turn.contains("müşteri")) customerTurns++;
            else if (turn.contains("ai") || turn.contains("sinem")) agentTurns++;
        }
        
        analysis.setCustomerTalkTimeSeconds(customerTurns * 15); // Ortalama 15 saniye/turn
        analysis.setAgentTalkTimeSeconds(agentTurns * 20); // Ortalama 20 saniye/turn
        
        // Callback zamanı çıkarma
        if (lowerTranscript.contains("sabah") || lowerTranscript.contains("09") || lowerTranscript.contains("10")) {
            analysis.setCallbackPreferredTime("MORNING");
        } else if (lowerTranscript.contains("öğleden sonra") || lowerTranscript.contains("14") || lowerTranscript.contains("15")) {
            analysis.setCallbackPreferredTime("AFTERNOON");
        } else if (lowerTranscript.contains("akşam") || lowerTranscript.contains("18") || lowerTranscript.contains("19")) {
            analysis.setCallbackPreferredTime("EVENING");
        }
        
        // Audio quality simulation
        analysis.setAudioQualityScore(0.85 + (Math.random() * 0.15));
        analysis.setSpeechClarityScore(0.80 + (Math.random() * 0.20));
    }

    /**
     * Müşteri ilgilerini ayrı kayıtlar olarak kaydet
     */
    private void saveCustomerInterests(ConversationAnalysis analysis, Map<String, Object> aiAnalysis) {
        // Orijinal kampanya ilgisi
        saveOriginalCampaignInterest(analysis);
        
        // Bahsedilen diğer markalar
        saveMentionedBrandInterests(analysis, aiAnalysis);
        
        // AI önerilen kampanyalar
        saveRecommendedCampaignInterests(analysis, aiAnalysis);
    }

    private void saveOriginalCampaignInterest(ConversationAnalysis analysis) {
        CustomerInterest originalInterest = CustomerInterest.builder()
                .callId(analysis.getCallId())
                .phoneNumber(analysis.getPhoneNumber())
                .customerName(analysis.getCustomerName())
                .interestType("ORIGINAL_CAMPAIGN")
                .brandName(analysis.getOriginalBrandName())
                .campaignName(analysis.getOriginalCampaignName())
                .category(analysis.getOriginalCampaignCategory())
                .interestLevel(analysis.getOriginalCampaignInterest())
                .interestScore(convertInterestLevelToScore(analysis.getOriginalCampaignInterest()))
                .mentionedBy("AGENT")
                .customerReaction(analysis.getConversationSentiment())
                .requestedInfo(analysis.getWantsDetailedInfo())
                .requestedTestDrive(analysis.getWantsTestDrive())
                .requestedCallback(analysis.getScheduledCallback())
                .followUpRequired(!"NO_ACTION".equals(analysis.getNextBestAction()))
                .followUpPriority(analysis.getFollowUpPriority())
                .build();
        
        customerInterestRepository.save(originalInterest);
    }

    private void saveMentionedBrandInterests(ConversationAnalysis analysis, Map<String, Object> aiAnalysis) {
        try {
            List<String> mentionedBrands = (List<String>) aiAnalysis.get("interestedBrands");
            if (mentionedBrands != null) {
                for (String brand : mentionedBrands) {
                    CustomerInterest brandInterest = CustomerInterest.builder()
                            .callId(analysis.getCallId())
                            .phoneNumber(analysis.getPhoneNumber())
                            .customerName(analysis.getCustomerName())
                            .interestType("MENTIONED_BRAND")
                            .brandName(brand)
                            .category(analysis.getOriginalCampaignCategory())
                            .interestLevel("MEDIUM")
                            .interestScore(0.6)
                            .mentionedBy("CUSTOMER")
                            .customerReaction("POSITIVE")
                            .followUpRequired(true)
                            .followUpPriority("MEDIUM")
                            .build();
                    
                    customerInterestRepository.save(brandInterest);
                }
            }
        } catch (Exception e) {
            log.error("Mentioned brands kaydedilirken hata", e);
        }
    }

    private void saveRecommendedCampaignInterests(ConversationAnalysis analysis, Map<String, Object> aiAnalysis) {
        // AI önerilen kampanyalar için benzer logic
    }

    // Helper methods
    private String getStringValue(Map<String, Object> map, String key, String defaultValue) {
        Object value = map.get(key);
        return value != null ? value.toString() : defaultValue;
    }

    private Double getDoubleValue(Map<String, Object> map, String key, Double defaultValue) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return defaultValue;
    }

    private Boolean getBooleanValue(Map<String, Object> map, String key, Boolean defaultValue) {
        Object value = map.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        return defaultValue;
    }

    private Double convertInterestLevelToScore(String interestLevel) {
        switch (interestLevel) {
            case "HIGH": return 0.9;
            case "MEDIUM": return 0.6;
            case "LOW": return 0.3;
            default: return 0.1;
        }
    }

    private Map<String, Object> createFallbackAnalysis(String transcript) {
        Map<String, Object> fallback = new HashMap<>();
        fallback.put("originalCampaignInterest", "MEDIUM");
        fallback.put("customerMood", "NEUTRAL");
        fallback.put("sentiment", "NEUTRAL");
        fallback.put("conversionProbability", 0.5);
        fallback.put("nextBestAction", "FOLLOW_UP");
        fallback.put("followUpPriority", "MEDIUM");
        fallback.put("summary", "Otomatik analiz yapılamadı");
        return fallback;
    }

    /**
     * Müşteri journey'sini getir
     */
    public List<ConversationAnalysis> getCustomerJourney(String phoneNumber) {
        return conversationAnalysisRepository.findCustomerJourney(phoneNumber);
    }

    /**
     * Müşterinin tüm marka ilgilerini getir
     */
    public List<CustomerInterest> getCustomerInterests(String phoneNumber) {
        return customerInterestRepository.findByPhoneNumberOrderByCreatedAtDesc(phoneNumber);
    }
}
