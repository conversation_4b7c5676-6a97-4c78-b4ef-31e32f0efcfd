package com.backend360.ai_call_center.repository;

import com.backend360.ai_call_center.entity.CallReport;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface CallReportRepository extends JpaRepository<CallReport, Long> {

    Optional<CallReport> findByCallId(String callId);

    List<CallReport> findByCampaignNameOrderByCreatedAtDesc(String campaignName);

    List<CallReport> findByBrandNameOrderByCreatedAtDesc(String brandName);

    List<CallReport> findByCampaignCategoryOrderByCreatedAtDesc(String campaignCategory);

    Page<CallReport> findByCallStatusOrderByCreatedAtDesc(String callStatus, Pageable pageable);

    Page<CallReport> findByCallResultOrderByCreatedAtDesc(String callResult, Pageable pageable);

    // Date range queries
    @Query("SELECT cr FROM CallReport cr WHERE cr.createdAt BETWEEN :startDate AND :endDate ORDER BY cr.createdAt DESC")
    List<CallReport> findByDateRange(@Param("startDate") LocalDateTime startDate, 
                                   @Param("endDate") LocalDateTime endDate);

    @Query("SELECT cr FROM CallReport cr WHERE cr.campaignName = :campaignName AND cr.createdAt BETWEEN :startDate AND :endDate ORDER BY cr.createdAt DESC")
    List<CallReport> findByCampaignAndDateRange(@Param("campaignName") String campaignName,
                                              @Param("startDate") LocalDateTime startDate,
                                              @Param("endDate") LocalDateTime endDate);

    // Statistics queries
    @Query("SELECT COUNT(cr) FROM CallReport cr WHERE cr.callResult = 'SUCCESS'")
    Long countSuccessfulCalls();

    @Query("SELECT COUNT(cr) FROM CallReport cr WHERE cr.callResult = 'SUCCESS' AND cr.campaignName = :campaignName")
    Long countSuccessfulCallsByCampaign(@Param("campaignName") String campaignName);

    @Query("SELECT COUNT(cr) FROM CallReport cr WHERE cr.wantsTestDrive = true")
    Long countTestDriveRequests();

    @Query("SELECT COUNT(cr) FROM CallReport cr WHERE cr.wantsDetailedInfo = true")
    Long countDetailedInfoRequests();

    @Query("SELECT COUNT(cr) FROM CallReport cr WHERE cr.campaignInterest = true")
    Long countCampaignInterests();

    @Query("SELECT COUNT(cr) FROM CallReport cr WHERE cr.sentiment = :sentiment")
    Long countBySentiment(@Param("sentiment") String sentiment);

    // Today's statistics
    @Query("SELECT COUNT(cr) FROM CallReport cr WHERE DATE(cr.createdAt) = CURRENT_DATE")
    Long countTodaysCalls();

    @Query("SELECT COUNT(cr) FROM CallReport cr WHERE DATE(cr.createdAt) = CURRENT_DATE AND cr.callResult = 'SUCCESS'")
    Long countTodaysSuccessfulCalls();

    // Average call duration
    @Query("SELECT AVG(cr.callDurationSeconds) FROM CallReport cr WHERE cr.callResult = 'SUCCESS'")
    Double getAverageCallDuration();

    @Query("SELECT AVG(cr.callDurationSeconds) FROM CallReport cr WHERE cr.callResult = 'SUCCESS' AND cr.campaignName = :campaignName")
    Double getAverageCallDurationByCampaign(@Param("campaignName") String campaignName);

    // Top performing campaigns
    @Query("SELECT cr.campaignName, COUNT(cr) as callCount FROM CallReport cr WHERE cr.callResult = 'SUCCESS' GROUP BY cr.campaignName ORDER BY callCount DESC")
    List<Object[]> getTopPerformingCampaigns();

    // Hourly call distribution
    @Query("SELECT HOUR(cr.createdAt) as hour, COUNT(cr) as callCount FROM CallReport cr WHERE DATE(cr.createdAt) = CURRENT_DATE GROUP BY HOUR(cr.createdAt) ORDER BY hour")
    List<Object[]> getTodaysHourlyCallDistribution();

    // Recent calls
    @Query("SELECT cr FROM CallReport cr ORDER BY cr.createdAt DESC")
    Page<CallReport> findRecentCalls(Pageable pageable);

    // Failed calls for retry
    @Query("SELECT cr FROM CallReport cr WHERE cr.callResult IN ('NO_ANSWER', 'BUSY') AND cr.createdAt >= :since ORDER BY cr.createdAt DESC")
    List<CallReport> findFailedCallsForRetry(@Param("since") LocalDateTime since);
}
