package com.backend360.ai_call_center.repository;

import com.backend360.ai_call_center.entity.CustomerInterest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface CustomerInterestRepository extends JpaRepository<CustomerInterest, Long> {

    List<CustomerInterest> findByCallIdOrderByInterestScoreDesc(String callId);

    List<CustomerInterest> findByPhoneNumberOrderByCreatedAtDesc(String phoneNumber);

    List<CustomerInterest> findByCustomerNameOrderByCreatedAtDesc(String customerName);

    // Brand Interest Analysis
    List<CustomerInterest> findByBrandNameOrderByInterestScoreDesc(String brandName);

    List<CustomerInterest> findByCategoryOrderByInterestScoreDesc(String category);

    @Query("SELECT ci FROM CustomerInterest ci WHERE ci.brandName = :brandName AND ci.interestLevel = :level ORDER BY ci.createdAt DESC")
    List<CustomerInterest> findByBrandAndInterestLevel(@Param("brandName") String brandName, @Param("level") String level);

    // Lead Quality Queries
    @Query("SELECT ci FROM CustomerInterest ci WHERE ci.interestLevel = 'HIGH' AND ci.purchaseTimeline IN ('IMMEDIATE', '1_MONTH') ORDER BY ci.createdAt DESC")
    List<CustomerInterest> findHotLeads();

    @Query("SELECT ci FROM CustomerInterest ci WHERE ci.requestedTestDrive = true OR ci.scheduledMeeting = true ORDER BY ci.createdAt DESC")
    List<CustomerInterest> findActiveProspects();

    @Query("SELECT ci FROM CustomerInterest ci WHERE ci.followUpRequired = true AND ci.followUpPriority = 'HIGH' ORDER BY ci.createdAt DESC")
    List<CustomerInterest> findHighPriorityFollowUps();

    // Timeline Analysis
    @Query("SELECT ci FROM CustomerInterest ci WHERE ci.purchaseTimeline = :timeline ORDER BY ci.interestScore DESC")
    List<CustomerInterest> findByPurchaseTimeline(@Param("timeline") String timeline);

    @Query("SELECT ci.purchaseTimeline, COUNT(ci), AVG(ci.interestScore) FROM CustomerInterest ci GROUP BY ci.purchaseTimeline ORDER BY COUNT(ci) DESC")
    List<Object[]> getTimelineDistribution();

    // Budget Analysis
    @Query("SELECT ci FROM CustomerInterest ci WHERE ci.budgetMentioned = true ORDER BY ci.createdAt DESC")
    List<CustomerInterest> findWithBudgetInfo();

    @Query("SELECT ci.budgetRange, COUNT(ci) FROM CustomerInterest ci WHERE ci.budgetMentioned = true GROUP BY ci.budgetRange ORDER BY COUNT(ci) DESC")
    List<Object[]> getBudgetDistribution();

    // Brand Performance
    @Query("SELECT ci.brandName, COUNT(ci) as totalInterests, " +
           "SUM(CASE WHEN ci.interestLevel = 'HIGH' THEN 1 ELSE 0 END) as highInterests, " +
           "AVG(ci.interestScore) as avgScore " +
           "FROM CustomerInterest ci GROUP BY ci.brandName ORDER BY totalInterests DESC")
    List<Object[]> getBrandInterestStats();

    @Query("SELECT ci.category, COUNT(ci) as totalInterests, " +
           "SUM(CASE WHEN ci.interestLevel = 'HIGH' THEN 1 ELSE 0 END) as highInterests, " +
           "AVG(ci.interestScore) as avgScore " +
           "FROM CustomerInterest ci GROUP BY ci.category ORDER BY totalInterests DESC")
    List<Object[]> getCategoryInterestStats();

    // Competitor Analysis
    @Query("SELECT ci FROM CustomerInterest ci WHERE ci.competitorComparisonMade = true ORDER BY ci.createdAt DESC")
    List<CustomerInterest> findCompetitorComparisons();

    @Query("SELECT ci FROM CustomerInterest ci WHERE ci.competitorBrandsMentioned IS NOT NULL AND ci.competitorBrandsMentioned != '' ORDER BY ci.createdAt DESC")
    List<CustomerInterest> findWithCompetitorMentions();

    // Conversion Tracking
    @Query("SELECT ci FROM CustomerInterest ci WHERE ci.requestedTestDrive = true ORDER BY ci.createdAt DESC")
    List<CustomerInterest> findTestDriveRequests();

    @Query("SELECT ci FROM CustomerInterest ci WHERE ci.scheduledMeeting = true ORDER BY ci.meetingDate ASC")
    List<CustomerInterest> findScheduledMeetings();

    @Query("SELECT ci FROM CustomerInterest ci WHERE ci.requestedCallback = true ORDER BY ci.createdAt DESC")
    List<CustomerInterest> findCallbackRequests();

    // Statistics
    @Query("SELECT COUNT(ci) FROM CustomerInterest ci WHERE ci.interestLevel = 'HIGH'")
    Long countHighInterestLeads();

    @Query("SELECT COUNT(ci) FROM CustomerInterest ci WHERE ci.requestedTestDrive = true")
    Long countTestDriveRequests();

    @Query("SELECT COUNT(ci) FROM CustomerInterest ci WHERE ci.scheduledMeeting = true")
    Long countScheduledMeetings();

    @Query("SELECT AVG(ci.interestScore) FROM CustomerInterest ci WHERE ci.interestScore IS NOT NULL")
    Double getAverageInterestScore();

    // Time-based Analysis
    @Query("SELECT ci FROM CustomerInterest ci WHERE ci.createdAt BETWEEN :startDate AND :endDate ORDER BY ci.interestScore DESC")
    List<CustomerInterest> findByDateRange(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    @Query("SELECT DATE(ci.createdAt) as date, COUNT(ci) as count, AVG(ci.interestScore) as avgScore FROM CustomerInterest ci WHERE ci.createdAt >= :since GROUP BY DATE(ci.createdAt) ORDER BY date DESC")
    List<Object[]> getDailyInterestTrends(@Param("since") LocalDateTime since);

    // Customer Journey
    @Query("SELECT ci FROM CustomerInterest ci WHERE ci.phoneNumber = :phoneNumber ORDER BY ci.createdAt ASC")
    List<CustomerInterest> findCustomerInterestJourney(@Param("phoneNumber") String phoneNumber);

    // Multi-brand Customers
    @Query("SELECT ci.phoneNumber, ci.customerName, COUNT(DISTINCT ci.brandName) as brandCount FROM CustomerInterest ci GROUP BY ci.phoneNumber, ci.customerName HAVING COUNT(DISTINCT ci.brandName) > 1 ORDER BY brandCount DESC")
    List<Object[]> findMultiBrandCustomers();

    // Recent High-Value Leads
    @Query("SELECT ci FROM CustomerInterest ci WHERE ci.interestScore >= :minScore AND ci.createdAt >= :since ORDER BY ci.interestScore DESC")
    List<CustomerInterest> findRecentHighValueLeads(@Param("minScore") Double minScore, @Param("since") LocalDateTime since);

    // Follow-up Management
    @Query("SELECT ci FROM CustomerInterest ci WHERE ci.followUpRequired = true AND ci.assignedTo IS NULL ORDER BY ci.followUpPriority DESC, ci.createdAt ASC")
    List<CustomerInterest> findUnassignedFollowUps();

    @Query("SELECT ci FROM CustomerInterest ci WHERE ci.assignedTo = :assignee AND ci.followUpRequired = true ORDER BY ci.followUpPriority DESC")
    List<CustomerInterest> findFollowUpsByAssignee(@Param("assignee") String assignee);
}
