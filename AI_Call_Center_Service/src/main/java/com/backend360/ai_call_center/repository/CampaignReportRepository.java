package com.backend360.ai_call_center.repository;

import com.backend360.ai_call_center.entity.CampaignReport;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface CampaignReportRepository extends JpaRepository<CampaignReport, Long> {

    Optional<CampaignReport> findByCampaignId(String campaignId);

    Optional<CampaignReport> findByCampaignName(String campaignName);

    List<CampaignReport> findByBrandNameOrderByCreatedAtDesc(String brandName);

    List<CampaignReport> findByCampaignCategoryOrderByCreatedAtDesc(String campaignCategory);

    List<CampaignReport> findByCampaignStatusOrderByCreatedAtDesc(String campaignStatus);

    // Performance queries
    @Query("SELECT cr FROM CampaignReport cr ORDER BY cr.successRate DESC")
    Page<CampaignReport> findBySuccessRateDesc(Pageable pageable);

    @Query("SELECT cr FROM CampaignReport cr ORDER BY cr.conversionRate DESC")
    Page<CampaignReport> findByConversionRateDesc(Pageable pageable);

    @Query("SELECT cr FROM CampaignReport cr ORDER BY cr.totalCalls DESC")
    Page<CampaignReport> findByTotalCallsDesc(Pageable pageable);

    // Active campaigns
    @Query("SELECT cr FROM CampaignReport cr WHERE cr.campaignStatus = 'ACTIVE' ORDER BY cr.lastCallDate DESC")
    List<CampaignReport> findActiveCampaigns();

    // Recent campaigns
    @Query("SELECT cr FROM CampaignReport cr WHERE cr.lastCallDate >= :since ORDER BY cr.lastCallDate DESC")
    List<CampaignReport> findRecentCampaigns(@Param("since") LocalDateTime since);

    // Statistics
    @Query("SELECT SUM(cr.totalCalls) FROM CampaignReport cr")
    Long getTotalCallsAcrossAllCampaigns();

    @Query("SELECT SUM(cr.successfulCalls) FROM CampaignReport cr")
    Long getTotalSuccessfulCallsAcrossAllCampaigns();

    @Query("SELECT AVG(cr.successRate) FROM CampaignReport cr WHERE cr.totalCalls > 0")
    Double getAverageSuccessRateAcrossAllCampaigns();

    @Query("SELECT AVG(cr.conversionRate) FROM CampaignReport cr WHERE cr.totalCalls > 0")
    Double getAverageConversionRateAcrossAllCampaigns();

    // Top performers
    @Query("SELECT cr FROM CampaignReport cr WHERE cr.totalCalls >= :minCalls ORDER BY cr.successRate DESC")
    List<CampaignReport> findTopPerformingCampaigns(@Param("minCalls") Integer minCalls);

    @Query("SELECT cr FROM CampaignReport cr WHERE cr.totalCalls >= :minCalls ORDER BY cr.conversionRate DESC")
    List<CampaignReport> findHighestConvertingCampaigns(@Param("minCalls") Integer minCalls);

    // Category performance
    @Query("SELECT cr.campaignCategory, AVG(cr.successRate), AVG(cr.conversionRate), SUM(cr.totalCalls) FROM CampaignReport cr GROUP BY cr.campaignCategory")
    List<Object[]> getCategoryPerformanceStats();

    // Brand performance
    @Query("SELECT cr.brandName, AVG(cr.successRate), AVG(cr.conversionRate), SUM(cr.totalCalls) FROM CampaignReport cr GROUP BY cr.brandName")
    List<Object[]> getBrandPerformanceStats();
}
