package com.backend360.ai_call_center.client;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@RequiredArgsConstructor
@Slf4j
public class CampaignServiceClient {

    private final RestTemplate restTemplate = new RestTemplate();
    
    @Value("${campaign.service.url:http://campaign-service:9001}")
    private String campaignServiceUrl;

    /**
     * Aktif kampanyaları getir
     */
    public List<Map<String, Object>> getActiveCampaigns() {
        log.info("Campaign Service'ten aktif kampanyalar getiriliyor");
        
        try {
            ResponseEntity<List<Map<String, Object>>> response = restTemplate.exchange(
                campaignServiceUrl + "/api/campaigns/active",
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<List<Map<String, Object>>>() {}
            );
            
            List<Map<String, Object>> campaigns = response.getBody();
            log.info("Aktif kampanya sayısı: {}", campaigns != null ? campaigns.size() : 0);
            return campaigns;
            
        } catch (Exception e) {
            log.error("Aktif kampanyalar alınırken hata oluştu", e);
            return getDefaultCampaigns();
        }
    }

    /**
     * Kategoriye göre kampanyaları getir
     */
    public List<Map<String, Object>> getCampaignsByCategory(String category) {
        log.info("Campaign Service'ten {} kategorisi kampanyaları getiriliyor", category);
        
        try {
            ResponseEntity<List<Map<String, Object>>> response = restTemplate.exchange(
                campaignServiceUrl + "/api/campaigns/category/" + category,
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<List<Map<String, Object>>>() {}
            );
            
            List<Map<String, Object>> campaigns = response.getBody();
            log.info("{} kategorisinde kampanya sayısı: {}", category, campaigns != null ? campaigns.size() : 0);
            return campaigns;
            
        } catch (Exception e) {
            log.error("{} kategorisi kampanyaları alınırken hata oluştu", category, e);
            return getDefaultCampaignsByCategory(category);
        }
    }

    /**
     * Markaya göre kampanyaları getir
     */
    public List<Map<String, Object>> getCampaignsByBrand(String brandName) {
        log.info("Campaign Service'ten {} markası kampanyaları getiriliyor", brandName);
        
        try {
            ResponseEntity<List<Map<String, Object>>> response = restTemplate.exchange(
                campaignServiceUrl + "/api/campaigns/brand/" + brandName,
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<List<Map<String, Object>>>() {}
            );
            
            List<Map<String, Object>> campaigns = response.getBody();
            log.info("{} markasında kampanya sayısı: {}", brandName, campaigns != null ? campaigns.size() : 0);
            return campaigns;
            
        } catch (Exception e) {
            log.error("{} markası kampanyaları alınırken hata oluştu", brandName, e);
            return getDefaultCampaignsByBrand(brandName);
        }
    }

    /**
     * Müşteri profiline uygun kampanyaları öner
     */
    public List<Map<String, Object>> getRecommendedCampaigns(String customerProfile, String currentCampaign) {
        log.info("Müşteri profiline uygun kampanyalar öneriliyor: {}", customerProfile);
        
        try {
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("customerProfile", customerProfile);
            requestBody.put("currentCampaign", currentCampaign);
            requestBody.put("maxRecommendations", 3);
            
            ResponseEntity<List<Map<String, Object>>> response = restTemplate.exchange(
                campaignServiceUrl + "/api/campaigns/recommend",
                HttpMethod.POST,
                new org.springframework.http.HttpEntity<>(requestBody),
                new ParameterizedTypeReference<List<Map<String, Object>>>() {}
            );
            
            List<Map<String, Object>> recommendations = response.getBody();
            log.info("Önerilen kampanya sayısı: {}", recommendations != null ? recommendations.size() : 0);
            return recommendations;
            
        } catch (Exception e) {
            log.error("Kampanya önerileri alınırken hata oluştu", e);
            return getDefaultRecommendations(customerProfile);
        }
    }

    /**
     * Kampanya detayını getir
     */
    public Map<String, Object> getCampaignDetails(String campaignId) {
        log.info("Kampanya detayı getiriliyor: {}", campaignId);
        
        try {
            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                campaignServiceUrl + "/api/campaigns/" + campaignId,
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<Map<String, Object>>() {}
            );
            
            return response.getBody();
            
        } catch (Exception e) {
            log.error("Kampanya detayı alınırken hata oluştu: {}", campaignId, e);
            return getDefaultCampaignDetails(campaignId);
        }
    }

    /**
     * Popüler kampanyaları getir
     */
    public List<Map<String, Object>> getPopularCampaigns(int limit) {
        log.info("Popüler kampanyalar getiriliyor, limit: {}", limit);
        
        try {
            ResponseEntity<List<Map<String, Object>>> response = restTemplate.exchange(
                campaignServiceUrl + "/api/campaigns/popular?limit=" + limit,
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<List<Map<String, Object>>>() {}
            );
            
            return response.getBody();
            
        } catch (Exception e) {
            log.error("Popüler kampanyalar alınırken hata oluştu", e);
            return getDefaultPopularCampaigns();
        }
    }

    // Fallback methods - Campaign Service erişilemezse
    private List<Map<String, Object>> getDefaultCampaigns() {
        return List.of(
            createDefaultCampaign("BMW X3 Özel Fırsatı", "BMW", "OTOMOTİV", "Sıfır faizli kredi imkanı"),
            createDefaultCampaign("Mercedes C-Class Kampanyası", "Mercedes", "OTOMOTİV", "Test sürüşü hediyeli"),
            createDefaultCampaign("Axa Sigorta Avantajı", "Axa", "SİGORTA", "%30 indirimli poliçe")
        );
    }

    private List<Map<String, Object>> getDefaultCampaignsByCategory(String category) {
        if ("OTOMOTİV".equalsIgnoreCase(category)) {
            return List.of(
                createDefaultCampaign("BMW X3 Özel Fırsatı", "BMW", "OTOMOTİV", "Sıfır faizli kredi imkanı"),
                createDefaultCampaign("Mercedes C-Class Kampanyası", "Mercedes", "OTOMOTİV", "Test sürüşü hediyeli")
            );
        } else if ("SİGORTA".equalsIgnoreCase(category)) {
            return List.of(
                createDefaultCampaign("Axa Sigorta Avantajı", "Axa", "SİGORTA", "%30 indirimli poliçe")
            );
        }
        return getDefaultCampaigns();
    }

    private List<Map<String, Object>> getDefaultCampaignsByBrand(String brandName) {
        return List.of(
            createDefaultCampaign(brandName + " Özel Kampanyası", brandName, "GENEL", "Özel indirim fırsatı")
        );
    }

    private List<Map<String, Object>> getDefaultRecommendations(String customerProfile) {
        return List.of(
            createDefaultCampaign("Size Özel Kampanya", "360 Avantajlı", "GENEL", "Profilinize uygun özel fırsat")
        );
    }

    private List<Map<String, Object>> getDefaultPopularCampaigns() {
        return getDefaultCampaigns();
    }

    private Map<String, Object> getDefaultCampaignDetails(String campaignId) {
        return createDefaultCampaign("Kampanya Detayı", "Marka", "GENEL", "Kampanya açıklaması");
    }

    private Map<String, Object> createDefaultCampaign(String name, String brand, String category, String description) {
        Map<String, Object> campaign = new HashMap<>();
        campaign.put("id", java.util.UUID.randomUUID().toString());
        campaign.put("name", name);
        campaign.put("brandName", brand);
        campaign.put("category", category);
        campaign.put("description", description);
        campaign.put("status", "ACTIVE");
        campaign.put("startDate", java.time.LocalDateTime.now().minusDays(7));
        campaign.put("endDate", java.time.LocalDateTime.now().plusDays(30));
        return campaign;
    }

    /**
     * Campaign Service sağlık kontrolü
     */
    public boolean isCampaignServiceHealthy() {
        try {
            ResponseEntity<String> response = restTemplate.getForEntity(
                campaignServiceUrl + "/actuator/health",
                String.class
            );
            return response.getStatusCode().is2xxSuccessful();
        } catch (Exception e) {
            log.error("Campaign Service health check failed", e);
            return false;
        }
    }
}
