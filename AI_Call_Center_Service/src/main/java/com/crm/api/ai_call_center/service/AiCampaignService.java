package com.crm.api.ai_call_center.service;

import com.crm.api.ai_call_center.dto.AiCampaignDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class AiCampaignService {

    public String createCampaign(AiCampaignDto campaignDto) {
        log.info("AI Campaign oluşturuluyor: {}", campaignDto.getName());
        
        // Campaign ID oluştur
        String campaignId = UUID.randomUUID().toString();
        
        // Burada campaign'i database'e kaydedebilirsiniz
        log.info("Campaign kaydedildi: {} - {}", campaignId, campaignDto.getName());
        
        return campaignId;
    }
}
