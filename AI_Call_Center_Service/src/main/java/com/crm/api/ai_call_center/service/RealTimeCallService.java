package com.crm.api.ai_call_center.service;

import com.crm.api.ai_call_center.dto.AiCallDto;
import com.crm.api.ai_call_center.dto.AiCallResponseDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;

@Service
@RequiredArgsConstructor
@Slf4j
public class RealTimeCallService implements SpeechToTextService.StreamingCallback {

    private final AiApiService aiApiService;
    private final TextToSpeechService textToSpeechService;
    private final SpeechToTextService speechToTextService;
    private final CallScriptService callScriptService;
    
    private final ConcurrentHashMap<String, CallSession> activeSessions = new ConcurrentHashMap<>();

    /**
     * <PERSON><PERSON><PERSON><PERSON> zamanlı AI konuşması başlat
     */
    public void startRealTimeCall(String callId, AiCallDto callDto) {
        log.info("Gerçek zamanlı AI konuşması başlatılıyor: {}", callId);
        
        try {
            // Call session oluştur
            CallSession session = new CallSession();
            session.setCallId(callId);
            session.setPhoneNumber(callDto.getParticipant().getNumber());
            session.setCampaignInfo(callDto.getParticipant().getAbout());
            session.setStartTime(LocalDateTime.now());
            session.setCurrentStep("GREETING");
            
            activeSessions.put(callId, session);
            
            // İlk AI script'i oluştur ve konuş
            String initialScript = generateInitialScript(callDto);
            session.addConversation("AI", initialScript);
            
            // Text-to-Speech ile sese çevir
            String audioContent = textToSpeechService.convertTextToSpeech(initialScript, "sinem");
            
            if (audioContent != null) {
                // Ses dosyasını kaydet
                String audioUrl = textToSpeechService.saveAudioAndGetUrl(audioContent, callId);
                session.setCurrentAudioUrl(audioUrl);
                
                log.info("AI konuşması başladı: {}", callId);
                
                // Müşteri cevabını dinlemeye başla
                speechToTextService.startStreamingRecognition(callId, this);
            }
            
        } catch (Exception e) {
            log.error("Gerçek zamanlı konuşma başlatılırken hata: {}", callId, e);
        }
    }

    /**
     * Müşteri konuşması algılandığında çağrılır
     */
    @Override
    public void onTranscript(String callId, String transcript) {
        log.info("Müşteri konuşması algılandı [{}]: {}", callId, transcript);
        
        CallSession session = activeSessions.get(callId);
        if (session == null) {
            log.warn("Call session bulunamadı: {}", callId);
            return;
        }
        
        try {
            // Müşteri cevabını kaydet
            session.addConversation("CUSTOMER", transcript);
            
            // AI'dan cevap al
            String aiResponse = generateAiResponse(session, transcript);
            
            if (aiResponse != null && !aiResponse.trim().isEmpty()) {
                session.addConversation("AI", aiResponse);
                
                // Text-to-Speech ile sese çevir
                String audioContent = textToSpeechService.convertTextToSpeech(aiResponse, "sinem");
                
                if (audioContent != null) {
                    String audioUrl = textToSpeechService.saveAudioAndGetUrl(audioContent, callId);
                    session.setCurrentAudioUrl(audioUrl);
                    
                    log.info("AI cevabı oluşturuldu [{}]: {}", callId, aiResponse);
                }
                
                // Konuşma adımını güncelle
                updateConversationStep(session, transcript);
            }
            
            // Konuşma bitmiş mi kontrol et
            if (isConversationComplete(session)) {
                completeCall(callId);
            }
            
        } catch (Exception e) {
            log.error("Müşteri cevabı işlenirken hata [{}]: {}", callId, e.getMessage(), e);
        }
    }

    /**
     * Konuşma tamamlandığında çağrılır
     */
    @Override
    public void onComplete(String callId) {
        log.info("Konuşma tamamlandı: {}", callId);
        completeCall(callId);
    }

    /**
     * Hata oluştuğunda çağrılır
     */
    @Override
    public void onError(String callId, String error) {
        log.error("Konuşma hatası [{}]: {}", callId, error);
        
        CallSession session = activeSessions.get(callId);
        if (session != null) {
            session.setStatus("ERROR");
            session.setErrorMessage(error);
        }
    }

    private String generateInitialScript(AiCallDto callDto) {
        String participantInfo = callDto.getParticipant().getAbout();
        String customerName = extractCustomerName(participantInfo);
        String brandName = extractBrandName(participantInfo);
        String campaignName = extractCampaignName(participantInfo);
        String campaignCategory = determineCampaignCategory(participantInfo);
        
        return callScriptService.generateCallScript(campaignName, brandName, customerName, campaignCategory);
    }

    private String generateAiResponse(CallSession session, String customerMessage) {
        try {
            String conversationContext = session.getConversationHistory();
            String campaignInfo = session.getCampaignInfo();
            
            return aiApiService.generateAiResponse(customerMessage, conversationContext, campaignInfo);
            
        } catch (Exception e) {
            log.error("AI response oluşturulurken hata", e);
            
            // Fallback response
            return callScriptService.generateResponseToCustomer(customerMessage, session.getCurrentStep());
        }
    }

    private void updateConversationStep(CallSession session, String customerMessage) {
        String lowerMessage = customerMessage.toLowerCase();
        
        if (session.getCurrentStep().equals("GREETING")) {
            if (containsPositiveWords(lowerMessage)) {
                if (session.getCampaignInfo().toLowerCase().contains("otomotiv")) {
                    session.setCurrentStep("TEST_DRIVE_OFFER");
                } else {
                    session.setCurrentStep("DETAILED_INFO_OFFER");
                }
            } else {
                session.setCurrentStep("CLOSING");
            }
        } else if (session.getCurrentStep().equals("TEST_DRIVE_OFFER")) {
            if (containsPositiveWords(lowerMessage)) {
                session.setWantsTestDrive(true);
                session.setCurrentStep("DETAILED_INFO_OFFER");
            } else {
                session.setCurrentStep("DETAILED_INFO_OFFER");
            }
        } else if (session.getCurrentStep().equals("DETAILED_INFO_OFFER")) {
            if (containsPositiveWords(lowerMessage)) {
                session.setWantsDetailedInfo(true);
            }
            session.setCurrentStep("CLOSING");
        }
    }

    private boolean isConversationComplete(CallSession session) {
        return "CLOSING".equals(session.getCurrentStep()) || 
               session.getConversationTurns() >= 10; // Max 10 turn
    }

    private void completeCall(String callId) {
        CallSession session = activeSessions.get(callId);
        if (session != null) {
            session.setStatus("COMPLETED");
            session.setEndTime(LocalDateTime.now());
            
            log.info("Call tamamlandı [{}]: Test Sürüşü={}, Detaylı Bilgi={}", 
                    callId, session.isWantsTestDrive(), session.isWantsDetailedInfo());
        }
    }

    private boolean containsPositiveWords(String text) {
        String[] positiveWords = {"evet", "tamam", "olur", "istiyorum", "isterim", "güzel", "harika"};
        for (String word : positiveWords) {
            if (text.contains(word)) return true;
        }
        return false;
    }

    // Helper methods (extract from other services)
    private String extractCustomerName(String participantInfo) {
        if (participantInfo.contains("Müşterinin adı")) {
            String[] parts = participantInfo.split("Müşterinin adı")[1].split("\\.");
            if (parts.length > 0) return parts[0].trim();
        }
        return "Değerli Müşterimiz";
    }

    private String extractBrandName(String participantInfo) {
        // Implementation from AiCallService
        return "360 Avantajlı";
    }

    private String extractCampaignName(String participantInfo) {
        // Implementation from AiCallService
        return "Özel Kampanya";
    }

    private String determineCampaignCategory(String participantInfo) {
        String lowerInfo = participantInfo.toLowerCase();
        return (lowerInfo.contains("otomotiv") || lowerInfo.contains("motosiklet")) ? "OTOMOTİV" : "GENEL";
    }

    /**
     * Call session data class
     */
    public static class CallSession {
        private String callId;
        private String phoneNumber;
        private String campaignInfo;
        private String currentStep;
        private String status = "ACTIVE";
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private String currentAudioUrl;
        private String errorMessage;
        private boolean wantsTestDrive = false;
        private boolean wantsDetailedInfo = false;
        private java.util.List<ConversationTurn> conversation = new java.util.ArrayList<>();
        
        public void addConversation(String speaker, String message) {
            conversation.add(new ConversationTurn(speaker, message, LocalDateTime.now()));
        }
        
        public String getConversationHistory() {
            StringBuilder history = new StringBuilder();
            for (ConversationTurn turn : conversation) {
                history.append(turn.getSpeaker()).append(": ").append(turn.getMessage()).append("\n");
            }
            return history.toString();
        }
        
        public int getConversationTurns() {
            return conversation.size();
        }
        
        // Getters and Setters
        public String getCallId() { return callId; }
        public void setCallId(String callId) { this.callId = callId; }
        
        public String getPhoneNumber() { return phoneNumber; }
        public void setPhoneNumber(String phoneNumber) { this.phoneNumber = phoneNumber; }
        
        public String getCampaignInfo() { return campaignInfo; }
        public void setCampaignInfo(String campaignInfo) { this.campaignInfo = campaignInfo; }
        
        public String getCurrentStep() { return currentStep; }
        public void setCurrentStep(String currentStep) { this.currentStep = currentStep; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
        
        public LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }
        
        public String getCurrentAudioUrl() { return currentAudioUrl; }
        public void setCurrentAudioUrl(String currentAudioUrl) { this.currentAudioUrl = currentAudioUrl; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public boolean isWantsTestDrive() { return wantsTestDrive; }
        public void setWantsTestDrive(boolean wantsTestDrive) { this.wantsTestDrive = wantsTestDrive; }
        
        public boolean isWantsDetailedInfo() { return wantsDetailedInfo; }
        public void setWantsDetailedInfo(boolean wantsDetailedInfo) { this.wantsDetailedInfo = wantsDetailedInfo; }
    }
    
    public static class ConversationTurn {
        private String speaker;
        private String message;
        private LocalDateTime timestamp;
        
        public ConversationTurn(String speaker, String message, LocalDateTime timestamp) {
            this.speaker = speaker;
            this.message = message;
            this.timestamp = timestamp;
        }
        
        // Getters
        public String getSpeaker() { return speaker; }
        public String getMessage() { return message; }
        public LocalDateTime getTimestamp() { return timestamp; }
    }
}
