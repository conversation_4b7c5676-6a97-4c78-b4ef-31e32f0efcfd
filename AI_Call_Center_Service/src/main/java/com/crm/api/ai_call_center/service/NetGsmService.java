package com.crm.api.ai_call_center.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class NetGsmService {

    private final RestTemplate restTemplate = new RestTemplate();
    
    @Value("${netgsm.api.url:https://api.netgsm.com.tr}")
    private String netGsmApiUrl;
    
    @Value("${netgsm.api.username:}")
    private String netGsmUsername;

    @Value("${netgsm.api.password:}")
    private String netGsmPassword;

    @Value("${netgsm.sip.gateway:sip.netgsm.com.tr}")
    private String sipGateway;

    @Value("${netgsm.sip.phone_number:}")
    private String sipPhoneNumber;

    @Value("${netgsm.sip.auth_user:}")
    private String sipAuthUser;

    @Value("${netgsm.sip.auth_pass:}")
    private String sipAuthPass;

    /**
     * Net GSM SIP Trunk ile AI destekli arama başlatma
     */
    public String initiateAiCall(String phoneNumber, String aiScript, String campaignInfo) {
        log.info("Net GSM SIP Trunk ile AI destekli arama başlatılıyor: {} -> {}", sipPhoneNumber, phoneNumber);

        try {
            // SIP Trunk üzerinden arama başlatma
            Map<String, Object> requestBody = new HashMap<>();

            // SIP Authentication
            requestBody.put("sip_gateway", sipGateway);
            requestBody.put("caller_id", sipPhoneNumber);
            requestBody.put("auth_user", sipAuthUser);
            requestBody.put("auth_pass", sipAuthPass);

            // Call Details
            requestBody.put("destination", phoneNumber);
            requestBody.put("type", "sip_ai_call");

            // AI Script bilgileri
            requestBody.put("ai_script", aiScript);
            requestBody.put("campaign_info", campaignInfo);

            // Konuşma parametreleri
            Map<String, Object> aiParams = new HashMap<>();
            aiParams.put("voice_type", "female"); // Sinem sesi
            aiParams.put("language", "tr-TR");
            aiParams.put("speed", "normal");
            aiParams.put("interactive", true); // Müşteri cevaplarını bekle
            aiParams.put("max_duration", 300); // 5 dakika max
            aiParams.put("record_call", true); // Konuşmayı kaydet

            requestBody.put("ai_parameters", aiParams);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            // Net GSM SIP API endpoint'i (gerçek endpoint Net GSM'den öğrenilmeli)
            ResponseEntity<String> response = restTemplate.postForEntity(
                netGsmApiUrl + "/sip/ai/call",
                entity,
                String.class
            );

            log.info("Net GSM SIP AI arama başlatıldı. Response: {}", response.getBody());
            return response.getBody();

        } catch (Exception e) {
            log.error("Net GSM SIP AI arama başlatılırken hata oluştu", e);

            // Fallback: Basit sesli mesaj gönder
            log.info("Fallback: Basit sesli mesaj gönderiliyor");
            return initiateSipCall(phoneNumber, aiScript);
        }
    }

    /**
     * SIP Trunk ile basit arama (fallback)
     */
    public String initiateSipCall(String phoneNumber, String message) {
        log.info("Net GSM SIP Trunk ile basit arama: {} -> {}", sipPhoneNumber, phoneNumber);

        try {
            Map<String, Object> requestBody = new HashMap<>();

            // SIP Authentication
            requestBody.put("username", netGsmUsername);
            requestBody.put("password", netGsmPassword);
            requestBody.put("caller_id", sipPhoneNumber);

            // Call Details
            requestBody.put("destination", phoneNumber);
            requestBody.put("message", message);
            requestBody.put("type", "sip_voice");

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(
                netGsmApiUrl + "/sip/voice/call",
                entity,
                String.class
            );

            log.info("Net GSM SIP arama başlatıldı. Response: {}", response.getBody());
            return response.getBody();

        } catch (Exception e) {
            log.error("Net GSM SIP arama başlatılırken hata oluştu", e);
            throw new RuntimeException("Net GSM SIP call failed", e);
        }
    }

    /**
     * Basit sesli mesaj gönderme (fallback)
     */
    public String initiateCall(String phoneNumber, String message) {
        log.info("Net GSM ile basit arama başlatılıyor: {}", phoneNumber);

        try {
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("username", netGsmUsername);
            requestBody.put("password", netGsmPassword);
            requestBody.put("phone", phoneNumber);
            requestBody.put("message", message);
            requestBody.put("type", "voice"); // Basit sesli mesaj

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(
                netGsmApiUrl + "/voice/call",
                entity,
                String.class
            );

            log.info("Net GSM arama başlatıldı. Response: {}", response.getBody());
            return response.getBody();

        } catch (Exception e) {
            log.error("Net GSM arama başlatılırken hata oluştu", e);
            throw new RuntimeException("Net GSM call failed", e);
        }
    }

    /**
     * Arama durumunu sorgulama
     */
    public String getCallStatus(String callId) {
        log.info("Net GSM arama durumu sorgulanıyor: {}", callId);
        
        try {
            String url = netGsmApiUrl + "/voice/status/" + callId + 
                        "?username=" + netGsmUsername + "&password=" + netGsmPassword;
            
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            
            log.info("Net GSM arama durumu: {}", response.getBody());
            return response.getBody();
            
        } catch (Exception e) {
            log.error("Net GSM arama durumu sorgulanırken hata oluştu", e);
            throw new RuntimeException("Net GSM status check failed", e);
        }
    }

    /**
     * Toplu arama (bulk call)
     */
    public String initiateBulkCall(String[] phoneNumbers, String message) {
        log.info("Net GSM ile toplu arama başlatılıyor. Adet: {}", phoneNumbers.length);
        
        try {
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("username", netGsmUsername);
            requestBody.put("password", netGsmPassword);
            requestBody.put("phones", phoneNumbers);
            requestBody.put("message", message);
            requestBody.put("type", "bulk_voice");
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<String> response = restTemplate.postForEntity(
                netGsmApiUrl + "/voice/bulk", 
                entity, 
                String.class
            );
            
            log.info("Net GSM toplu arama başlatıldı. Response: {}", response.getBody());
            return response.getBody();
            
        } catch (Exception e) {
            log.error("Net GSM toplu arama başlatılırken hata oluştu", e);
            throw new RuntimeException("Net GSM bulk call failed", e);
        }
    }
}
