package com.crm.api.ai_call_center.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class AiContactService {

    public String createContact(Map<String, Object> contactData) {
        log.info("AI Contact oluşturuluyor: {}", contactData.get("email"));
        
        // Contact ID oluştur
        String contactId = UUID.randomUUID().toString();
        
        // Burada contact'ı database'e kaydedebilirsiniz
        log.info("Contact kaydedildi: {} - {}", contactId, contactData.get("email"));
        
        return contactId;
    }
}
