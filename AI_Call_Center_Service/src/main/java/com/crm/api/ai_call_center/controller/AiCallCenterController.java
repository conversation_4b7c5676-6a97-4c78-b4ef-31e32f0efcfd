package com.crm.api.ai_call_center.controller;

import com.crm.api.ai_call_center.dto.AiCallDto;
import com.crm.api.ai_call_center.dto.AiCallResponseDto;
import com.crm.api.ai_call_center.dto.AiCampaignDto;
import com.crm.api.ai_call_center.dto.AiContactDto;
import com.crm.api.ai_call_center.service.AiCallService;
import com.crm.api.ai_call_center.service.AiCampaignService;
import com.crm.api.ai_call_center.service.AiContactService;
import com.crm.api.ai_call_center.service.CallScriptService;
import com.crm.api.ai_call_center.service.AiApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1")
@CrossOrigin(origins = "*")
@RequiredArgsConstructor
@Slf4j
public class AiCallCenterController {

    private final AiCallService aiCallService;
    private final AiCampaignService aiCampaignService;
    private final AiContactService aiContactService;
    private final CallScriptService callScriptService;
    private final AiApiService aiApiService;
    private final TextToSpeechService textToSpeechService;
    private final SpeechToTextService speechToTextService;
    private final NetGsmService netGsmService;
    private final SmartCampaignService smartCampaignService;

    /**
     * Call API - Mindhunter API'sine uyumlu
     * POST /api/v1/call
     */
    @PostMapping("/call")
    public ResponseEntity<String> initiateCall(@RequestBody AiCallDto callDto) {
        log.info("AI Call Center'a call isteği geldi: {}", callDto.getParticipant().getNumber());
        
        try {
            AiCallResponseDto response = aiCallService.initiateCall(callDto);
            log.info("AI Call başarıyla başlatıldı! Call ID: {}", response.getCallId());
            
            // Mindhunter API'sine benzer response format
            return ResponseEntity.ok("Call initiated successfully. Call ID: " + response.getCallId());
        } catch (Exception e) {
            log.error("AI Call başlatılırken hata oluştu", e);
            return ResponseEntity.internalServerError().body("Call initiation failed: " + e.getMessage());
        }
    }

    /**
     * Campaign API - Mindhunter API'sine uyumlu
     * POST /api/v1/campaigns
     */
    @PostMapping("/campaigns")
    public ResponseEntity<String> createCampaign(@RequestBody AiCampaignDto campaignDto) {
        log.info("AI Call Center'a campaign isteği geldi: {}", campaignDto.getName());
        
        try {
            String campaignId = aiCampaignService.createCampaign(campaignDto);
            log.info("Campaign başarıyla oluşturuldu! Campaign ID: {}", campaignId);
            
            return ResponseEntity.ok("Campaign created successfully. Campaign ID: " + campaignId);
        } catch (Exception e) {
            log.error("Campaign oluşturulurken hata oluştu", e);
            return ResponseEntity.internalServerError().body("Campaign creation failed: " + e.getMessage());
        }
    }

    /**
     * Contact API - Mindhunter API'sine uyumlu
     * POST /api/v1/contacts
     */
    @PostMapping("/contacts")
    public ResponseEntity<String> createContact(@RequestBody Map<String, Object> contactData) {
        log.info("AI Call Center'a contact isteği geldi: {}", contactData.get("email"));
        
        try {
            String contactId = aiContactService.createContact(contactData);
            log.info("Contact başarıyla oluşturuldu! Contact ID: {}", contactId);
            
            return ResponseEntity.ok("Contact created successfully. Contact ID: " + contactId);
        } catch (Exception e) {
            log.error("Contact oluşturulurken hata oluştu", e);
            return ResponseEntity.internalServerError().body("Contact creation failed: " + e.getMessage());
        }
    }

    /**
     * AI Script Test API - Konuşma script'ini test etmek için
     * POST /api/v1/test-script
     */
    @PostMapping("/test-script")
    public ResponseEntity<Map<String, String>> testCallScript(@RequestBody Map<String, String> request) {
        log.info("AI Script test isteği");
        
        try {
            String campaignName = request.getOrDefault("campaignName", "Test Kampanyası");
            String brandName = request.getOrDefault("brandName", "Test Marka");
            String customerName = request.getOrDefault("customerName", "Ahmet Yılmaz");
            String campaignCategory = request.getOrDefault("campaignCategory", "GENEL");
            
            String script = callScriptService.generateCallScript(campaignName, brandName, customerName, campaignCategory);
            
            return ResponseEntity.ok(Map.of(
                "script", script,
                "campaignName", campaignName,
                "brandName", brandName,
                "customerName", customerName,
                "campaignCategory", campaignCategory
            ));
        } catch (Exception e) {
            log.error("Script test edilirken hata oluştu", e);
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * Call Status API - Call durumunu sorgulama
     * GET /api/v1/call/{callId}/status
     */
    @GetMapping("/call/{callId}/status")
    public ResponseEntity<AiCallResponseDto> getCallStatus(@PathVariable String callId) {
        log.info("Call status sorgusu: {}", callId);
        
        try {
            AiCallResponseDto response = aiCallService.getCallStatus(callId);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Call status sorgulanırken hata oluştu", e);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Health Check API
     * GET /api/v1/health
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> healthCheck() {
        return ResponseEntity.ok(Map.of(
            "status", "UP",
            "service", "AI Call Center",
            "version", "1.0.0",
            "features", "Otomotiv Test Sürüşü, Detaylı Bilgi, Net GSM SIP Trunk",
            "sip_number", "**********"
        ));
    }

    /**
     * Net GSM SIP Test API
     * POST /api/v1/test-sip
     */
    @PostMapping("/test-sip")
    public ResponseEntity<Map<String, Object>> testSipConnection(@RequestBody Map<String, String> request) {
        log.info("Net GSM SIP bağlantısı test ediliyor");

        try {
            String testNumber = request.getOrDefault("phoneNumber", "**********");
            String testMessage = request.getOrDefault("message", "Bu bir test aramasıdır. Net GSM SIP Trunk çalışıyor.");

            Map<String, Object> result = new HashMap<>();
            result.put("sip_gateway", "sip.netgsm.com.tr");
            result.put("caller_id", "**********");
            result.put("test_number", testNumber);

            try {
                String sipResponse = netGsmService.initiateSipCall(testNumber, testMessage);
                result.put("sip_status", "SUCCESS");
                result.put("sip_response", sipResponse);
                result.put("message", "SIP arama başarıyla başlatıldı");
            } catch (Exception e) {
                result.put("sip_status", "ERROR");
                result.put("sip_error", e.getMessage());
                result.put("message", "SIP arama başlatılamadı");
            }

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("SIP test edilirken hata oluştu", e);
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * Active Calls API - Aktif çağrıları listele
     * GET /api/v1/calls/active
     */
    @GetMapping("/calls/active")
    public ResponseEntity<List<AiCallResponseDto>> getActiveCalls() {
        try {
            List<AiCallResponseDto> activeCalls = aiCallService.getActiveCalls();
            return ResponseEntity.ok(activeCalls);
        } catch (Exception e) {
            log.error("Aktif çağrılar sorgulanırken hata oluştu", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * AI Test API - AI özelliklerini test et
     * POST /api/v1/test-ai
     */
    @PostMapping("/test-ai")
    public ResponseEntity<Map<String, Object>> testAiFeatures(@RequestBody Map<String, String> request) {
        log.info("AI özellikleri test ediliyor");

        try {
            Map<String, Object> result = new HashMap<>();

            String testText = request.getOrDefault("text", "Merhaba, ben Sinem. Size BMW kampanyası hakkında bilgi vermek istiyorum.");
            String userMessage = request.getOrDefault("userMessage", "Evet, ilgiliyim");

            // 1. AI Response Test
            try {
                String aiResponse = aiApiService.generateAiResponse(userMessage, "", "BMW Kampanyası");
                result.put("aiResponse", aiResponse);
                result.put("aiStatus", "SUCCESS");
            } catch (Exception e) {
                result.put("aiResponse", "AI API hatası: " + e.getMessage());
                result.put("aiStatus", "ERROR");
            }

            // 2. Text-to-Speech Test
            try {
                String audioContent = textToSpeechService.convertTextToSpeech(testText, "sinem");
                result.put("ttsStatus", audioContent != null ? "SUCCESS" : "ERROR");
                result.put("ttsAudioLength", audioContent != null ? audioContent.length() : 0);
            } catch (Exception e) {
                result.put("ttsStatus", "ERROR");
                result.put("ttsError", e.getMessage());
            }

            // 3. Speech-to-Text Test (simulated)
            try {
                result.put("sttStatus", "SIMULATED");
                result.put("sttTranscript", "Test transcript: " + userMessage);
            } catch (Exception e) {
                result.put("sttStatus", "ERROR");
                result.put("sttError", e.getMessage());
            }

            // 4. Call Script Test
            try {
                String script = callScriptService.generateCallScript("BMW Kampanyası", "BMW", "Ahmet Yılmaz", "OTOMOTİV");
                result.put("scriptStatus", "SUCCESS");
                result.put("script", script.substring(0, Math.min(200, script.length())) + "...");
            } catch (Exception e) {
                result.put("scriptStatus", "ERROR");
                result.put("scriptError", e.getMessage());
            }

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("AI test edilirken hata oluştu", e);
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * TTS Test API - Sadece Text-to-Speech test et
     * POST /api/v1/test-tts
     */
    @PostMapping("/test-tts")
    public ResponseEntity<Map<String, Object>> testTextToSpeech(@RequestBody Map<String, String> request) {
        log.info("Text-to-Speech test ediliyor");

        try {
            String text = request.getOrDefault("text", "Merhaba, ben Sinem. Size kampanya hakkında bilgi vermek istiyorum.");
            String voiceType = request.getOrDefault("voiceType", "sinem");

            String audioContent = textToSpeechService.convertTextToSpeech(text, voiceType);

            Map<String, Object> result = new HashMap<>();
            result.put("success", audioContent != null);
            result.put("text", text);
            result.put("voiceType", voiceType);
            result.put("audioLength", audioContent != null ? audioContent.length() : 0);

            if (audioContent != null) {
                result.put("audioPreview", audioContent.substring(0, Math.min(100, audioContent.length())) + "...");
            }

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("TTS test edilirken hata oluştu", e);
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * Campaign Integration Test API
     * POST /api/v1/test-campaigns
     */
    @PostMapping("/test-campaigns")
    public ResponseEntity<Map<String, Object>> testCampaignIntegration(@RequestBody Map<String, String> request) {
        log.info("Campaign Service entegrasyonu test ediliyor");

        try {
            String customerProfile = request.getOrDefault("customerProfile", "INTERESTED_CUSTOMER");
            String currentCampaign = request.getOrDefault("currentCampaign", "BMW X3 Kampanyası");
            String customerMood = request.getOrDefault("customerMood", "INTERESTED");
            String interestLevel = request.getOrDefault("interestLevel", "HIGH");
            String category = request.getOrDefault("category", "OTOMOTİV");

            Map<String, Object> result = new HashMap<>();

            // Smart recommendations test
            try {
                List<Map<String, Object>> recommendations = smartCampaignService.getSmartRecommendations(
                    customerProfile, currentCampaign, customerMood, interestLevel, category);
                result.put("smartRecommendations", recommendations);
                result.put("recommendationCount", recommendations.size());
                result.put("campaignServiceStatus", "SUCCESS");
            } catch (Exception e) {
                result.put("campaignServiceStatus", "ERROR");
                result.put("campaignServiceError", e.getMessage());
            }

            // Dynamic script test
            try {
                String customerResponse = request.getOrDefault("customerResponse", "Evet, ilgiliyim ama başka seçenekler de var mı?");
                String dynamicScript = smartCampaignService.generateCampaignSuggestionScript(
                    customerResponse, currentCampaign, customerProfile, "CONVERSATION_CONTEXT");
                result.put("dynamicScript", dynamicScript);
                result.put("scriptGenerationStatus", "SUCCESS");
            } catch (Exception e) {
                result.put("scriptGenerationStatus", "ERROR");
                result.put("scriptError", e.getMessage());
            }

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("Campaign integration test edilirken hata oluştu", e);
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }
}
