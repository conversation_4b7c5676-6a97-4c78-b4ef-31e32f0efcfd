package com.crm.api.ai_call_center.service;

import com.crm.api.ai_call_center.dto.AiCallDto;
import com.crm.api.ai_call_center.dto.AiCallResponseDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class AiEngineService {

    private final CallScriptService callScriptService;
    private final SmartCampaignService smartCampaignService;

    /**
     * Kendi AI motorumuzla call işlemi
     */
    public AiCallResponseDto.AiAnalysis processCall(AiCallDto callDto) {
        log.info("AI Call işleniyor: {}", callDto.getParticipant().getNumber());

        try {
            // Participant bilgisinden kampanya detaylarını çıkar
            String participantInfo = callDto.getParticipant().getAbout();
            String promptContent = callDto.getPrompt().getContent();

            // Kampanya bilgilerini parse et
            CallInfo callInfo = parseCallInfo(participantInfo, promptContent);

            // AI konuşma script'i oluştur
            String callScript = callScriptService.generateCallScript(
                callInfo.getCampaignName(),
                callInfo.getBrandName(),
                callInfo.getCustomerName(),
                callInfo.getCampaignCategory()
            );

            // Simulated konuşma sonucu (gerçek AI entegrasyonunda burası değişecek)
            String simulatedConversation = simulateConversation(callScript, callInfo);

            // Konuşma sonucunu analiz et
            CallScriptService.CallResult callResult = callScriptService.analyzeCallResult(simulatedConversation);

            return AiCallResponseDto.AiAnalysis.builder()
                    .sentiment(callResult.getSentiment())
                    .interestLevel(callResult.isInterestInCampaign() ? "HIGH" : "LOW")
                    .nextAction(callResult.getNextAction())
                    .summary(generateCallSummary(callInfo, callResult))
                    .keywords(generateKeywords(callInfo, callResult))
                    .build();

        } catch (Exception e) {
            log.error("AI Engine processing failed", e);
            throw new RuntimeException("AI Engine processing failed", e);
        }
    }

    private CallInfo parseCallInfo(String participantInfo, String promptContent) {
        CallInfo info = new CallInfo();

        // Participant info'dan müşteri adını çıkar
        if (participantInfo.contains("Müşterinin adı")) {
            String[] parts = participantInfo.split("Müşterinin adı")[1].split("\\.");
            if (parts.length > 0) {
                info.setCustomerName(parts[0].trim());
            }
        }

        // Kampanya bilgilerini çıkar
        if (participantInfo.contains("kampanya") || participantInfo.contains("Kampanya")) {
            String[] parts = participantInfo.split("kampanya|Kampanya");
            if (parts.length > 1) {
                String campaignPart = parts[1];
                if (campaignPart.contains(":")) {
                    String campaignInfo = campaignPart.split(":")[1].trim();
                    String[] campaignParts = campaignInfo.split(" ");
                    if (campaignParts.length > 0) {
                        info.setBrandName(campaignParts[0]);
                        if (campaignParts.length > 1) {
                            info.setCampaignName(String.join(" ", java.util.Arrays.copyOfRange(campaignParts, 1, campaignParts.length)));
                        }
                    }
                }
            }
        }

        // Kategori tespiti
        String lowerInfo = participantInfo.toLowerCase();
        if (lowerInfo.contains("otomotiv") || lowerInfo.contains("motosiklet") || lowerInfo.contains("araç")) {
            info.setCampaignCategory("OTOMOTİV");
        } else {
            info.setCampaignCategory("GENEL");
        }

        return info;
    }

    private String simulateConversation(String script, CallInfo callInfo) {
        // Gerçek AI entegrasyonunda burası değişecek
        // Şimdilik simulated conversation
        StringBuilder conversation = new StringBuilder();
        conversation.append("AI: ").append(script).append("\n");

        if ("OTOMOTİV".equals(callInfo.getCampaignCategory())) {
            conversation.append("Müşteri: Evet, ilgiliyim.\n");
            conversation.append("AI: Test sürüşü yapmak ister misiniz?\n");
            conversation.append("Müşteri: Evet, test sürüşü istiyorum.\n");
            conversation.append("AI: Marka yetkilisinden detaylı bilgi alabilir misiniz?\n");
            conversation.append("Müşteri: Tamam, arayabilirler.\n");
        } else {
            conversation.append("Müşteri: Evet, ilgiliyim.\n");
            conversation.append("AI: Marka yetkilisinden detaylı bilgi alabilir misiniz?\n");
            conversation.append("Müşteri: Evet, arayabilirler.\n");
        }

        return conversation.toString();
    }

    private String generateCallSummary(CallInfo callInfo, CallScriptService.CallResult result) {
        StringBuilder summary = new StringBuilder();
        summary.append("Müşteri ").append(callInfo.getCustomerName())
               .append(" ile ").append(callInfo.getBrandName())
               .append(" ").append(callInfo.getCampaignName())
               .append(" kampanyası hakkında konuşuldu. ");

        if (result.isInterestInCampaign()) {
            summary.append("Müşteri kampanyaya ilgi gösterdi. ");
        }

        if (result.isWantsTestDrive()) {
            summary.append("Test sürüşü talep etti. ");
        }

        if (result.isWantsDetailedInfo()) {
            summary.append("Detaylı bilgi için aranmayı kabul etti.");
        }

        return summary.toString();
    }

    private String[] generateKeywords(CallInfo callInfo, CallScriptService.CallResult result) {
        java.util.List<String> keywords = new java.util.ArrayList<>();
        keywords.add(callInfo.getBrandName());
        keywords.add(callInfo.getCampaignCategory().toLowerCase());

        if (result.isWantsTestDrive()) {
            keywords.add("test_sürüşü");
        }

        if (result.isWantsDetailedInfo()) {
            keywords.add("detaylı_bilgi");
        }

        return keywords.toArray(new String[0]);
    }

    // Helper class
    private static class CallInfo {
        private String customerName = "";
        private String brandName = "";
        private String campaignName = "";
        private String campaignCategory = "";

        // Getters and Setters
        public String getCustomerName() { return customerName; }
        public void setCustomerName(String customerName) { this.customerName = customerName; }

        public String getBrandName() { return brandName; }
        public void setBrandName(String brandName) { this.brandName = brandName; }

        public String getCampaignName() { return campaignName; }
        public void setCampaignName(String campaignName) { this.campaignName = campaignName; }

        public String getCampaignCategory() { return campaignCategory; }
        public void setCampaignCategory(String campaignCategory) { this.campaignCategory = campaignCategory; }
    }
}
