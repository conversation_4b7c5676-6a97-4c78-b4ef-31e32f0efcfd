package com.crm.api.ai_call_center.service;

import com.crm.api.ai_call_center.dto.AiCallDto;
import com.crm.api.ai_call_center.dto.AiCallResponseDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

@Service
@RequiredArgsConstructor
@Slf4j
public class AiCallService {

    private final ConcurrentHashMap<String, AiCallResponseDto> activeCalls = new ConcurrentHashMap<>();
    private final AiEngineService aiEngineService;
    private final NetGsmService netGsmService;
    private final CallScriptService callScriptService;
    // Reporting service'i ekleyeceğiz ama şimdilik comment'te bırakıyoruz
    // private final CallReportingService callReportingService;

    public AiCallResponseDto initiateCall(AiCallDto callDto) {
        log.info("AI Call başlatılıyor: {}", callDto.getParticipant().getNumber());
        
        String callId = UUID.randomUUID().toString();
        
        AiCallResponseDto response = AiCallResponseDto.builder()
                .callId(callId)
                .status("INITIATED")
                .participantNumber(callDto.getParticipant().getNumber())
                .agentId(callDto.getAgentId())
                .startTime(LocalDateTime.now())
                .callResult("PENDING")
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
        
        activeCalls.put(callId, response);
        
        // Net GSM ile gerçek arama başlat
        processCallWithNetGsm(callId, callDto);
        
        return response;
    }

    private void processCallWithNetGsm(String callId, AiCallDto callDto) {
        new Thread(() -> {
            try {
                log.info("AI destekli arama başlatılıyor: {}", callId);

                updateCallStatus(callId, "IN_PROGRESS");

                // Kampanya bilgilerini parse et
                String participantInfo = callDto.getParticipant().getAbout();
                String promptContent = callDto.getPrompt().getContent();

                // Kampanya kategorisini belirle
                String campaignCategory = determineCampaignCategory(participantInfo);
                String customerName = extractCustomerName(participantInfo);
                String brandName = extractBrandName(participantInfo);
                String campaignName = extractCampaignName(participantInfo);

                // AI konuşma script'i oluştur
                String aiScript = callScriptService.generateCallScript(
                    campaignName, brandName, customerName, campaignCategory
                );

                log.info("AI Script oluşturuldu: {}", aiScript.substring(0, Math.min(100, aiScript.length())));

                // Net GSM ile AI destekli arama başlat
                String netGsmResponse = netGsmService.initiateAiCall(
                    callDto.getParticipant().getNumber(),
                    aiScript,
                    String.format("Kampanya: %s %s, Kategori: %s", brandName, campaignName, campaignCategory)
                );

                // AI Engine ile konuşma analizi yap
                AiCallResponseDto.AiAnalysis analysis = aiEngineService.processCall(callDto);

                // Call'ı tamamla
                AiCallResponseDto response = activeCalls.get(callId);
                if (response != null) {
                    response.setStatus("COMPLETED");
                    response.setCallResult("SUCCESS");
                    response.setEndTime(LocalDateTime.now());
                    response.setDurationSeconds(calculateDuration(response.getStartTime(), response.getEndTime()));
                    response.setAiAnalysis(analysis);
                    response.setTranscript(generateTranscript(aiScript, analysis));
                    response.setAudioUrl("https://ai-call-center.com/audio/" + callId + ".mp3");
                    response.setUpdatedAt(LocalDateTime.now());

                    activeCalls.put(callId, response);
                }

                log.info("AI Call tamamlandı: {} - Sonuç: {}", callId, analysis.getSentiment());

            } catch (Exception e) {
                log.error("AI Call işlenirken hata oluştu: {}", callId, e);
                updateCallStatus(callId, "FAILED");
            }
        }).start();
    }

    private String determineCampaignCategory(String participantInfo) {
        String lowerInfo = participantInfo.toLowerCase();
        if (lowerInfo.contains("otomotiv") || lowerInfo.contains("motosiklet") || lowerInfo.contains("araç")) {
            return "OTOMOTİV";
        }
        return "GENEL";
    }

    private String extractCustomerName(String participantInfo) {
        // JSON formatında gelen form verilerini parse et
        try {
            if (participantInfo.contains("\"name\"")) {
                String namePattern = "\"name\"\\s*:\\s*\"([^\"]+)\"";
                java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(namePattern);
                java.util.regex.Matcher matcher = pattern.matcher(participantInfo);
                if (matcher.find()) {
                    String name = matcher.group(1);

                    // Surname'i de bul
                    String surnamePattern = "\"surname\"\\s*:\\s*\"([^\"]+)\"";
                    java.util.regex.Pattern surnamePatternObj = java.util.regex.Pattern.compile(surnamePattern);
                    java.util.regex.Matcher surnameMatcher = surnamePatternObj.matcher(participantInfo);
                    if (surnameMatcher.find()) {
                        String surname = surnameMatcher.group(1);
                        return name + " " + surname;
                    }
                    return name;
                }
            }

            // Eski format desteği
            if (participantInfo.contains("Müşterinin adı")) {
                String[] parts = participantInfo.split("Müşterinin adı")[1].split("\\.");
                if (parts.length > 0) {
                    return parts[0].trim();
                }
            }

            // ContactName alanını kontrol et
            if (participantInfo.contains("contactName")) {
                String contactNamePattern = "\"contactName\"\\s*:\\s*\"([^\"]+)\"";
                java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(contactNamePattern);
                java.util.regex.Matcher matcher = pattern.matcher(participantInfo);
                if (matcher.find()) {
                    return matcher.group(1);
                }
            }

        } catch (Exception e) {
            log.warn("Customer name parse edilemedi: {}", e.getMessage());
        }

        return "Değerli Müşterimiz";
    }

    private String extractBrandName(String participantInfo) {
        try {
            // JSON formatında brandName alanını ara
            if (participantInfo.contains("\"brandName\"")) {
                String brandPattern = "\"brandName\"\\s*:\\s*\"([^\"]+)\"";
                java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(brandPattern);
                java.util.regex.Matcher matcher = pattern.matcher(participantInfo);
                if (matcher.find()) {
                    return matcher.group(1);
                }
            }

            // Campaign içinden brand bilgisini çıkar
            if (participantInfo.contains("\"campaignName\"")) {
                String campaignPattern = "\"campaignName\"\\s*:\\s*\"([^\"]+)\"";
                java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(campaignPattern);
                java.util.regex.Matcher matcher = pattern.matcher(participantInfo);
                if (matcher.find()) {
                    String campaignName = matcher.group(1);
                    // Kampanya adından brand çıkarmaya çalış
                    if (campaignName.contains("BMW")) return "BMW";
                    if (campaignName.contains("Mercedes")) return "Mercedes-Benz";
                    if (campaignName.contains("Audi")) return "Audi";
                    if (campaignName.contains("Volkswagen")) return "Volkswagen";
                    if (campaignName.contains("Ford")) return "Ford";
                    if (campaignName.contains("Renault")) return "Renault";
                    if (campaignName.contains("Peugeot")) return "Peugeot";
                }
            }

            // Eski format desteği
            if (participantInfo.contains("kampanya") || participantInfo.contains("Kampanya")) {
                String[] parts = participantInfo.split("kampanya|Kampanya");
                if (parts.length > 1) {
                    String campaignPart = parts[1];
                    if (campaignPart.contains(":")) {
                        String campaignInfo = campaignPart.split(":")[1].trim();
                        String[] campaignParts = campaignInfo.split(" ");
                        if (campaignParts.length > 0) {
                            return campaignParts[0];
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.warn("Brand name parse edilemedi: {}", e.getMessage());
        }

        return "360 Avantajlı";
    }

    private String extractCampaignName(String participantInfo) {
        try {
            // JSON formatında campaignName alanını ara
            if (participantInfo.contains("\"campaignName\"")) {
                String campaignPattern = "\"campaignName\"\\s*:\\s*\"([^\"]+)\"";
                java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(campaignPattern);
                java.util.regex.Matcher matcher = pattern.matcher(participantInfo);
                if (matcher.find()) {
                    return matcher.group(1);
                }
            }

            // Campaign title alanını kontrol et
            if (participantInfo.contains("\"campaignTitle\"")) {
                String titlePattern = "\"campaignTitle\"\\s*:\\s*\"([^\"]+)\"";
                java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(titlePattern);
                java.util.regex.Matcher matcher = pattern.matcher(participantInfo);
                if (matcher.find()) {
                    return matcher.group(1);
                }
            }

            // Campaign description'dan kısa isim çıkar
            if (participantInfo.contains("\"campaignDescription\"")) {
                String descPattern = "\"campaignDescription\"\\s*:\\s*\"([^\"]+)\"";
                java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(descPattern);
                java.util.regex.Matcher matcher = pattern.matcher(participantInfo);
                if (matcher.find()) {
                    String description = matcher.group(1);
                    // İlk 50 karakteri al
                    return description.length() > 50 ? description.substring(0, 50) + "..." : description;
                }
            }

            // Eski format desteği
            if (participantInfo.contains("kampanya") || participantInfo.contains("Kampanya")) {
                String[] parts = participantInfo.split("kampanya|Kampanya");
                if (parts.length > 1) {
                    String campaignPart = parts[1];
                    if (campaignPart.contains(":")) {
                        String campaignInfo = campaignPart.split(":")[1].trim();
                        String[] campaignParts = campaignInfo.split(" ");
                        if (campaignParts.length > 1) {
                            return String.join(" ", java.util.Arrays.copyOfRange(campaignParts, 1, campaignParts.length));
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.warn("Campaign name parse edilemedi: {}", e.getMessage());
        }

        return "Özel Kampanya";
    }

    private String generateTranscript(String aiScript, AiCallResponseDto.AiAnalysis analysis) {
        StringBuilder transcript = new StringBuilder();
        transcript.append("=== AI CALL CENTER KONUŞMA KAYDI ===\n\n");
        transcript.append("AI Script: ").append(aiScript).append("\n\n");
        transcript.append("Konuşma Analizi:\n");
        transcript.append("- Sentiment: ").append(analysis.getSentiment()).append("\n");
        transcript.append("- İlgi Seviyesi: ").append(analysis.getInterestLevel()).append("\n");
        transcript.append("- Sonraki Aksiyon: ").append(analysis.getNextAction()).append("\n");
        transcript.append("- Özet: ").append(analysis.getSummary()).append("\n");
        transcript.append("- Anahtar Kelimeler: ").append(String.join(", ", analysis.getKeywords())).append("\n");

        return transcript.toString();
    }

    private void updateCallStatus(String callId, String status) {
        AiCallResponseDto response = activeCalls.get(callId);
        if (response != null) {
            response.setStatus(status);
            response.setUpdatedAt(LocalDateTime.now());
            activeCalls.put(callId, response);
        }
    }

    private Integer calculateDuration(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) return null;
        return (int) java.time.Duration.between(start, end).getSeconds();
    }

    public AiCallResponseDto getCallStatus(String callId) {
        AiCallResponseDto response = activeCalls.get(callId);
        if (response == null) {
            throw new RuntimeException("Call not found: " + callId);
        }
        return response;
    }

    public List<AiCallResponseDto> getActiveCalls() {
        return new ArrayList<>(activeCalls.values());
    }
}
