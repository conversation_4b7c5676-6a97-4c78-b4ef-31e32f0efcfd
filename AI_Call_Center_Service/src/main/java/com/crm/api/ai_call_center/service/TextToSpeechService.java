package com.crm.api.ai_call_center.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class TextToSpeechService {

    private final RestTemplate restTemplate = new RestTemplate();
    
    @Value("${ai.tts.provider:google}")
    private String ttsProvider;
    
    @Value("${ai.tts.google.api.key:}")
    private String googleApiKey;
    
    @Value("${ai.tts.elevenlabs.api.key:}")
    private String elevenLabsApiKey;
    
    @Value("${ai.tts.azure.api.key:}")
    private String azureApiKey;

    /**
     * Metni sese çevirir - Provider'a göre
     */
    public String convertTextToSpeech(String text, String voiceType) {
        log.info("Text-to-Speech dönüşümü başlatılıyor - Provider: {}, Voice: {}", ttsProvider, voiceType);
        
        switch (ttsProvider.toLowerCase()) {
            case "google":
                return convertWithGoogle(text, voiceType);
            case "elevenlabs":
                return convertWithElevenLabs(text, voiceType);
            case "azure":
                return convertWithAzure(text, voiceType);
            default:
                log.warn("Bilinmeyen TTS provider: {}, Google kullanılıyor", ttsProvider);
                return convertWithGoogle(text, voiceType);
        }
    }

    /**
     * Google Cloud Text-to-Speech
     */
    private String convertWithGoogle(String text, String voiceType) {
        try {
            String url = "https://texttospeech.googleapis.com/v1/text:synthesize?key=" + googleApiKey;
            
            Map<String, Object> requestBody = new HashMap<>();
            
            // Input text
            Map<String, String> input = new HashMap<>();
            input.put("text", text);
            requestBody.put("input", input);
            
            // Voice configuration
            Map<String, String> voice = new HashMap<>();
            voice.put("languageCode", "tr-TR");
            voice.put("name", getGoogleVoiceName(voiceType));
            voice.put("ssmlGender", "FEMALE");
            requestBody.put("voice", voice);
            
            // Audio configuration
            Map<String, String> audioConfig = new HashMap<>();
            audioConfig.put("audioEncoding", "MP3");
            audioConfig.put("speakingRate", "1.0");
            audioConfig.put("pitch", "0.0");
            requestBody.put("audioConfig", audioConfig);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);
            
            if (response.getBody() != null && response.getBody().containsKey("audioContent")) {
                String audioContent = (String) response.getBody().get("audioContent");
                log.info("Google TTS başarılı");
                return audioContent; // Base64 encoded audio
            }
            
        } catch (Exception e) {
            log.error("Google TTS hatası", e);
        }
        
        return null;
    }

    /**
     * ElevenLabs Text-to-Speech (Daha kaliteli)
     */
    private String convertWithElevenLabs(String text, String voiceType) {
        try {
            String voiceId = getElevenLabsVoiceId(voiceType);
            String url = "https://api.elevenlabs.io/v1/text-to-speech/" + voiceId;
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("text", text);
            requestBody.put("model_id", "eleven_multilingual_v2");
            
            Map<String, Object> voiceSettings = new HashMap<>();
            voiceSettings.put("stability", 0.5);
            voiceSettings.put("similarity_boost", 0.75);
            voiceSettings.put("style", 0.0);
            voiceSettings.put("use_speaker_boost", true);
            requestBody.put("voice_settings", voiceSettings);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("xi-api-key", elevenLabsApiKey);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<byte[]> response = restTemplate.postForEntity(url, entity, byte[].class);
            
            if (response.getBody() != null) {
                log.info("ElevenLabs TTS başarılı");
                return java.util.Base64.getEncoder().encodeToString(response.getBody());
            }
            
        } catch (Exception e) {
            log.error("ElevenLabs TTS hatası", e);
        }
        
        return null;
    }

    /**
     * Azure Cognitive Services Text-to-Speech
     */
    private String convertWithAzure(String text, String voiceType) {
        try {
            String url = "https://eastus.tts.speech.microsoft.com/cognitiveservices/v1";
            
            String ssml = String.format(
                "<speak version='1.0' xml:lang='tr-TR'>" +
                "<voice xml:lang='tr-TR' xml:gender='Female' name='%s'>" +
                "%s" +
                "</voice></speak>",
                getAzureVoiceName(voiceType), text
            );
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_XML);
            headers.set("Ocp-Apim-Subscription-Key", azureApiKey);
            headers.set("X-Microsoft-OutputFormat", "audio-16khz-128kbitrate-mono-mp3");
            
            HttpEntity<String> entity = new HttpEntity<>(ssml, headers);
            
            ResponseEntity<byte[]> response = restTemplate.postForEntity(url, entity, byte[].class);
            
            if (response.getBody() != null) {
                log.info("Azure TTS başarılı");
                return java.util.Base64.getEncoder().encodeToString(response.getBody());
            }
            
        } catch (Exception e) {
            log.error("Azure TTS hatası", e);
        }
        
        return null;
    }

    private String getGoogleVoiceName(String voiceType) {
        switch (voiceType.toLowerCase()) {
            case "female":
            case "sinem":
                return "tr-TR-Standard-A"; // Kadın ses
            case "male":
                return "tr-TR-Standard-B"; // Erkek ses
            default:
                return "tr-TR-Standard-A";
        }
    }

    private String getElevenLabsVoiceId(String voiceType) {
        // ElevenLabs'da önceden tanımlı Türkçe sesler
        switch (voiceType.toLowerCase()) {
            case "female":
            case "sinem":
                return "21m00Tcm4TlvDq8ikWAM"; // Rachel (female)
            case "male":
                return "29vD33N1CtxCmqQRPOHJ"; // Drew (male)
            default:
                return "21m00Tcm4TlvDq8ikWAM";
        }
    }

    private String getAzureVoiceName(String voiceType) {
        switch (voiceType.toLowerCase()) {
            case "female":
            case "sinem":
                return "tr-TR-EmelNeural"; // Türkçe kadın ses
            case "male":
                return "tr-TR-AhmetNeural"; // Türkçe erkek ses
            default:
                return "tr-TR-EmelNeural";
        }
    }

    /**
     * Ses dosyasını kaydet ve URL döndür
     */
    public String saveAudioAndGetUrl(String base64Audio, String callId) {
        try {
            // Burada ses dosyasını sunucuya kaydedip URL döndürebilirsiniz
            // Şimdilik simulated URL
            String audioUrl = "https://ai-call-center.com/audio/" + callId + ".mp3";
            log.info("Ses dosyası kaydedildi: {}", audioUrl);
            return audioUrl;
        } catch (Exception e) {
            log.error("Ses dosyası kaydedilirken hata", e);
            return null;
        }
    }
}
