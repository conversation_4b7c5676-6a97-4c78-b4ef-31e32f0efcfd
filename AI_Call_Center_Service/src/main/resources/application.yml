spring:
  application:
    name: ai-call-center-service

  cloud:
    config:
      uri: http://config-service:8888
    compatibility-verifier:
      enabled: false

  config:
    import: "configserver:"

eureka:
  client:
    service-url:
      defaultZone: http://discovery-service:8761/eureka/

management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always

logging:
  level:
    com.backend360.ai_call_center: DEBUG
    org.springframework.web: INFO
