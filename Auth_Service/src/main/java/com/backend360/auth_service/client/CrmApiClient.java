package com.backend360.auth_service.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * CRM-API ile iletişim için Feign Client
 * CRM Admin'in CRM-API'ye istek atması için kullanılır
 */
@FeignClient(name = "crmApiClient", url = "${crm.api.url}")
public interface CrmApiClient {

    @GetMapping("/api/v1/contacts")
    ResponseEntity<Map<String, Object>> getAllContacts(
            @RequestHeader("Authorization") String token,
            @RequestParam(value = "size", defaultValue = "1000") int size
    );

    @GetMapping("/api/v1/contacts/{uuid}")
    ResponseEntity<Map<String, Object>> getContactByUuid(
            @PathVariable UUID uuid,
            @RequestHeader("Authorization") String token
    );

    @PostMapping("/api/v1/contacts")
    ResponseEntity<Map<String, Object>> createContact(
            @RequestBody Map<String, Object> contact,
            @RequestHeader("Authorization") String token
    );

    @PutMapping("/api/v1/contacts/{uuid}")
    ResponseEntity<Map<String, Object>> updateContact(
            @PathVariable UUID uuid,
            @RequestBody Map<String, Object> contact,
            @RequestHeader("Authorization") String token
    );

    @DeleteMapping("/api/v1/contacts/{uuid}")
    ResponseEntity<Void> deleteContact(
            @PathVariable UUID uuid,
            @RequestHeader("Authorization") String token
    );

    @GetMapping("/api/v1/campaigns")
    ResponseEntity<List<Map<String, Object>>> getAllCampaigns(
            @RequestHeader("Authorization") String token
    );

    @GetMapping("/api/v1/campaigns/{uuid}")
    ResponseEntity<Map<String, Object>> getCampaignByUuid(
            @PathVariable UUID uuid,
            @RequestHeader("Authorization") String token
    );

    @GetMapping("/api/v1/tags")
    ResponseEntity<List<Map<String, Object>>> getAllTags(
            @RequestHeader("Authorization") String token
    );

    @PostMapping("/api/v1/tags")
    ResponseEntity<Map<String, Object>> createTag(
            @RequestBody Map<String, Object> tag,
            @RequestHeader("Authorization") String token
    );

    @GetMapping("/api/v1/calls")
    ResponseEntity<List<Map<String, Object>>> getAllCalls(
            @RequestHeader("Authorization") String token
    );

    @PostMapping("/api/v1/calls")
    ResponseEntity<Map<String, Object>> createCall(
            @RequestBody Map<String, Object> call,
            @RequestHeader("Authorization") String token
    );

    // Webhook Events
    @GetMapping("/api/v1/webhook-events")
    ResponseEntity<List<Map<String, Object>>> getAllWebhookEvents(
            @RequestHeader("Authorization") String token
    );

    @GetMapping("/api/v1/webhook-events/{id}")
    ResponseEntity<Map<String, Object>> getWebhookEventById(
            @PathVariable("id") UUID id,
            @RequestHeader("Authorization") String token
    );
}
