package com.backend360.auth_service.dto;

import com.backend360.auth_service.model.Role;
import lombok.*;

import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class AuthRequest {
    // Temel bilgiler
    private String name;
    private String surname;
    private String email;
    private String username;
    private String password;
    private Role role;

    // Ek bilgiler
    private String phoneNumber;
    private String country;
    private String city;
    private String town;
    private String gender;
    private Date birthday;
    private Long jobId;
    private Boolean remindMe;
    private Boolean isActive;
}
