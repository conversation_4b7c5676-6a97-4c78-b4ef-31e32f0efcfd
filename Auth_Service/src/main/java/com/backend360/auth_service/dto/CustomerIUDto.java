package com.backend360.auth_service.dto;

import com.backend360.auth_service.model.Role;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomerIUDto implements Serializable {
    private String name;
    private String surname;
    private String email;
    private String phoneNumber;
    private String country;
    private String city;
    private String town;
    private Boolean isActive;
    private String gender;
    private Long jobId;
    private Boolean remindMe;
    private Date birthday;
    private String username;
    private String password;
    private Role role;
}
