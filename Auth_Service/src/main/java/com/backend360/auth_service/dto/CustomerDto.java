package com.backend360.auth_service.dto;

import com.backend360.auth_service.model.Customer;
import com.backend360.auth_service.model.Job;
import com.backend360.auth_service.model.Role;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerDto implements Serializable {
    private Long id;
    private String name;
    private String surname;
    private String email;
    private String phoneNumber;
    private String country;
    private String city;
    private String town;
    private Boolean isActive;
    private String gender;
    private Job job;
    private Boolean remindMe;
    private String username;
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String password;
    private Role role;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy")
    private Date birthday;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime updatedAt;

    public static CustomerDto entityToDto(Customer customer) {
        if (customer == null) return null;
        return CustomerDto.builder()
                .id(customer.getId())
                .name(customer.getName())
                .surname(customer.getSurname())
                .email(customer.getEmail())
                .phoneNumber(customer.getPhoneNumber())
                .country(customer.getCountry())
                .city(customer.getCity())
                .town(customer.getTown())
                .isActive(customer.getIsActive())
                .gender(customer.getGender())
                .job(customer.getJob())
                .remindMe(customer.getRemindMe())
                .birthday(customer.getBirthday())
                .createdAt(customer.getCreatedAt())
                .updatedAt(customer.getUpdatedAt())
                .username(customer.getUsername())
                .password(customer.getPassword())
                .role(customer.getRole())
                .build();
    }
}
