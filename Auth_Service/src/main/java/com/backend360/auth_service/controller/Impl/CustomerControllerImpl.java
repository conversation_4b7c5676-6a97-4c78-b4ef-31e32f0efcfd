package com.backend360.auth_service.controller.Impl;

import com.backend360.auth_service.controller.ICustomerController;
import com.backend360.auth_service.dto.CustomerDto;
import com.backend360.auth_service.dto.CustomerIUDto;
import com.backend360.auth_service.dto.PasswordChangeDto;
import com.backend360.auth_service.model.Role;
import com.backend360.auth_service.service.ICustomerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/customer")
public class CustomerControllerImpl implements ICustomerController {

    @Autowired
    private ICustomerService customerService;

    @Override
    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('CRM_ADMIN')")
    public ResponseEntity<List<CustomerDto>> getAllCustomers() {
        List<CustomerDto> customers = customerService.findAll();

        if (customers.isEmpty()) {
            return ResponseEntity.noContent().build();
        }

        return ResponseEntity.ok(customers);
    }

    @Override
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('CRM_ADMIN') or #id == authentication.principal.id")
    public ResponseEntity<CustomerDto> getCustomerById(@PathVariable Long id) {
        CustomerDto customer = customerService.findById(id);

        return ResponseEntity.ok(customer);
    }

    @Override
    @GetMapping("/profile")
    public ResponseEntity<CustomerDto> getMyProfile() {
        CustomerDto customer = customerService.getMyProfile();
        return ResponseEntity.ok(customer);
    }

    @Override
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('CRM_ADMIN') or #id == authentication.principal.id")
    public ResponseEntity<CustomerDto> updateCustomer(@PathVariable Long id, @RequestBody CustomerIUDto customerIUDto) {
        return ResponseEntity.ok(customerService.update(id, customerIUDto));
    }

    @Override
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('CRM_ADMIN') or #id == authentication.principal.id")
    public ResponseEntity<Void> deleteCustomer(@PathVariable Long id) {
        customerService.delete(id);

        return ResponseEntity.noContent().build();
    }

    @Override
    @PutMapping("/change-password/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('CRM_ADMIN') or #id == authentication.principal.id")
    public ResponseEntity<Void> changePassword(@PathVariable Long id, @RequestBody PasswordChangeDto passwordChangeDto) {
        customerService.changePassword(id, passwordChangeDto);

        return ResponseEntity.ok().build();
    }

    @Override
    @PutMapping("/change-role/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('CRM_ADMIN')")
    public ResponseEntity<?> changeCustomerRole(@PathVariable Long id, @RequestBody Role newRole) {
        customerService.changeCustomerRole(id, newRole);
        return ResponseEntity.ok().build();
    }
}
