package com.backend360.auth_service.controller;

import com.backend360.auth_service.dto.CustomerDto;
import com.backend360.auth_service.dto.CustomerIUDto;
import com.backend360.auth_service.dto.PasswordChangeDto;
import com.backend360.auth_service.model.Role;
import org.springframework.http.ResponseEntity;

import java.util.List;

public interface ICustomerController {
    ResponseEntity<List<CustomerDto>> getAllCustomers();

    ResponseEntity<CustomerDto> getCustomerById(Long id);

    ResponseEntity<CustomerDto> getMyProfile();

    ResponseEntity<CustomerDto> updateCustomer(Long id, CustomerIUDto customerIUDto);

    ResponseEntity<Void> deleteCustomer(Long id);

    ResponseEntity<Void> changePassword(Long id, PasswordChangeDto passwordChangeDto);

    ResponseEntity<?> changeCustomerRole(Long id, Role newRole);
}
