package com.backend360.auth_service.controller;

import com.backend360.auth_service.service.ICustomerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.HashMap;

@Slf4j
@RestController
@RequestMapping("/public/customer")
public class PublicCustomerController {

    @Autowired
    private ICustomerService customerService;

    /**
     * Username ile customer ID bulma (form submission için)
     * <PERSON><PERSON><PERSON> ID döner, diğer hassas bilgiler döndürülmez
     */
    @GetMapping("/find-id")
    public ResponseEntity<Map<String, Object>> findCustomerIdByUsername(@RequestParam String username) {
        try {
            log.info("Customer ID aranıyor: {}", username);
            
            // Username veya email ile customer bul
            var customer = customerService.findByUsernameOrEmail(username);
            
            Map<String, Object> response = new HashMap<>();
            if (customer != null) {
                response.put("found", true);
                response.put("customerId", customer.getId());
                response.put("username", customer.getUsername());
                // Hassas bilgileri döndürme (email, phone, vs.)
            } else {
                response.put("found", false);
                response.put("customerId", null);
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Customer ID bulma hatası: {}", e.getMessage(), e);
            return ResponseEntity.status(500)
                .body(Map.of("error", "Customer bilgisi alınırken hata oluştu"));
        }
    }

    /**
     * Customer ID ile duplicate campaign kontrolü
     */
    @GetMapping("/check-campaign-duplicate")
    public ResponseEntity<Map<String, Object>> checkCampaignDuplicate(
            @RequestParam Long customerId, 
            @RequestParam Long campaignId) {
        try {
            // Bu kontrolü Campaign Service'den yapacağız
            // Burada sadece customer'ın var olup olmadığını kontrol edelim
            var customer = customerService.findById(customerId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("customerExists", customer != null);
            
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Customer campaign duplicate check hatası: {}", e.getMessage(), e);
            return ResponseEntity.status(500)
                .body(Map.of("error", "Kontrol sırasında hata oluştu"));
        }
    }
}
