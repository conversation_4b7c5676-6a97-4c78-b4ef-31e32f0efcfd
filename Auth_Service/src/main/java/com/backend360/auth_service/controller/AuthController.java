package com.backend360.auth_service.controller;

import com.backend360.auth_service.dto.AuthRequest;
import com.backend360.auth_service.dto.RefreshTokenRequest;
import com.backend360.auth_service.service.AuthService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/auth")
public class AuthController {

    @Autowired
    private AuthService authService;

    @PostMapping("/create-user")
    public ResponseEntity<?> createUser(@RequestBody AuthRequest authRequest) {
        return authService.createUser(authRequest);
    }

    @PostMapping("/login")
    public ResponseEntity<?> login(@RequestBody AuthRequest authRequest, HttpServletResponse response) {
        return authService.login(authRequest, response);
    }

    @PostMapping("/access-token")
    public ResponseEntity<?> getAccessToken(@RequestBody(required = false) RefreshTokenRequest refreshTokenRequest, 
                                          @CookieValue(name = "refreshToken", required = false) String refreshTokenCookie,
                                          HttpServletResponse response) {
        String refreshToken = null;
        
        // Önce request body'den al, yoksa cookie'den al
        if (refreshTokenRequest != null && refreshTokenRequest.getRefreshToken() != null) {
            refreshToken = refreshTokenRequest.getRefreshToken();
        } else if (refreshTokenCookie != null) {
            refreshToken = refreshTokenCookie;
        }
        
        if (refreshToken == null) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Refresh token not found");
        }
        
        return authService.getAccessToken(refreshToken, response);
    }

    @GetMapping("/current-user")
    public ResponseEntity<?> getCurrentUserInfo() {
        return authService.getCurrentUser();
    }

    @PostMapping("/logout")
    public ResponseEntity<?> logout(HttpServletResponse response) {
        return authService.logout(response);
    }
}
