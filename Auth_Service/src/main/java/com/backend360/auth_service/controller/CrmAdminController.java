package com.backend360.auth_service.controller;

import com.backend360.auth_service.dto.CustomerDto;
import com.backend360.auth_service.service.ICustomerService;
import com.backend360.auth_service.service.CrmAdminService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import org.springframework.web.bind.annotation.*;
import jakarta.servlet.http.HttpServletRequest;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * CRM Admin Controller
 * CRM_ADMIN role'üne sahip kullanıcılar için özel endpoint'ler
 * Sadece CRM işlemleri yapabilir, campaign işlemleri yapamaz
 */
@Slf4j
@RestController
@RequestMapping("/crm-admin")
public class CrmAdminController {

    @Autowired
    private ICustomerService customerService;

    @Autowired
    private CrmAdminService crmAdminService;

    @GetMapping("/dashboard")
    public ResponseEntity<String> crmAdminDashboard() {
        log.info("CRM Admin dashboard accessed");
        return ResponseEntity.ok("CRM Admin Dashboard - Hoşgeldiniz!");
    }

    @GetMapping("/test-connection")
    public ResponseEntity<String> testCrmApiConnection() {
        try {
            log.info("CRM Admin - CRM-API bağlantısı test ediliyor");
            Map<String, Object> response = crmAdminService.getAllContacts();
            Object content = response.get("content");
            int size = content instanceof List ? ((List<?>) content).size() : 0;
            return ResponseEntity.ok("CRM-API bağlantısı başarılı! " + size + " contact bulundu.");
        } catch (Exception e) {
            log.error("CRM-API bağlantı hatası: {}", e.getMessage());
            return ResponseEntity.ok("CRM-API bağlantı hatası: " + e.getMessage());
        }
    }

    // CRM-API Proxy Endpoints
    @GetMapping("/crm/contacts")
    public ResponseEntity<Map<String, Object>> getAllContacts(HttpServletRequest request) {
        try {
            log.info("CRM Admin - Tüm contact'lar isteniyor");

            // Debug: Request header'larını logla
            String authHeader = request.getHeader("Authorization");
            log.info("Authorization Header: {}", authHeader != null ? "Bearer token mevcut" : "Token yok");

            Map<String, Object> contacts = crmAdminService.getAllContacts();
            Object content = contacts.get("content");
            int size = content instanceof List ? ((List<?>) content).size() : 0;
            log.info("Contact'lar başarıyla getirildi: {} adet", size);
            return ResponseEntity.ok(contacts);
        } catch (Exception e) {
            log.error("Contact'lar getirilemedi: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Collections.emptyMap());
        }
    }

    @GetMapping("/crm/contacts/{uuid}")
    public ResponseEntity<Map<String, Object>> getContactById(@PathVariable String uuid) {
        try {
            log.info("CRM Admin - Contact detayı isteniyor: {}", uuid);
            Map<String, Object> contact = crmAdminService.getContactById(uuid);
            return ResponseEntity.ok(contact);
        } catch (Exception e) {
            log.error("Contact detayı getirilemedi: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Collections.emptyMap());
        }
    }

    @PostMapping("/crm/contacts")
    public ResponseEntity<Map<String, Object>> createContact(@RequestBody Map<String, Object> contactData) {
        try {
            log.info("CRM Admin - Yeni contact oluşturuluyor");
            Map<String, Object> contact = crmAdminService.createContact(contactData);
            return ResponseEntity.ok(contact);
        } catch (Exception e) {
            log.error("Contact oluşturulamadı: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Collections.emptyMap());
        }
    }

    @PutMapping("/crm/contacts/{uuid}")
    public ResponseEntity<Map<String, Object>> updateContact(@PathVariable String uuid, @RequestBody Map<String, Object> contactData) {
        try {
            log.info("CRM Admin - Contact güncelleniyor: {}", uuid);
            Map<String, Object> contact = crmAdminService.updateContact(uuid, contactData);
            return ResponseEntity.ok(contact);
        } catch (Exception e) {
            log.error("Contact güncellenemedi: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Collections.emptyMap());
        }
    }

    @DeleteMapping("/crm/contacts/{uuid}")
    public ResponseEntity<Void> deleteContact(@PathVariable String uuid) {
        try {
            log.info("CRM Admin - Contact siliniyor: {}", uuid);
            crmAdminService.deleteContact(uuid);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Contact silinemedi: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/crm/campaigns")
    public ResponseEntity<List<Map<String, Object>>> getAllCampaigns() {
        try {
            log.info("CRM Admin - Tüm campaign'lar isteniyor");
            List<Map<String, Object>> campaigns = crmAdminService.getAllCampaigns();
            return ResponseEntity.ok(campaigns);
        } catch (Exception e) {
            log.error("Campaign'lar getirilemedi: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Collections.emptyList());
        }
    }

    @GetMapping("/crm/tags")
    public ResponseEntity<List<Map<String, Object>>> getAllTags() {
        try {
            log.info("CRM Admin - Tüm tag'lar isteniyor");
            List<Map<String, Object>> tags = crmAdminService.getAllTags();
            return ResponseEntity.ok(tags);
        } catch (Exception e) {
            log.error("Tag'lar getirilemedi: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Collections.emptyList());
        }
    }

    @GetMapping("/crm/calls")
    public ResponseEntity<List<Map<String, Object>>> getAllCalls() {
        try {
            log.info("CRM Admin - Tüm call'lar isteniyor");
            List<Map<String, Object>> calls = crmAdminService.getAllCalls();
            return ResponseEntity.ok(calls);
        } catch (Exception e) {
            log.error("Call'lar getirilemedi: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Collections.emptyList());
        }
    }

    @GetMapping("/customers")
    public ResponseEntity<List<CustomerDto>> getAllCustomers() {
        log.info("CRM Admin - Tüm müşteriler listeleniyor");
        List<CustomerDto> customers = customerService.findAll();
        return ResponseEntity.ok(customers);
    }

    @GetMapping("/customers/{id}")
    public ResponseEntity<CustomerDto> getCustomerById(@PathVariable Long id) {
        log.info("CRM Admin - Müşteri detayı getiriliyor: {}", id);
        CustomerDto customer = customerService.findById(id);
        return ResponseEntity.ok(customer);
    }

    @GetMapping("/profile")
    public ResponseEntity<String> getCrmAdminProfile() {
        log.info("CRM Admin profile accessed");
        return ResponseEntity.ok("CRM Admin Profile - Sadece CRM işlemleri yapabilirsiniz");
    }

    // CRM Admin sadece müşteri bilgilerini görüntüleyebilir
    // Campaign işlemleri için yetki yok
    @GetMapping("/permissions")
    public ResponseEntity<String> getCrmAdminPermissions() {
        return ResponseEntity.ok("CRM Admin Yetkileri: Müşteri görüntüleme, CRM işlemleri. Campaign işlemleri yasak.");
    }

    // Webhook Events Endpoints
    @GetMapping("/crm/webhook-events")
    public ResponseEntity<List<Map<String, Object>>> getAllWebhookEvents(HttpServletRequest request) {
        try {
            log.info("CRM Admin - Tüm webhook event'lar isteniyor");

            String authHeader = request.getHeader("Authorization");
            log.info("Authorization Header: {}", authHeader != null ? "Bearer token mevcut" : "Token yok");

            List<Map<String, Object>> events = crmAdminService.getAllWebhookEvents();
            log.info("Webhook event'lar başarıyla getirildi: {} adet", events.size());
            return ResponseEntity.ok(events);
        } catch (Exception e) {
            log.error("Webhook event'lar getirilemedi: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Collections.emptyList());
        }
    }

    @GetMapping("/crm/webhook-events/{id}")
    public ResponseEntity<Map<String, Object>> getWebhookEventById(@PathVariable String id, HttpServletRequest request) {
        try {
            log.info("CRM Admin - Webhook event detayı isteniyor: {}", id);

            String authHeader = request.getHeader("Authorization");
            log.info("Authorization Header: {}", authHeader != null ? "Bearer token mevcut" : "Token yok");

            Map<String, Object> event = crmAdminService.getWebhookEventById(UUID.fromString(id));
            return ResponseEntity.ok(event);
        } catch (Exception e) {
            log.error("Webhook event detayı getirilemedi: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
}
