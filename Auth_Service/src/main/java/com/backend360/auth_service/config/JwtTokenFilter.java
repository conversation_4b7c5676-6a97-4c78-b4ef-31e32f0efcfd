package com.backend360.auth_service.config;

import com.backend360.auth_service.service.JwtTokenService;
import com.backend360.auth_service.service.UserDetailsServiceImpl;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Arrays;
import java.util.Optional;

@Component
public class JwtTokenFilter extends OncePerRequestFilter {
    @Autowired
    private JwtTokenService jwtTokenService;

    @Autowired
    private UserDetailsServiceImpl userDetailsService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        // JWT validation is Skipped for these public endpoints
        String path = request.getRequestURI();
        String method = request.getMethod();
        
        if (path.startsWith("/auth/login") || 
            path.startsWith("/auth/create-user") ||
            path.startsWith("/auth/access-token") || 
            path.startsWith("/auth/user") ||
            path.startsWith("/auth/current-user") ||
            path.startsWith("/auth/logout") ||
            // Customer endpoint'leri için sadece POST metodlarında JWT kontrolü atlanır
            ("POST".equals(method) && path.startsWith("/customer"))) {
            filterChain.doFilter(request, response);
            return;
        }

        // Try to get token from Authorization header
        String token = null;
        String authorizationHeader = request.getHeader("Authorization");
        
        if (authorizationHeader != null && authorizationHeader.startsWith("Bearer ")) {
            token = authorizationHeader.substring(7);
        } else {
            // If not in header, try to get from cookies
            Cookie[] cookies = request.getCookies();
            if (cookies != null) {
                Optional<Cookie> accessTokenCookie = Arrays.stream(cookies)
                    .filter(cookie -> "accessToken".equals(cookie.getName()))
                    .findFirst();
                
                if (accessTokenCookie.isPresent()) {
                    token = accessTokenCookie.get().getValue();
                }
            }
        }

        // Process token if found
        if (token != null) {
            String username = jwtTokenService.getUsernameFromToken(token);

            if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                UserDetails userDetails = userDetailsService.loadUserByUsername(username);
                if (jwtTokenService.isValidAccessToken(token, userDetails.getUsername())) {
                    // Customer objesini Authentication'a set et
                    UsernamePasswordAuthenticationToken authenticationToken =
                            new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());

                    authenticationToken.setDetails(
                            new WebAuthenticationDetailsSource().buildDetails(request)
                    );

                    SecurityContextHolder.getContext().setAuthentication(authenticationToken);
                }
            }
        }

        filterChain.doFilter(request, response);
    }
}

