package com.backend360.auth_service.config;

import com.backend360.auth_service.service.UserDetailsServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
public class SecurityConfig {
    @Autowired
    private JwtTokenFilter jwtTokenFilter;

    @Autowired
    private UserDetailsServiceImpl userDetailsServiceImp;

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http.csrf(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests(req -> req
                    // Public customer endpoints - Sadece form submission için
                    .requestMatchers("/public/customer/**").permitAll()

                    // GET /customer (tüm liste) - sadece ADMIN ve CRM_ADMIN
                    .requestMatchers(HttpMethod.GET, "/customer").hasAnyRole("ADMIN", "CRM_ADMIN")

                    // GET /customer/{id} - herkes kendi id'si için (controller'da @PreAuthorize ile kontrol)
                    .requestMatchers(HttpMethod.GET, "/customer/*").authenticated()

                    // POST /customer - Kayıt işlemi için herkese açık
                    .requestMatchers(HttpMethod.POST, "/customer").permitAll()

                    // PUT /customer/{id} - Kendi bilgisini güncelleyebilir (controller'da @PreAuthorize ile kontrol)
                    .requestMatchers(HttpMethod.PUT, "/customer/*").authenticated()

                    // DELETE /customer - sadece ADMIN ve CRM_ADMIN (customer silme)
                    .requestMatchers(HttpMethod.DELETE, "/customer/**").hasAnyRole("ADMIN", "CRM_ADMIN")
                    .requestMatchers(HttpMethod.PUT, "/customer/**").permitAll()
                    .requestMatchers(HttpMethod.DELETE, "/customer/**").permitAll()
                    
                    // CRM Admin endpoints - sadece authenticated kullanıcılar
                    .requestMatchers("/crm-admin/**").authenticated()

                    // Auth endpoints herkese açık
                    .requestMatchers("/auth/**").permitAll()

                    // Diğer tüm endpoint'ler herkese açık
                    .anyRequest().permitAll()
                )
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .addFilterBefore(jwtTokenFilter, UsernamePasswordAuthenticationFilter.class);
        return http.build();
    }

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration authenticationConfiguration) throws Exception {
        return authenticationConfiguration.getAuthenticationManager();
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
