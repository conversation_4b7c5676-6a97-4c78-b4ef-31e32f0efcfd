package com.backend360.auth_service.repository;

import com.backend360.auth_service.model.Customer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ICustomerRepository extends JpaRepository<Customer, Long> {
    Optional<Customer> findByUsername(String username);
    Optional<Customer> findByEmail(String email);
    boolean existsByEmail(String email);
    boolean existsByUsername(String username);

    // Public form submission için yeni metod
    Customer findByUsernameOrEmail(String username, String email);
} 