package com.backend360.auth_service.service;

import com.backend360.auth_service.dto.CustomerDto;
import com.backend360.auth_service.dto.CustomerIUDto;
import com.backend360.auth_service.dto.PasswordChangeDto;
import com.backend360.auth_service.model.Role;

import java.util.List;


public interface ICustomerService {
    List<CustomerDto> findAll();

    CustomerDto findById(Long id);

    CustomerDto findByUsername(String username);

    CustomerDto getMyProfile();

    CustomerDto update(Long id, CustomerIUDto customerIUDto);

    void delete(Long id);

    void changePassword(Long id, PasswordChangeDto passwordChangeDto);

    void changeCustomerRole(Long id, Role newRole);

    // Public form submission için yeni metodlar
    CustomerDto findByUsernameOrEmail(String usernameOrEmail);
}
