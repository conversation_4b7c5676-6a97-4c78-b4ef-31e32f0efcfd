package com.backend360.auth_service.service;

import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.SecretKey;
import java.util.Date;

/**
 * Servisler arası iletişim için internal token service
 * Bu tokenlar sadece backend servisleri arasında kullanılır
 */
@Slf4j
@Service
public class InternalServiceTokenService {

    @Value("${internal.service.secret-key:internal-service-secret-key-2025}")
    private String internalSecretKey;

    @Value("${internal.service.token.expiration:3600000}") // 1 saat
    private long tokenExpiration;

    private SecretKey getSigningKey() {
        return Keys.hmacShaKeyFor(internalSecretKey.getBytes());
    }

    /**
     * Backend2 servisinin CRM-API'ye erişimi için internal token oluştur
     */
    public String generateInternalServiceToken(String serviceName, String userRole, String username) {
        return Jwts.builder()
                .subject("internal-service")
                .claim("service", serviceName)
                .claim("user_role", userRole)
                .claim("username", username)
                .claim("internal", true)
                .issuedAt(new Date(System.currentTimeMillis()))
                .expiration(new Date(System.currentTimeMillis() + tokenExpiration))
                .signWith(getSigningKey())
                .compact();
    }

    /**
     * CRM_Admin kullanıcısı için CRM-API erişim token'ı oluştur
     */
    public String generateCrmAccessToken(String username) {
        log.debug("Generating CRM access token for user: {}", username);
        String token = generateInternalServiceToken("backend2-auth-service", "CRM_ADMIN", username);
        log.info("Generated CRM access token for user: {} (first 50 chars): {}",
                username, token.substring(0, Math.min(50, token.length())));
        return token;
    }
}
