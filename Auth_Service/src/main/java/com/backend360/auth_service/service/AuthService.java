package com.backend360.auth_service.service;

import com.backend360.auth_service.dto.AuthRequest;
import com.backend360.auth_service.dto.AuthResponse;
import com.backend360.auth_service.dto.ErrorDto;
import com.backend360.auth_service.model.Customer;
import com.backend360.auth_service.repository.ICustomerRepository;
import com.backend360.auth_service.repository.IJobRepository;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class AuthService {
    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private JwtTokenService jwtTokenService;

    @Autowired
    private ICustomerRepository customerRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Value("${security.jwt.access-token-expiration}")
    private long accessTokenExpire;

    @Value("${security.jwt.refresh-token-expiration}")
    private long refreshTokenExpire;

    @Value("${app.cookie.secure:true}")
    private boolean cookieSecure;

    @Value("${app.cookie.same-site:Strict}")
    private String cookieSameSite;

    public ResponseEntity<?> createUser(AuthRequest authRequest) {
        try {
            if(customerRepository.existsByEmail(authRequest.getEmail())) {
                ErrorDto error = new ErrorDto(
                    "Girilen e-mail ile kayıtlı kullancı bulunmaktadır",
                    HttpStatus.CONFLICT.value(),
                    LocalDateTime.now(ZoneId.of("Europe/Istanbul")).format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
                );
                return new ResponseEntity<>(error, HttpStatus.CONFLICT);
            }

            if(customerRepository.existsByUsername(authRequest.getUsername())){
                ErrorDto error = new ErrorDto(
                    "Girilen kullanıcı adı ile kayıtlı kullancı bulunmaktadır",
                    HttpStatus.CONFLICT.value(),
                    LocalDateTime.now(ZoneId.of("Europe/Istanbul")).format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
                );
                return new ResponseEntity<>(error, HttpStatus.CONFLICT);
            }

            Customer customer = Customer.builder()
                    .name(authRequest.getName())
                    .surname(authRequest.getSurname())
                    .email(authRequest.getEmail())
                    .username(authRequest.getUsername())
                    .role(authRequest.getRole())
                    .password(passwordEncoder.encode(authRequest.getPassword()))
                    .isActive(true)
                    .remindMe(true)
                    .createdAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")))
                    .updatedAt(null)
                    .build();

            Customer savedCustomer = customerRepository.save(customer);
            savedCustomer.setPassword(null); // Remove password from response

            Map<String, Object> response = new HashMap<>();
            response.put("message", "SUCCESS");
            response.put("data", savedCustomer);
            response.put("httpStatus", HttpStatus.CREATED);

            return new ResponseEntity<>(response, HttpStatus.CREATED);
        } catch (Exception e) {
            log.error("Kullanıcı oluşturulurken hata oluştu: {}", e.getMessage(), e);
            ErrorDto error = new ErrorDto(
                "Internal error!",
                HttpStatus.INTERNAL_SERVER_ERROR.value(),
                LocalDateTime.now(ZoneId.of("Europe/Istanbul")).format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
            );
            return new ResponseEntity<>(error, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public ResponseEntity<?> login(AuthRequest request, HttpServletResponse response) {
        try {
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(request.getUsername(), request.getPassword()));

            SecurityContextHolder.getContext().setAuthentication(authentication);

            Customer customer = (Customer) authentication.getPrincipal();

            customer.setLastLogin(LocalDateTime.now(ZoneId.of("Europe/Istanbul")));
            customerRepository.save(customer);

            String accessToken = jwtTokenService.generateAccessToken(customer);
            String refreshToken = jwtTokenService.generateRefreshToken(customer);

            // Set cookies
            addAccessTokenCookie(response, accessToken);
            addRefreshTokenCookie(response, refreshToken);

            return new ResponseEntity<>(new AuthResponse(accessToken, refreshToken), HttpStatus.OK);
        } catch (AuthenticationException e) {
            log.error("Authentication failed: {}", e.getMessage());
            ErrorDto error = new ErrorDto(
                "Kullanıcı adı veya şifre hatalı",
                HttpStatus.UNAUTHORIZED.value(),
                LocalDateTime.now(ZoneId.of("Europe/Istanbul")).format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
            );
            return new ResponseEntity<>(error, HttpStatus.UNAUTHORIZED);
        } catch (Exception e) {
            log.error("Internal error: {}", e.getMessage(), e);
            ErrorDto error = new ErrorDto(
                "Internal error!",
                HttpStatus.INTERNAL_SERVER_ERROR.value(),
                LocalDateTime.now(ZoneId.of("Europe/Istanbul")).format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
            );
            return new ResponseEntity<>(error, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public ResponseEntity<?> getAccessToken(String refreshToken, HttpServletResponse response) {
        try {
            String username = jwtTokenService.getUsernameFromToken(refreshToken);

            Optional<Customer> customerOpt = customerRepository.findByUsername(username);
            if (customerOpt.isPresent()) {
                Customer customer = customerOpt.get();
                if (jwtTokenService.isValidRefreshToken(refreshToken, customer.getUsername())) {
                    String newAccessToken = jwtTokenService.generateAccessToken(customer);
                    addAccessTokenCookie(response, newAccessToken);
                    return ResponseEntity.ok(new AuthResponse(newAccessToken, refreshToken));
                }
            }

            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        } catch (Exception e) {
            log.error("Error refreshing token: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    private void addAccessTokenCookie(HttpServletResponse response, String token) {
        Cookie cookie = new Cookie("accessToken", token);
        cookie.setMaxAge((int) (accessTokenExpire / 1000)); // Convert milliseconds to seconds
        cookie.setPath("/");
        cookie.setHttpOnly(false); // Frontend'den erişim için
        cookie.setSecure(cookieSecure); // Environment-based: true for production, false for development
        if (!"Strict".equals(cookieSameSite)) {
            cookie.setAttribute("SameSite", cookieSameSite);
        }
        response.addCookie(cookie);
    }

    private void addRefreshTokenCookie(HttpServletResponse response, String token) {
        Cookie cookie = new Cookie("refreshToken", token);
        cookie.setMaxAge((int) (refreshTokenExpire / 1000)); // Convert milliseconds to seconds
        cookie.setPath("/");
        cookie.setHttpOnly(false); // Frontend'den erişim için
        cookie.setSecure(cookieSecure); // Environment-based: true for production, false for development
        if (!"Strict".equals(cookieSameSite)) {
            cookie.setAttribute("SameSite", cookieSameSite);
        }
        response.addCookie(cookie);
    }

    public ResponseEntity<?> getCurrentUser() {
        try {
            String username = SecurityContextHolder.getContext().getAuthentication().getName();

            Optional<Customer> customerOpt = customerRepository.findByUsername(username);
            if (customerOpt.isPresent()) {
                Customer customer = customerOpt.get();
                customer.setPassword(null); // Remove sensitive information
                return ResponseEntity.ok(customer);
            }

            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("User not found");
        } catch (Exception e) {
            log.error("Error getting current user: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error getting user information");
        }
    }

    public ResponseEntity<?> logout(HttpServletResponse response) {
        try {
            // Clear SecurityContext
            SecurityContextHolder.clearContext();
            
            // Delete cookies by setting Max-Age=0
            clearAccessTokenCookie(response);
            clearRefreshTokenCookie(response);
            
            log.info("User logged out successfully");
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("message", "Logout successful");
            responseData.put("httpStatus", HttpStatus.OK);
            
            return new ResponseEntity<>(responseData, HttpStatus.OK);
        } catch (Exception e) {
            log.error("Error during logout: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error during logout");
        }
    }

    private void clearAccessTokenCookie(HttpServletResponse response) {
        Cookie cookie = new Cookie("accessToken", "");
        cookie.setMaxAge(0); // Set Max-Age=0 to delete the cookie
        cookie.setPath("/");
        cookie.setHttpOnly(true);
        cookie.setSecure(cookieSecure); // Environment-based: true for production, false for development
        if (!"Strict".equals(cookieSameSite)) {
            cookie.setAttribute("SameSite", cookieSameSite);
        }
        response.addCookie(cookie);
        log.debug("AccessToken cookie cleared");
    }

    private void clearRefreshTokenCookie(HttpServletResponse response) {
        Cookie cookie = new Cookie("refreshToken", "");
        cookie.setMaxAge(0); // Set Max-Age=0 to delete the cookie
        cookie.setPath("/");
        cookie.setHttpOnly(true);
        cookie.setSecure(cookieSecure); // Environment-based: true for production, false for development
        if (!"Strict".equals(cookieSameSite)) {
            cookie.setAttribute("SameSite", cookieSameSite);
        }
        response.addCookie(cookie);
        log.debug("RefreshToken cookie cleared");
    }
}
