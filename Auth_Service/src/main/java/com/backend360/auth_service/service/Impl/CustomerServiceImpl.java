package com.backend360.auth_service.service.Impl;

import com.backend360.auth_service.dto.CustomerDto;
import com.backend360.auth_service.dto.CustomerIUDto;
import com.backend360.auth_service.dto.PasswordChangeDto;
import com.backend360.auth_service.exception.AlreadyExistsException;
import com.backend360.auth_service.exception.BadRequestException;
import com.backend360.auth_service.exception.CustomNotFoundException;
import com.backend360.auth_service.model.Customer;
import com.backend360.auth_service.model.Job;
import com.backend360.auth_service.model.Role;
import com.backend360.auth_service.repository.ICustomerRepository;
import com.backend360.auth_service.repository.IJobRepository;
import com.backend360.auth_service.service.ICustomerService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CustomerServiceImpl implements ICustomerService {
    @Autowired
    private ICustomerRepository customerRepository;

    @Autowired
    private IJobRepository jobRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    // Email regex pattern
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$"
    );

    @Override
    public List<CustomerDto> findAll() {
        log.info("Tüm müşteriler listeleniyor");
        List<Customer> customers = customerRepository.findAll();
        log.debug("Toplam {} müşteri bulundu", customers.size());

        return customers.stream()
                .map(CustomerDto::entityToDto)
                .collect(Collectors.toList());
    }

    @Override
    public CustomerDto findById(Long id) {
        log.info("ID'si {} olan müşteri aranıyor", id);
        Customer customer = customerRepository.findById(id)
                .orElseThrow(() -> {
                    
                    return new CustomNotFoundException("Müşteri ID " + id + " bulunamadı");
                });
        log.debug("ID'si {} olan müşteri bulundu", id);
        return CustomerDto.entityToDto(customer);
    }

    @Override
    public CustomerDto findByUsername(String username) {
       
        Customer customer = customerRepository.findByUsername(username)
                .orElseThrow(() -> {
                    
                    return new CustomNotFoundException("Müşteri username " + username + " bulunamadı");
                });
       
        return CustomerDto.entityToDto(customer);
    }

    @Override
    public CustomerDto getMyProfile() {
        log.info("Mevcut kullanıcının profili getiriliyor");
        
        // Spring Security'den mevcut kullanıcıyı al
        String currentUsername = org.springframework.security.core.context.SecurityContextHolder
                .getContext()
                .getAuthentication()
                .getName();
        
        log.debug("Mevcut kullanıcı: {}", currentUsername);
        
        Customer customer = customerRepository.findByUsername(currentUsername)
                .orElseThrow(() -> {
                    log.error("Mevcut kullanıcı bulunamadı: {}", currentUsername);
                    return new CustomNotFoundException("Mevcut kullanıcı bulunamadı");
                });
        
        log.debug("Kullanıcı profili başarıyla getirildi: {}", currentUsername);
        return CustomerDto.entityToDto(customer);
    }

    @Override
    public CustomerDto update(Long id, CustomerIUDto customerIUDto) {
        try {
            log.info("ID'si {} olan müşteri güncelleniyor", id);
            
            // Temel validasyonlar
            if (customerIUDto.getName() == null || customerIUDto.getName().isBlank()) {
                log.warn("Müşteri adı boş");
                throw new BadRequestException("Müşteri adı boş olamaz.");
            }

            if (customerIUDto.getSurname() == null || customerIUDto.getSurname().isBlank()) {
                log.warn("Müşteri soyadı boş");
                throw new BadRequestException("Müşteri soyadı boş olamaz.");
            }

            if (customerIUDto.getEmail() == null || customerIUDto.getEmail().isBlank()) {
                log.warn("Email adresi boş");
                throw new BadRequestException("Email adresi boş olamaz.");
            }

            // Email format kontrolü
            if (!EMAIL_PATTERN.matcher(customerIUDto.getEmail()).matches()) {
                log.warn("Geçersiz email formatı: {}", customerIUDto.getEmail());
                throw new BadRequestException("Geçerli bir email adresi giriniz.");
            }

            if (customerIUDto.getUsername() == null || customerIUDto.getUsername().isBlank()) {
                log.warn("Kullanıcı adı boş");
                throw new BadRequestException("Kullanıcı adı boş olamaz.");
            }

            Job job = null;
            if (customerIUDto.getJobId() != null) {
                log.debug("Meslek ID {} aranıyor", customerIUDto.getJobId());
                job = jobRepository.findById(customerIUDto.getJobId())
                        .orElseThrow(() -> {
                            log.error("Meslek ID {} bulunamadı", customerIUDto.getJobId());
                            return new CustomNotFoundException("Meslek ID " + customerIUDto.getJobId() + " bulunamadı");
                        });
            }

            log.debug("Müşteri ID {} aranıyor", id);
            Customer customer = customerRepository.findById(id)
                    .orElseThrow(() -> {
                        log.error("Müşteri ID {} bulunamadı", id);
                        return new CustomNotFoundException("Müşteri ID " + id + " bulunamadı");
                    });

            // Email ve username unique kontrolü (sadece değiştirilmişse)
            if (!customer.getEmail().equals(customerIUDto.getEmail()) &&
                    customerRepository.existsByEmail(customerIUDto.getEmail())) {
                log.warn("Email adresi zaten kayıtlı: {}", customerIUDto.getEmail());
                throw new AlreadyExistsException("Bu email adresi zaten kayıtlı: " + customerIUDto.getEmail());
            }

            if (!customer.getUsername().equals(customerIUDto.getUsername()) &&
                    customerRepository.existsByUsername(customerIUDto.getUsername())) {
                log.warn("Kullanıcı adı zaten kayıtlı: {}", customerIUDto.getUsername());
                throw new AlreadyExistsException("Bu kullanıcı adı zaten kayıtlı: " + customerIUDto.getUsername());
            }

            customer.setName(customerIUDto.getName());
            customer.setSurname(customerIUDto.getSurname());
            customer.setPhoneNumber(customerIUDto.getPhoneNumber());
            customer.setCountry(customerIUDto.getCountry());
            customer.setCity(customerIUDto.getCity());
            customer.setTown(customerIUDto.getTown());
            customer.setIsActive(customerIUDto.getIsActive());
            customer.setGender(customerIUDto.getGender());
            customer.setBirthday(customerIUDto.getBirthday());
            customer.setJob(job);
            customer.setRemindMe(customerIUDto.getRemindMe());
            customer.setRole(customerIUDto.getRole());
            customer.setUpdatedAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")));
            customer.setUsername(customerIUDto.getUsername());
            customer.setEmail(customerIUDto.getEmail());


            log.debug("Müşteri bilgileri güncellendi, kaydediliyor");
            customer = customerRepository.save(customer);
            log.info("ID'si {} olan müşteri başarıyla güncellendi", id);

            return CustomerDto.entityToDto(customer);
        } catch (Exception e) {
            log.error("Kullanıcı güncellenirken hata oluştu: {}", e.getMessage(), e);
            throw new BadRequestException("Kullanıcı güncellenirken hata oluştu");
        }
    }

    @Override
    public void delete(Long id) {
        log.info("ID'si {} olan müşteri siliniyor (pasif hale getiriliyor)", id);
        Customer customer = customerRepository.findById(id)
                .orElseThrow(() -> {
                    log.error("ID'si {} olan müşteri bulunamadı", id);
                    return new CustomNotFoundException("Müşteri ID " + id + " bulunamadı");
                });

        customer.setIsActive(false);
        customer.setUpdatedAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")));
        customerRepository.save(customer);
        log.info("ID'si {} olan müşteri başarıyla pasif hale getirildi", id);
    }

    @Override
    public void changePassword(Long id, PasswordChangeDto passwordChangeDto) {
        log.info("ID'si {} olan müşterinin şifresi değiştiriliyor", id);
        
        // Validasyonlar
        if (passwordChangeDto.getCurrentPassword() == null || passwordChangeDto.getCurrentPassword().isBlank()) {
            log.warn("Mevcut şifre boş");
            throw new BadRequestException("Mevcut şifre boş olamaz.");
        }

        if (passwordChangeDto.getNewPassword() == null || passwordChangeDto.getNewPassword().isBlank()) {
            log.warn("Yeni şifre boş");
            throw new BadRequestException("Yeni şifre boş olamaz.");
        }

        if (passwordChangeDto.getConfirmPassword() == null || passwordChangeDto.getConfirmPassword().isBlank()) {
            log.warn("Şifre onayı boş");
            throw new BadRequestException("Şifre onayı boş olamaz.");
        }

        if (!passwordChangeDto.getNewPassword().equals(passwordChangeDto.getConfirmPassword())) {
            log.warn("Yeni şifre ve şifre onayı eşleşmiyor");
            throw new BadRequestException("Yeni şifre ve şifre onayı eşleşmiyor.");
        }

        // Müşteriyi bul
        log.debug("Müşteri ID {} aranıyor", id);
        Customer customer = customerRepository.findById(id)
                .orElseThrow(() -> {
                    log.error("Müşteri ID {} bulunamadı", id);
                    return new CustomNotFoundException("Müşteri ID " + id + " bulunamadı");
                });

        // Mevcut şifreyi kontrol et
        if (!passwordEncoder.matches(passwordChangeDto.getCurrentPassword(), customer.getPassword())) {
            log.warn("Mevcut şifre yanlış");
            throw new BadRequestException("Mevcut şifre yanlış.");
        }

        // Yeni şifreyi güncelle
        log.debug("Şifre güncelleniyor");
        customer.setPassword(passwordEncoder.encode(passwordChangeDto.getNewPassword()));
        customer.setUpdatedAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")));
        customerRepository.save(customer);

        log.info("ID'si {} olan müşterinin şifresi başarıyla değiştirildi", id);
    }

    @Override
    public void changeCustomerRole(Long id, Role newRole) {
        log.info("ID'si {} olan müşterinin rolü {} olarak değiştiriliyor", id, newRole);

        // Validasyon
        if (newRole == null) {
            log.warn("Yeni rol null olamaz");
            throw new BadRequestException("Yeni rol null olamaz");
        }

        // Müşteriyi bul
        log.debug("Müşteri ID {} aranıyor", id);
        Customer customer = customerRepository.findById(id)
                .orElseThrow(() -> {
                    log.error("Müşteri ID {} bulunamadı", id);
                    return new CustomNotFoundException("Müşteri ID " + id + " bulunamadı");
                });

        Role currentRole = customer.getRole();
        log.debug("Müşterinin mevcut rolü: {}, yeni rol: {}", currentRole, newRole);

        // Rolü güncelle
        customer.setRole(newRole);
        customer.setUpdatedAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")));
        customerRepository.save(customer);

        log.info("ID'si {} olan müşterinin rolü {} olarak başarıyla değiştirildi", id, newRole);
    }

    @Override
    public CustomerDto findByUsernameOrEmail(String usernameOrEmail) {
        log.info("Username veya email ile müşteri aranıyor: {}", usernameOrEmail);

        Customer customer = customerRepository.findByUsernameOrEmail(usernameOrEmail, usernameOrEmail);
        if (customer == null) {
            log.warn("Username veya email ile müşteri bulunamadı: {}", usernameOrEmail);
            return null;
        }

        log.info("Username veya email ile müşteri bulundu: {}", customer.getUsername());
        return CustomerDto.entityToDto(customer);
    }
}
