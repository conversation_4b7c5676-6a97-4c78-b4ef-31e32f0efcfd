package com.backend360.auth_service.service;

import com.backend360.auth_service.client.CrmApiClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import jakarta.servlet.http.HttpServletRequest;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * CRM Admin Service
 * CRM-API ile iletişim kurar ve CRM işlemlerini yönetir
 */
@Slf4j
@Service
public class CrmAdminService {

    @Autowired
    private CrmApiClient crmApiClient;

    @Value("${crm.api.token:default-token}")
    private String crmApiToken;

    private String getAuthHeader() {
        // Mevcut request'ten JWT token'ı al ve forward et
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String authHeader = request.getHeader("Authorization");
                if (authHeader != null && authHeader.startsWith("Bearer ")) {
                    log.debug("Forwarding JWT token from current request to CRM-API");
                    return authHeader;
                } else {
                    log.warn("No Authorization header found in request");
                }
            } else {
                log.warn("No request attributes found");
            }
        } catch (Exception e) {
            log.error("Error getting JWT token from current request: {}", e.getMessage(), e);
        }

        // Token bulunamazsa authentication hatası ver
        log.error("No valid JWT token found in request. User must be authenticated.");
        throw new RuntimeException("Authentication required. Please login first.");
    }

    // Contact işlemleri
    public Map<String, Object> getAllContacts() {
        try {
            log.info("CRM-API'den tüm contact'lar getiriliyor");
            ResponseEntity<Map<String, Object>> response = crmApiClient.getAllContacts(getAuthHeader(), 1000);
            return response.getBody();
        } catch (Exception e) {
            log.error("Contact'lar getirilirken hata oluştu: {}", e.getMessage());
            throw new RuntimeException("CRM-API'den contact'lar getirilemedi", e);
        }
    }

    public Map<String, Object> getContactById(String uuid) {
        try {
            log.info("CRM-API'den contact detayı getiriliyor: {}", uuid);
            ResponseEntity<Map<String, Object>> response =
                crmApiClient.getContactByUuid(UUID.fromString(uuid), getAuthHeader());
            return response.getBody();
        } catch (Exception e) {
            log.error("CRM-API'den contact detayı getirilemedi: {}", e.getMessage());
            throw new RuntimeException("CRM-API'den contact detayı getirilemedi", e);
        }
    }

    public Map<String, Object> createContact(Map<String, Object> contactData) {
        try {
            log.info("CRM-API'de yeni contact oluşturuluyor");
            ResponseEntity<Map<String, Object>> response =
                crmApiClient.createContact(contactData, getAuthHeader());
            return response.getBody();
        } catch (Exception e) {
            log.error("CRM-API'de contact oluşturulamadı: {}", e.getMessage());
            throw new RuntimeException("CRM-API'de contact oluşturulamadı", e);
        }
    }

    public Map<String, Object> updateContact(String uuid, Map<String, Object> contactData) {
        try {
            log.info("CRM-API'de contact güncelleniyor: {}", uuid);
            ResponseEntity<Map<String, Object>> response =
                crmApiClient.updateContact(UUID.fromString(uuid), contactData, getAuthHeader());
            return response.getBody();
        } catch (Exception e) {
            log.error("CRM-API'de contact güncellenemedi: {}", e.getMessage());
            throw new RuntimeException("CRM-API'de contact güncellenemedi", e);
        }
    }

    public void deleteContact(String uuid) {
        try {
            log.info("CRM-API'de contact siliniyor: {}", uuid);
            crmApiClient.deleteContact(UUID.fromString(uuid), getAuthHeader());
        } catch (Exception e) {
            log.error("CRM-API'de contact silinemedi: {}", e.getMessage());
            throw new RuntimeException("CRM-API'de contact silinemedi", e);
        }
    }

    // Duplicate metodlar kaldırıldı - Aşağıda yenileri var

    // Campaign işlemleri (sadece görüntüleme)
    public List<Map<String, Object>> getAllCampaigns() {
        try {
            log.info("CRM-API'den tüm campaign'lar getiriliyor");
            ResponseEntity<List<Map<String, Object>>> response = crmApiClient.getAllCampaigns(getAuthHeader());
            return response.getBody();
        } catch (Exception e) {
            log.error("Campaign'lar getirilirken hata oluştu: {}", e.getMessage());
            throw new RuntimeException("CRM-API'den campaign'lar getirilemedi", e);
        }
    }

    public Map<String, Object> getCampaignByUuid(UUID uuid) {
        try {
            log.info("CRM-API'den campaign getiriliyor: {}", uuid);
            ResponseEntity<Map<String, Object>> response = crmApiClient.getCampaignByUuid(uuid, getAuthHeader());
            return response.getBody();
        } catch (Exception e) {
            log.error("Campaign getirilirken hata oluştu: {}", e.getMessage());
            throw new RuntimeException("CRM-API'den campaign getirilemedi", e);
        }
    }

    // Tag işlemleri
    public List<Map<String, Object>> getAllTags() {
        try {
            log.info("CRM-API'den tüm tag'lar getiriliyor");
            ResponseEntity<List<Map<String, Object>>> response = crmApiClient.getAllTags(getAuthHeader());
            return response.getBody();
        } catch (Exception e) {
            log.error("Tag'lar getirilirken hata oluştu: {}", e.getMessage());
            throw new RuntimeException("CRM-API'den tag'lar getirilemedi", e);
        }
    }

    public Map<String, Object> createTag(Map<String, Object> tag) {
        try {
            log.info("CRM-API'de yeni tag oluşturuluyor");
            ResponseEntity<Map<String, Object>> response = crmApiClient.createTag(tag, getAuthHeader());
            return response.getBody();
        } catch (Exception e) {
            log.error("Tag oluşturulurken hata oluştu: {}", e.getMessage());
            throw new RuntimeException("CRM-API'de tag oluşturulamadı", e);
        }
    }

    // Call işlemleri
    public List<Map<String, Object>> getAllCalls() {
        try {
            log.info("CRM-API'den tüm call'lar getiriliyor");
            ResponseEntity<List<Map<String, Object>>> response = crmApiClient.getAllCalls(getAuthHeader());
            return response.getBody();
        } catch (Exception e) {
            log.error("Call'lar getirilirken hata oluştu: {}", e.getMessage());
            throw new RuntimeException("CRM-API'den call'lar getirilemedi", e);
        }
    }

    public Map<String, Object> createCall(Map<String, Object> call) {
        try {
            log.info("CRM-API'de yeni call oluşturuluyor");
            ResponseEntity<Map<String, Object>> response = crmApiClient.createCall(call, getAuthHeader());
            return response.getBody();
        } catch (Exception e) {
            log.error("Call oluşturulurken hata oluştu: {}", e.getMessage());
            throw new RuntimeException("CRM-API'de call oluşturulamadı", e);
        }
    }

    // Webhook Event işlemleri
    public List<Map<String, Object>> getAllWebhookEvents() {
        try {
            log.info("CRM-API'den tüm webhook event'lar getiriliyor");
            ResponseEntity<List<Map<String, Object>>> response = crmApiClient.getAllWebhookEvents(getAuthHeader());
            return response.getBody();
        } catch (Exception e) {
            log.error("Webhook event'lar getirilirken hata oluştu: {}", e.getMessage());
            throw new RuntimeException("CRM-API'den webhook event'lar getirilemedi", e);
        }
    }

    public Map<String, Object> getWebhookEventById(UUID id) {
        try {
            log.info("CRM-API'den webhook event detayı getiriliyor: {}", id);
            ResponseEntity<Map<String, Object>> response = crmApiClient.getWebhookEventById(id, getAuthHeader());
            return response.getBody();
        } catch (Exception e) {
            log.error("CRM-API'den webhook event detayı getirilemedi: {}", e.getMessage());
            throw new RuntimeException("CRM-API'den webhook event detayı getirilemedi", e);
        }
    }
}
