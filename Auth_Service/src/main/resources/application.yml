spring:
  application:
    name: auth-service

  profiles:
    active: dev  # Default to development profile

  config:
    import: "configserver:"

  cloud:
    config:
      uri: http://config-service:8888

# Default cookie settings (overridden by profile-specific configs)
app:
  cookie:
    secure: true      # Default to secure for safety
    same-site: Strict # Default to strict for safety

logging:
  level:
    org.springframework.security: DEBUG
    com.backend360.auth_service.config: DEBUG

# CRM API Configuration
crm:
  api:
    url: http://crm-api-gateway:6002

