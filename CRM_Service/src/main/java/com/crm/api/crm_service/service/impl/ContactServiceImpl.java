package com.crm.api.crm_service.service.impl;

import com.crm.api.crm_service.dto.ContactDto;
import com.crm.api.crm_service.dto.ContactIUDto;
import com.crm.api.crm_service.util.TextToSpeechFormatter;
import com.crm.api.crm_service.model.Contact;
import com.crm.api.crm_service.model.Tag;
import com.crm.api.crm_service.repository.ContactRepository;
import com.crm.api.crm_service.repository.TagRepository;
import com.crm.api.crm_service.service.ContactService;
import com.crm.api.crm_service.dto.MindhuntersCallDto;
import com.crm.api.crm_service.service.mindHunters.MindhuntersCallIntegrationService;
import com.crm.api.crm_service.service.mindHunters.MindhuntersContactIntegrationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import com.fasterxml.jackson.annotation.JsonProperty;

@Service
@Slf4j
public class ContactServiceImpl implements ContactService {
    private ContactRepository contactRepository;
    private TagRepository tagRepository;
    private final MindhuntersContactIntegrationService mindhuntersContactIntegrationService;
    private final MindhuntersCallIntegrationService mindhuntersCallIntegrationService;

    // Constructor ile bağımlılıkları alıyoruz
    public ContactServiceImpl(ContactRepository contactRepository, TagRepository tagRepository, MindhuntersContactIntegrationService mindhuntersContactIntegrationService, MindhuntersCallIntegrationService mindhuntersCallIntegrationService) {
        this.contactRepository = contactRepository;
        this.tagRepository = tagRepository;
        this.mindhuntersContactIntegrationService = mindhuntersContactIntegrationService;
        this.mindhuntersCallIntegrationService = mindhuntersCallIntegrationService;
    }

    @Override
    public Page<ContactDto> getAllContacts(Pageable pageable) {
        return contactRepository.findAll(pageable).map(ContactDto::entityToDto);
    }

    @Override
    public List<ContactDto> getAllContactsWithoutPagination() {
        return contactRepository.findAll().stream()
                .map(ContactDto::entityToDto)
                .collect(Collectors.toList());
    }

    @Override
    public Optional<ContactDto> getContactByUuid(UUID uuid) {
        return contactRepository.findByUuid(uuid).map(ContactDto::entityToDto);
    }

    @Override
    public ContactDto createContact(ContactIUDto contactIUDto) {
        Contact contact = new Contact();
        contact.setIsCalled(false); // Başlangıçta false olarak set et

        // Eğer contactName varsa onu kullan, yoksa name'i kullan
        contact.setName(contactIUDto.getContactName() != null && !contactIUDto.getContactName().trim().isEmpty()
                ? contactIUDto.getContactName() : contactIUDto.getName());

        // Eğer contactSurname varsa onu kullan, yoksa surname'i kullan
        contact.setSurname(contactIUDto.getContactSurname() != null && !contactIUDto.getContactSurname().trim().isEmpty()
                ? contactIUDto.getContactSurname() : contactIUDto.getSurname());

        // Eğer contactEmail varsa onu kullan, yoksa email'i kullan
        contact.setEmail(contactIUDto.getContactEmail() != null && !contactIUDto.getContactEmail().trim().isEmpty()
                ? contactIUDto.getContactEmail() : contactIUDto.getEmail());

        // Eğer contactPhone varsa onu kullan, yoksa phoneNumber'ı kullan
        contact.setPhoneNumber(contactIUDto.getContactPhone() != null && !contactIUDto.getContactPhone().trim().isEmpty()
                ? contactIUDto.getContactPhone() : contactIUDto.getPhoneNumber());
        contact.setNumberType(contactIUDto.getNumberType());
        contact.setCountryCode(contactIUDto.getCountryCode());
        contact.setTimezone(contactIUDto.getTimezone());
        contact.setStatus(contactIUDto.getStatus());
        contact.setCampaignId(contactIUDto.getCampaignId());
        contact.setCampaignName(contactIUDto.getCampaignName());
        contact.setCampaignTitle(contactIUDto.getCampaignTitle());
        contact.setCampaignDescription(contactIUDto.getCampaignDescription());
        contact.setCampaignType(contactIUDto.getCampaignType());
        contact.setContactPhone(contactIUDto.getContactPhone());
        contact.setContactEmail(contactIUDto.getContactEmail());
        contact.setContactName(contactIUDto.getContactName());
        contact.setContactSurname(contactIUDto.getContactSurname());
        contact.setBrandName(contactIUDto.getBrandName());
        contact.setSource(contactIUDto.getSource());
        if (contact.getCreatedAt() == null) {
            contact.setCreatedAt(java.time.LocalDateTime.now());
        }
        Contact saved = contactRepository.save(contact);

        // Mindhunters'a gönder
        try {
            mindhuntersContactIntegrationService.sendContactToMindhunters(contactIUDto);
        } catch (Exception e) {
            log.error("Mindhunters'a contact gönderilirken hata oluştu", e);
        }

        // Contact oluşunca call başlat
        try {
            String rawNumber = contactIUDto.getContactPhone() != null && !contactIUDto.getContactPhone().isBlank()
                    ? contactIUDto.getContactPhone()
                    : contactIUDto.getPhoneNumber();
            String formattedNumber = rawNumber;
            if (rawNumber != null) {
                if (rawNumber.startsWith("+")) {
                    formattedNumber = rawNumber;
                } else {
                    // baştaki 0'ları sil, +90 ekle
                    formattedNumber = "+90" + rawNumber.replaceFirst("^0+", "");
                }
            }
            // Format campaign name for better speech synthesis
            String formattedCampaignName = contactIUDto.getCampaignName() != null ?
                TextToSpeechFormatter.formatNumbersForSpeech(contactIUDto.getCampaignName()) : "";
            
            // Agent için isim ve soyisim önceliği: contactName/contactSurname > name/surname
            String leadName = (contactIUDto.getContactName() != null && !contactIUDto.getContactName().trim().isEmpty())
                    ? contactIUDto.getContactName()
                    : contactIUDto.getName();
            String leadSurname = (contactIUDto.getContactSurname() != null && !contactIUDto.getContactSurname().trim().isEmpty())
                    ? contactIUDto.getContactSurname()
                    : contactIUDto.getSurname();

            MindhuntersCallDto callDto = new MindhuntersCallDto();
            callDto.setAgentId("704c233e-df13-41e4-81fc-4cc7411746ac");
            callDto.setMessage(new MindhuntersCallDto.Message("Merhaba, ben Sinem. Sizi 360 Avantajlı’dan arıyorum.  "+ leadName + " " + leadSurname + " ile mi görüşüyorum?"));
            // Mindhunters'a gönderilecek alanları kısalt
            String campaignTitle = truncate(contactIUDto.getCampaignTitle(),100 );
            
            // Kategori kontrolü ile farklı promptlar oluştur
            String promptText;
            String participantInfo;
            
            String campaignType = contactIUDto.getCampaignType();
            String brandName = contactIUDto.getBrandName() != null ? contactIUDto.getBrandName() : "";
            log.info("Campaign Type: {}", campaignType);
            log.info("Brand Name: {}", brandName);
            
            if (campaignType != null && campaignType.toLowerCase().contains("banka")) {
    promptText = brandName + " " + formattedCampaignName + " kampanyası hakkında bilgi ver. " +
                 "Tarihleri ve numaraları Türkçe ifade et. Konuşmayı kısa tut, onay al ve kapat.";
    
    participantInfo = "Müşterinin adı " + leadName + " " + leadSurname +
                      ". İlgilendiği banka kampanyası: " + brandName + " " + formattedCampaignName + ".";

    log.info("BANKA kategorisi için sade prompt kullanılıyor");
} else if (campaignType != null && campaignType.toLowerCase().contains("otomotiv")) {
    promptText = brandName + " " + formattedCampaignName + " kampanyası hakkında bilgi ver. " +
                 "Tarihleri ve numaraları Türkçe ifade et. Konuşmayı kısa tut, test sürüşü teklif et, onay al ve kapat.";

    participantInfo = "Müşterinin adı " + leadName + " " + leadSurname +
                      ". İlgilendiği otomotiv kampanyası: " + brandName + " " + formattedCampaignName + ".";

    log.info("OTOMOTİV kategorisi için sade prompt kullanılıyor");
} else {
    promptText = brandName + " " + formattedCampaignName + " kampanyası hakkında kısa bilgi ver. " +
                 "Tarihleri ve numaraları Türkçe ifade et. Onay al ve konuşmayı bitir.";

    participantInfo = "Müşterinin adı " + leadName + " " + leadSurname +
                      ". Kampanya ilgisi: " + brandName + " " + formattedCampaignName + ".";

    log.info("GENEL kategorisi için sade prompt kullanılıyor");
}

            
            callDto.setPrompt(new MindhuntersCallDto.Prompt(false, promptText));
            callDto.setParticipant(new MindhuntersCallDto.Participant(formattedNumber, participantInfo));
            mindhuntersCallIntegrationService.sendCallToMindhunters(callDto);
            // Başarılı ise isCalled=true yap
            contact.setIsCalled(true);
            contactRepository.save(contact);
        } catch (Exception e) {
            log.error("Mindhunters call başlatılırken hata oluştu", e);
        }

        return ContactDto.entityToDto(saved);
    }

    @Override
    public Optional<ContactDto> updateContact(UUID uuid, ContactIUDto contactIUDto) {
        return contactRepository.findByUuid(uuid).map(contact -> {
            // Eğer contactName varsa onu kullan, yoksa name'i kullan
            contact.setName(contactIUDto.getContactName() != null && !contactIUDto.getContactName().trim().isEmpty()
                    ? contactIUDto.getContactName() : contactIUDto.getName());

            // Eğer contactSurname varsa onu kullan, yoksa surname'i kullan
            contact.setSurname(contactIUDto.getContactSurname() != null && !contactIUDto.getContactSurname().trim().isEmpty()
                    ? contactIUDto.getContactSurname() : contactIUDto.getSurname());

            // Eğer contactEmail varsa onu kullan, yoksa email'i kullan
            contact.setEmail(contactIUDto.getContactEmail() != null && !contactIUDto.getContactEmail().trim().isEmpty()
                    ? contactIUDto.getContactEmail() : contactIUDto.getEmail());

            // Eğer contactPhone varsa onu kullan, yoksa phoneNumber'ı kullan
            contact.setPhoneNumber(contactIUDto.getContactPhone() != null && !contactIUDto.getContactPhone().trim().isEmpty()
                    ? contactIUDto.getContactPhone() : contactIUDto.getPhoneNumber());
            contact.setNumberType(contactIUDto.getNumberType());
            contact.setCountryCode(contactIUDto.getCountryCode());
            contact.setTimezone(contactIUDto.getTimezone());
            contact.setStatus(contactIUDto.getStatus());
            contact.setCampaignId(contactIUDto.getCampaignId());
            contact.setCampaignName(contactIUDto.getCampaignName());
            contact.setCampaignTitle(contactIUDto.getCampaignTitle());
            contact.setCampaignDescription(contactIUDto.getCampaignDescription());
            contact.setCampaignType(contactIUDto.getCampaignType());
            contact.setContactPhone(contactIUDto.getContactPhone());
            contact.setContactEmail(contactIUDto.getContactEmail());
            contact.setContactName(contactIUDto.getContactName());
            contact.setContactSurname(contactIUDto.getContactSurname());
            contact.setBrandName(contactIUDto.getBrandName());
            Contact updated = contactRepository.save(contact);
            return ContactDto.entityToDto(updated);
        });
    }

    @Override
    public boolean deleteContact(UUID uuid) {
        if (contactRepository.existsById(uuid)) {
            contactRepository.deleteById(uuid);
            return true;
        }
        return false;
    }

    @Override
    public boolean addTagToContact(UUID uuid, Long tagId) {
        Optional<Contact> contactOpt = contactRepository.findByUuid(uuid);
        Optional<Tag> tagOpt = tagRepository.findById(tagId);
        if (contactOpt.isPresent() && tagOpt.isPresent()) {
            Contact contact = contactOpt.get();
            Tag tag = tagOpt.get();
            if (contact.getTags() == null) {
                contact.setTags(Set.of(tag));
            } else {
                contact.getTags().add(tag);
            }
            contactRepository.save(contact);
            return true;
        }
        return false;
    }

    @Override
    public boolean removeTagFromContact(UUID uuid, Long tagId) {
        Optional<Contact> contactOpt = contactRepository.findByUuid(uuid);
        if (contactOpt.isPresent()) {
            Contact contact = contactOpt.get();
            if (contact.getTags() != null) {
                Set<Tag> updatedTags = contact.getTags().stream()
                        .filter(tag -> !tag.getId().equals(tagId))
                        .collect(Collectors.toSet());
                contact.setTags(updatedTags);
                contactRepository.save(contact);
                return true;
            }
        }
        return false;
    }

    // Yardımcı fonksiyon: String'i maxLength kadar kısaltır.
    private String truncate(String value, int maxLength) {
        if (value == null) return null;
        return value.length() > maxLength ? value.substring(0, maxLength) : value;
    }
}