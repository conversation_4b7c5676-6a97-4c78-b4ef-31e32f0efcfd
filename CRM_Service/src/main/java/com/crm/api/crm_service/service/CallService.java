package com.crm.api.crm_service.service;

import com.crm.api.crm_service.dto.CallDto;
import com.crm.api.crm_service.dto.CallIUDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;
import java.util.UUID;

public interface CallService {
    Page<CallDto> getAllCalls(Pageable pageable);
    Optional<CallDto> getCallByUuid(UUID uuid);
    CallDto createCall(CallIUDto callIUDto);
} 