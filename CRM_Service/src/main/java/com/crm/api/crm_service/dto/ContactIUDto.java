package com.crm.api.crm_service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContactIUDto implements Serializable {
    private String name;
    private String surname;
    private String email;
    private String phoneNumber;
    private String numberType;
    private String countryCode;
    private String timezone;
    private String status;
    @JsonProperty("brandName")
    private String brandName;

    // CustomerToCampaign bilgileri
    private Boolean isCalled;
    private Boolean isActive;
    private String contactPhone;
    private String contactEmail;
    private String contactName;
    private String contactSurname;
    private Long campaignId;
    private String campaignName;
    private String campaignTitle;
    private String campaignDescription;
    private String campaignType;
    private String source;
} 