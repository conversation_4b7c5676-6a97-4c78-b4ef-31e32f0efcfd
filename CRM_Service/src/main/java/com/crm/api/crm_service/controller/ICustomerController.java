package com.crm.api.crm_service.controller;

import com.crm.api.crm_service.dto.CustomerIUDto;
import com.crm.api.crm_service.dto.CustomerDto;
import org.springframework.http.ResponseEntity;

import java.util.List;

public interface ICustomerController {
    
    ResponseEntity<List<CustomerDto>> getAllCustomers();
    
    ResponseEntity<CustomerDto> getCustomerById(Long id);
    
    ResponseEntity<CustomerDto> getCustomerByEmail(String email);
    
    ResponseEntity<CustomerDto> getCustomerByUsername(String username);
    
    ResponseEntity<CustomerDto> createCustomer(CustomerIUDto customerIUDto);
    
    ResponseEntity<CustomerDto> updateCustomer(Long id, CustomerIUDto customerIUDto);
    
    ResponseEntity<Void> deleteCustomer(Long id);
} 