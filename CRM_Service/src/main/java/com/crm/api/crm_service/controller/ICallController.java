package com.crm.api.crm_service.controller;

import com.crm.api.crm_service.dto.CallDto;
import com.crm.api.crm_service.dto.CallIUDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;

import java.util.Optional;
import java.util.UUID;

public interface ICallController {
    ResponseEntity<CallDto> createCall(CallIUDto callIUDto);
    ResponseEntity<Page<CallDto>> getAllCalls(Pageable pageable);
    ResponseEntity<CallDto> getCallByUuid(UUID uuid);
} 