package com.crm.api.crm_service.service.impl;

import com.crm.api.crm_service.dto.CallDto;
import com.crm.api.crm_service.dto.CallIUDto;
import com.crm.api.crm_service.model.Call;
import com.crm.api.crm_service.repository.CallRepository;
import com.crm.api.crm_service.service.CallService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.UUID;

@Service
@Slf4j
public class CallServiceImpl implements CallService {
    @Autowired
    private CallRepository callRepository;

    @Override
    public Page<CallDto> getAllCalls(Pageable pageable) {
        return callRepository.findAll(pageable).map(CallDto::entityToDto);
    }

    @Override
    public Optional<CallDto> getCallByUuid(UUID uuid) {
        return callRepository.findByUuid(uuid).map(CallDto::entityToDto);
    }

    @Override
    public CallDto createCall(CallIUDto callIUDto) {
        Call call = new Call();
        call.setAgentId(callIUDto.getAgentId());
        if (callIUDto.getMessage() != null) {
            call.setMessageStart(callIUDto.getMessage().getStart());
        }
        if (callIUDto.getPrompt() != null) {
            call.setPromptOverwrite(callIUDto.getPrompt().getOverwrite());
            call.setPromptContent(callIUDto.getPrompt().getContent());
        }
        if (callIUDto.getParticipant() != null) {
            call.setParticipantNumber(callIUDto.getParticipant().getNumber());
            call.setParticipantAbout(callIUDto.getParticipant().getAbout());
        }
        Call saved = callRepository.save(call);
        return CallDto.entityToDto(saved);
    }
} 