package com.crm.api.crm_service.service.mindHunters;

import com.crm.api.crm_service.dto.MindhuntersCampaignDto;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
@Slf4j
public class MindhuntersCampaignIntegrationService {

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    public MindhuntersCampaignIntegrationService() {
        // Java 8 LocalDateTime desteği için JavaTimeModule ekle
        objectMapper.registerModule(new JavaTimeModule());
    }

    public void sendCampaignToMindhunters(MindhuntersCampaignDto campaignDto) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth("3e569edd-caef-42f7-bc1c-ba414fb5fb1c");

        HttpEntity<MindhuntersCampaignDto> entity = new HttpEntity<>(campaignDto, headers);

        try {
            String jsonPayload = objectMapper.writeValueAsString(campaignDto);
            log.info("Mindhunters'a gönderilen campaign JSON: {}", jsonPayload);

            ResponseEntity<String> response = restTemplate.postForEntity(
                    "https://360avantajli.mindhunters.ai/api/v1/campaigns",
                    entity,
                    String.class
            );
            log.info("Campaign başarıyla gönderildi! Status: {}", response.getStatusCode());
            log.info("Cevap: {}", response.getBody());
        } catch (Exception e) {
            log.error("Campaign gönderirken hata oluştu", e);
        }
    }
}