package com.crm.api.crm_service.controller;

import com.crm.api.crm_service.dto.CampaignDto;
import com.crm.api.crm_service.dto.CampaignIUDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;

import java.util.Optional;
import java.util.UUID;

public interface ICampaignController {
    ResponseEntity<Page<CampaignDto>> getAllCampaigns(Pageable pageable);
    ResponseEntity<CampaignDto> getCampaignByUuid(UUID uuid);
    ResponseEntity<CampaignDto> createCampaign(CampaignIUDto campaignIUDto);
    ResponseEntity<CampaignDto> updateCampaign(UUID uuid, CampaignIUDto campaignIUDto);
    ResponseEntity<Void> deleteCampaign(UUID uuid);
} 