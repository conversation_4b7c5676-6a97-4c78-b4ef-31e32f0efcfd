package com.crm.api.crm_service.security;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.util.Date;

/**
 * Internal service token validator
 * Backend servisleri arasındaki iletişim i<PERSON> kull<PERSON>lır
 */
@Component
@Slf4j
public class InternalServiceTokenValidator {

    @Value("${internal.service.secret-key:internal-service-secret-key-2025}")
    private String internalSecretKey;

    @Value("${internal.service.allowed-services:backend2-auth-service,backend2-campaign-service}")
    private String allowedServices;

    private SecretKey getSigningKey() {
        return Keys.hmacShaKeyFor(internalSecretKey.getBytes());
    }

    /**
     * Internal service token'ını validate et
     */
    public boolean validateInternalToken(String token) {
        try {
            Claims claims = Jwts.parser()
                    .verifyWith(getSigningKey())
                    .build()
                    .parseSignedClaims(token)
                    .getPayload();

            // Token süresini kontrol et
            if (claims.getExpiration().before(new Date())) {
                log.warn("Internal service token expired");
                return false;
            }

            // Internal token olduğunu kontrol et
            Boolean isInternal = claims.get("internal", Boolean.class);
            if (isInternal == null || !isInternal) {
                log.warn("Token is not marked as internal service token");
                return false;
            }

            // Servis adını kontrol et
            String serviceName = claims.get("service", String.class);
            if (serviceName == null || !isAllowedService(serviceName)) {
                log.warn("Service {} is not allowed", serviceName);
                return false;
            }

            log.debug("Internal service token validated successfully for service: {}", serviceName);
            return true;

        } catch (Exception e) {
            log.error("Internal service token validation failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Token'dan kullanıcı rolünü çıkar
     */
    public String extractUserRole(String token) {
        try {
            Claims claims = Jwts.parser()
                    .verifyWith(getSigningKey())
                    .build()
                    .parseSignedClaims(token)
                    .getPayload();

            return claims.get("user_role", String.class);
        } catch (Exception e) {
            log.error("Failed to extract user role from internal token: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Token'dan username çıkar
     */
    public String extractUsername(String token) {
        try {
            Claims claims = Jwts.parser()
                    .verifyWith(getSigningKey())
                    .build()
                    .parseSignedClaims(token)
                    .getPayload();

            return claims.get("username", String.class);
        } catch (Exception e) {
            log.error("Failed to extract username from internal token: {}", e.getMessage());
            return null;
        }
    }

    private boolean isAllowedService(String serviceName) {
        if (allowedServices == null) return false;
        String[] services = allowedServices.split(",");
        for (String service : services) {
            if (service.trim().equals(serviceName)) {
                return true;
            }
        }
        return false;
    }
}
