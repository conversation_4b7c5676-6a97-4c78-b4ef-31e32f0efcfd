package com.crm.api.crm_service.controller.impl;

import com.crm.api.crm_service.model.WebhookEvent;
import com.crm.api.crm_service.service.WebhookEventService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.security.access.prepost.PreAuthorize;

@RestController
@RequestMapping("/api/v1/webhook-events")
@CrossOrigin(origins = "*")
@Slf4j
public class WebhookEventControllerImpl {
    
    @Autowired
    private WebhookEventService webhookEventService;
    
    @PreAuthorize("hasAnyRole('ADMIN', 'CRM_ADMIN')")
    @GetMapping
    public ResponseEntity<List<WebhookEvent>> getAllWebhookEvents() {
        log.info("GET /api/v1/webhook-events");
        List<WebhookEvent> events = webhookEventService.findAll();
        return ResponseEntity.ok(events);
    }
    
    @PreAuthorize("hasAnyRole('ADMIN', 'CRM_ADMIN')")
    @GetMapping("/{id}")
    public ResponseEntity<WebhookEvent> getWebhookEventById(@PathVariable UUID id) {
        log.info("GET /api/v1/webhook-events/{}", id);
        Optional<WebhookEvent> event = webhookEventService.findById(id);
        return event.map(ResponseEntity::ok)
                   .orElse(ResponseEntity.notFound().build());
    }

    @PreAuthorize("hasAnyRole('ADMIN', 'CRM_ADMIN')")
    @GetMapping("/by-phone/{phoneNumber}")
    public ResponseEntity<List<WebhookEvent>> getWebhookEventsByPhoneNumber(@PathVariable String phoneNumber) {
        log.info("GET /api/v1/webhook-events/by-phone/{}", phoneNumber);
        List<WebhookEvent> events = webhookEventService.findByPhoneNumber(phoneNumber);
        return ResponseEntity.ok(events);
    }

    @PreAuthorize("hasRole('ADMIN')")
    @PutMapping("/{id}")
    public ResponseEntity<WebhookEvent> updateWebhookEvent(@PathVariable UUID id, @RequestBody WebhookEvent updatedEvent) {
        log.info("PUT /api/v1/webhook-events/{}", id);
        Optional<WebhookEvent> existingOpt = webhookEventService.findById(id);
        if (existingOpt.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        WebhookEvent existing = existingOpt.get();
        // Update fields except uuid, createdAt, updatedAt
        existing.setEvent(updatedEvent.getEvent());
        existing.setCallId(updatedEvent.getCallId());
        existing.setAnalysis(updatedEvent.getAnalysis());
        existing.setDuration(updatedEvent.getDuration());
        existing.setConversation(updatedEvent.getConversation());
        existing.setEndReason(updatedEvent.getEndReason());
        existing.setPipeline(updatedEvent.getPipeline());
        existing.setFields(updatedEvent.getFields());
        existing.setNumber(updatedEvent.getNumber());
        existing.setWebhookTimestamp(updatedEvent.getWebhookTimestamp());
        WebhookEvent saved = webhookEventService.save(existing);
        return ResponseEntity.ok(saved);
    }

    @PreAuthorize("hasRole('ADMIN')")
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteWebhookEvent(@PathVariable UUID id) {
        log.info("DELETE /api/v1/webhook-events/{}", id);
        webhookEventService.deleteById(id);
        return ResponseEntity.noContent().build();
    }
}
