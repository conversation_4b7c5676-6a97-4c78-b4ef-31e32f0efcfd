package com.crm.api.crm_service.service;

import com.crm.api.crm_service.dto.CallLogIUDto;
import com.crm.api.crm_service.dto.CallLogDto;
import com.crm.api.crm_service.model.CallLog;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface CallLogService {
    
    List<CallLogDto> getAllCallLogs();
    
    Optional<CallLogDto> getCallLogById(Long id);
    
    List<CallLogDto> getCallLogsByCustomerId(Long customerId);
    
    List<CallLogDto> getCallLogsByAgent(String agent);
    
    List<CallLogDto> getCallLogsByResult(CallLog.CallResult result);
    
    List<CallLogDto> getCallLogsByDateRange(LocalDateTime startDate, LocalDateTime endDate);
    
    CallLogDto createCallLog(CallLogIUDto callLogIUDto);
    
    Optional<CallLogDto> updateCallLog(Long id, CallLogIUDto callLogIUDto);
    
    boolean deleteCallLog(Long id);
} 