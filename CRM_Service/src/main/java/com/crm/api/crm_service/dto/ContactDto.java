package com.crm.api.crm_service.dto;

import com.crm.api.crm_service.model.Contact;
import com.crm.api.crm_service.model.Tag;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContactDto implements Serializable {
    private UUID uuid;
    private String name;
    private String surname;
    private String email;
    private String phoneNumber;
    private String numberType;
    private String countryCode;
    private String timezone;
    private String status;
    @JsonProperty("brandName")
    private String brandName;
    
    // CustomerToCampaign bilgileri
    private Boolean isCalled;
    private Boolean isActive;
    private String contactPhone;
    private String contactEmail;
    private String contactName;
    private String contactSurname;
    
    // Campaign bilgileri
    private Long campaignId;
    private String campaignName;
    private String campaignTitle;
    private String campaignDescription;
    private String campaignType;
    private String source;
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime createdAt;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime updatedAt;
    private Set<TagDto> tags;

    public static ContactDto entityToDto(Contact contact) {
        if (contact == null) return null;
        return ContactDto.builder()
                .uuid(contact.getUuid())
                .name(contact.getName())
                .surname(contact.getSurname())
                .email(contact.getEmail())
                .phoneNumber(contact.getPhoneNumber())
                .numberType(contact.getNumberType())
                .countryCode(contact.getCountryCode())
                .timezone(contact.getTimezone())
                .status(contact.getStatus())
                .brandName(contact.getBrandName())
                // CustomerToCampaign bilgileri
                .isCalled(contact.getIsCalled())
                .isActive(contact.getIsActive())
                .contactPhone(contact.getContactPhone())
                .contactEmail(contact.getContactEmail())
                .contactName(contact.getContactName())
                .contactSurname(contact.getContactSurname())
                // Campaign bilgileri
                .campaignId(contact.getCampaignId())
                .campaignName(contact.getCampaignName())
                .campaignTitle(contact.getCampaignTitle())
                .campaignDescription(contact.getCampaignDescription())
                .campaignType(contact.getCampaignType())
                .source(contact.getSource())
                .createdAt(contact.getCreatedAt())
                .updatedAt(contact.getUpdatedAt())
                .tags(contact.getTags() != null ? contact.getTags().stream().map(TagDto::entityToDto).collect(Collectors.toSet()) : null)
                .build();
    }
} 