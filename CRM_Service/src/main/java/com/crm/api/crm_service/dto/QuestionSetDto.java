package com.crm.api.crm_service.dto;

import com.crm.api.crm_service.model.QuestionSet;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QuestionSetDto implements Serializable {
    
    private Long id;
    private String question;
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime createdAt;
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime updatedAt;
    
    public static QuestionSetDto entityToDto(QuestionSet questionSet) {
        if (questionSet == null) return null;
        return QuestionSetDto.builder()
                .id(questionSet.getId())
                .question(questionSet.getQuestion())
                .createdAt(questionSet.getCreatedAt())
                .updatedAt(questionSet.getUpdatedAt())
                .build();
    }
    
    public static List<QuestionSetDto> entityListToDtoList(List<QuestionSet> questionSets) {
        if (questionSets == null) return null;
        return questionSets.stream()
                .map(QuestionSetDto::entityToDto)
                .collect(Collectors.toList());
    }
} 