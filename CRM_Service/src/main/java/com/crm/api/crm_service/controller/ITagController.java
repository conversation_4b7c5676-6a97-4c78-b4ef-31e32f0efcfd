package com.crm.api.crm_service.controller;

import com.crm.api.crm_service.dto.TagDto;
import com.crm.api.crm_service.dto.TagIUDto;
import org.springframework.http.ResponseEntity;

import java.util.List;

public interface ITagController {
    ResponseEntity<List<TagDto>> getAllTags();
    ResponseEntity<TagDto> getTagById(Long id);
    ResponseEntity<TagDto> createTag(TagIUDto tagIUDto);
    ResponseEntity<TagDto> updateTag(Long id, TagIUDto tagIUDto);
    ResponseEntity<Void> deleteTag(Long id);
} 