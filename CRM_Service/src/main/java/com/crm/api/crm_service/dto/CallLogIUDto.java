package com.crm.api.crm_service.dto;

import com.crm.api.crm_service.model.CallLog;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CallLogIUDto implements Serializable {
    
    private Long customerId;
    private LocalDateTime callDate;
    private CallLog.CallResult result;
    private String audioUrl;
    private String note;
    private String agent;
} 