package com.crm.api.crm_service.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CampaignIUDto implements Serializable {
    private String name;
    private String description;
    private String status;
    private String campaignType;
    private LocalDateTime startDate;
    private LocalDateTime endDate;
    private Map<String, Object> rules;
    private Long agentId;
    private Long agentAppId;
} 