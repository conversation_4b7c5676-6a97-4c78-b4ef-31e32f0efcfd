package com.crm.api.crm_service.controller.impl;

import com.crm.api.crm_service.dto.WebhookPayload;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import com.crm.api.crm_service.model.WebhookEvent;
import com.crm.api.crm_service.service.WebhookEventService;
import com.crm.api.crm_service.dto.WebhookData;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;

@RestController
@RequestMapping("/webhook")
@Slf4j
public class WebhookController {

    @Value("${webhook.secret.key:webhook-secret-key-2025}")
    private String webhookSecretKey;

    @Autowired
    private WebhookEventService webhookEventService;

    @Autowired
    private ObjectMapper objectMapper;

    @PostMapping(value = "/conversation-end", consumes = "application/json")
    public ResponseEntity<String> receiveConversationEnd(
            @RequestBody WebhookPayload payload,
            @RequestHeader(value = "X-Webhook-Signature", required = true) String secretKey) {

        // Secret key kontrolü
        if (!webhookSecretKey.equals(secretKey)) {
            log.warn("Unauthorized webhook attempt! Secret key mismatch.");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid secret key");
        }

        log.info("Webhook received: {}", payload);
        // Veritabanına kaydet
        try {
            WebhookData data = payload.getData();
            WebhookEvent event = new WebhookEvent();
            event.setEvent(payload.getEvent());
            event.setCallId(data.getCall_id());
            event.setAnalysis(data.getAnalysis());
            event.setDuration(data.getDuration());
            event.setConversation(data.getConversation());
            event.setEndReason(data.getEnd_reason());
            event.setNumber(data.getNumber());
            event.setWebhookTimestamp(payload.getTimestamp());
            // pipeline ve fields'ı JSON string olarak kaydet
            event.setPipeline(objectMapper.writeValueAsString(data.getPipeline()));
            event.setFields(objectMapper.writeValueAsString(data.getFields()));
            webhookEventService.save(event);
        } catch (JsonProcessingException e) {
            log.error("WebhookEvent JSON serialization error: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Webhook save error");
        }
        return ResponseEntity.ok("Webhook received successfully");
    }
} 