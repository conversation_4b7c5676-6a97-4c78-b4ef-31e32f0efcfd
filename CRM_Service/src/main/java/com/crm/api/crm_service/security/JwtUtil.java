package com.crm.api.crm_service.security;

import io.jsonwebtoken.*;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

@Component
@Slf4j
public class JwtUtil {

    @Value("${jwt.secret:crm-api-secret-key-for-ai-call-center-integration-2025}")
    private String secret;

    @Value("${jwt.expiration:86400000}") // 24 hours in milliseconds
    private Long expiration;

    @Value("${internal.service.secret-key:internal-service-secret-key-2025}")
    private String internalServiceSecret;

    private SecretKey getSigningKey() {
        // Backend2 ile uyumluluk için Base64URL decode kullan
        byte[] keyBytes = Decoders.BASE64URL.decode(secret);
        return Keys.hmacShaKeyFor(keyBytes);
    }

    private SecretKey getInternalServiceSigningKey() {
        // Internal service token'ları için basit string key kullan
        return Keys.hmacShaKeyFor(internalServiceSecret.getBytes());
    }

    public String extractUsername(String token) {
        return extractClaim(token, Claims::getSubject);
    }

    public Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.apply(claims);
    }

    private Claims extractAllClaims(String token) {
        try {
            // Önce normal JWT secret key ile dene
            return Jwts.parser()
                    .verifyWith(getSigningKey())
                    .build()
                    .parseSignedClaims(token)
                    .getPayload();
        } catch (JwtException e) {
            try {
                // Normal key başarısız olursa internal service key ile dene
                log.debug("Normal JWT key failed, trying internal service key");
                return Jwts.parser()
                        .verifyWith(getInternalServiceSigningKey())
                        .build()
                        .parseSignedClaims(token)
                        .getPayload();
            } catch (JwtException internalException) {
                log.error("JWT parsing error with both keys: Normal={}, Internal={}", e.getMessage(), internalException.getMessage());
                throw e; // İlk hatayı fırlat
            }
        }
    }

    private Boolean isTokenExpired(String token) {
        return extractExpiration(token).before(new Date());
    }

    public String generateToken(String username, String role) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("role", role);
        claims.put("iat", new Date());
        return createToken(claims, username);
    }

    private String createToken(Map<String, Object> claims, String subject) {
        return Jwts.builder()
                .claims(claims)
                .subject(subject)
                .issuedAt(new Date(System.currentTimeMillis()))
                .expiration(new Date(System.currentTimeMillis() + expiration))
                .signWith(getSigningKey())
                .compact();
    }

    public Boolean validateToken(String token, String username) {
        try {
            final String extractedUsername = extractUsername(token);
            return (extractedUsername.equals(username) && !isTokenExpired(token));
        } catch (JwtException e) {
            log.error("JWT validation error: {}", e.getMessage());
            return false;
        }
    }

    public Boolean validateToken(String token) {
        try {
            extractAllClaims(token);
            return !isTokenExpired(token);
        } catch (JwtException e) {
            log.error("JWT validation error: {}", e.getMessage());
            return false;
        }
    }

    public String extractRole(String token) {
        // Backend2 ile uyumluluk için farklı claim'leri kontrol et
        String role = extractClaim(token, claims -> claims.get("roles", String.class));
        if (role == null) {
            role = extractClaim(token, claims -> claims.get("role", String.class));
        }
        // Internal service token'ları için user_role claim'ini de kontrol et
        if (role == null) {
            role = extractClaim(token, claims -> claims.get("user_role", String.class));
        }
        return role;
    }
}
