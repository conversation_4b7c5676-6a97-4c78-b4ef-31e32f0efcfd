package com.crm.api.crm_service.controller;

import com.crm.api.crm_service.dto.QuestionSetIUDto;
import com.crm.api.crm_service.dto.QuestionSetDto;
import org.springframework.http.ResponseEntity;

import java.util.List;

public interface IQuestionSetController {
    
    ResponseEntity<List<QuestionSetDto>> getAllQuestionSets();
    
    ResponseEntity<QuestionSetDto> getQuestionSetById(Long id);
    
    ResponseEntity<QuestionSetDto> createQuestionSet(QuestionSetIUDto questionSetIUDto);
    
    ResponseEntity<QuestionSetDto> updateQuestionSet(Long id, QuestionSetIUDto questionSetIUDto);
    
    ResponseEntity<Void> deleteQuestionSet(Long id);
} 