package com.crm.api.crm_service.service.mindHunters;

import com.crm.api.crm_service.dto.MindhuntersCallDto;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
@Slf4j
public class MindhuntersCallIntegrationService {
    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    public MindhuntersCallIntegrationService() {
        objectMapper.registerModule(new JavaTimeModule());
    }

    public void sendCallToMindhunters(MindhuntersCallDto callDto) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth("3e569edd-caef-42f7-bc1c-ba414fb5fb1c");

        HttpEntity<MindhuntersCallDto> entity = new HttpEntity<>(callDto, headers);

        try {
            ResponseEntity<String> response = restTemplate.postForEntity(
                    "https://360avantajli.mindhunters.ai/api/v1/call",
                    entity,
                    String.class
            );
            log.info("Call başarıyla gönderildi! Status: {}", response.getStatusCode());
        } catch (Exception e) {
            log.error("Call gönderirken hata oluştu", e);
        }
    }
}
