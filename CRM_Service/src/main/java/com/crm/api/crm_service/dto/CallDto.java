package com.crm.api.crm_service.dto;

import com.crm.api.crm_service.model.Call;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CallDto implements Serializable {
    private UUID uuid;
    private UUID agentId;
    private String messageStart;
    private Boolean promptOverwrite;
    private String promptContent;
    private String participantNumber;
    private String participantAbout;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime createdAt;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime updatedAt;

    public static CallDto entityToDto(Call call) {
        if (call == null) return null;
        return CallDto.builder()
                .uuid(call.getUuid())
                .agentId(call.getAgentId())
                .messageStart(call.getMessageStart())
                .promptOverwrite(call.getPromptOverwrite())
                .promptContent(call.getPromptContent())
                .participantNumber(call.getParticipantNumber())
                .participantAbout(call.getParticipantAbout())
                .createdAt(call.getCreatedAt())
                .updatedAt(call.getUpdatedAt())
                .build();
    }
} 