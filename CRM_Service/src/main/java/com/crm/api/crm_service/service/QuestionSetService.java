package com.crm.api.crm_service.service;

import com.crm.api.crm_service.dto.QuestionSetIUDto;
import com.crm.api.crm_service.dto.QuestionSetDto;

import java.util.List;
import java.util.Optional;

public interface QuestionSetService {
    
    List<QuestionSetDto> getAllQuestionSets();
    
    Optional<QuestionSetDto> getQuestionSetById(Long id);
    
    QuestionSetDto createQuestionSet(QuestionSetIUDto questionSetIUDto);
    
    Optional<QuestionSetDto> updateQuestionSet(Long id, QuestionSetIUDto questionSetIUDto);
    
    boolean deleteQuestionSet(Long id);
} 