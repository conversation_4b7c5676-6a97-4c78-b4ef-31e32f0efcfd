package com.crm.api.crm_service.controller;

import com.crm.api.crm_service.dto.ContactDto;
import com.crm.api.crm_service.dto.ContactIUDto;
import com.crm.api.crm_service.dto.FormDataDto;
import com.crm.api.crm_service.service.ContactService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/form")
@RequiredArgsConstructor
@Slf4j
public class FormDataController {

    private final ContactService contactService;

    @PostMapping("/submit")
    public ResponseEntity<ContactDto> submitFormData(@RequestBody FormDataDto formDataDto) {
        log.info("POST /api/v1/form/submit - Form verisi alındı, source: {}", formDataDto.getSource());
        
        // FormDataDto'yu ContactIUDto'ya çevir
        ContactIUDto contactIUDto = new ContactIUDto();
        contactIUDto.setName(formDataDto.getName());
        contactIUDto.setSurname(formDataDto.getSurname());
        contactIUDto.setEmail(formDataDto.getEmail());
        contactIUDto.setPhoneNumber(formDataDto.getPhoneNumber());
        contactIUDto.setCountryCode(formDataDto.getCountryCode());
        contactIUDto.setTimezone(formDataDto.getTimezone());
        contactIUDto.setCampaignId(Long.valueOf(formDataDto.getCampaignId()));
        contactIUDto.setCampaignName(formDataDto.getCampaignName());
        contactIUDto.setCampaignTitle(formDataDto.getCampaignTitle());
        contactIUDto.setCampaignDescription(formDataDto.getCampaignDescription());
        contactIUDto.setCampaignType(formDataDto.getCampaignType());
        contactIUDto.setBrandName(formDataDto.getBrandName());
        contactIUDto.setSource(formDataDto.getSource());
        
        ContactDto createdContact = contactService.createContact(contactIUDto);
        return ResponseEntity.ok(createdContact);
    }
} 