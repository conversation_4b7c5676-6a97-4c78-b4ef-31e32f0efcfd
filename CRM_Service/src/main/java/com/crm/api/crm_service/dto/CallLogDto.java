package com.crm.api.crm_service.dto;

import com.crm.api.crm_service.model.CallLog;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CallLogDto implements Serializable {
    
    private Long id;
    private Long customerId;
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime callDate;
    
    private CallLog.CallResult result;
    private String audioUrl;
    private String note;
    private String agent;
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime createdAt;
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime updatedAt;
    
    public static CallLogDto entityToDto(CallLog callLog) {
        if (callLog == null) return null;
        return CallLogDto.builder()
                .id(callLog.getId())
                .customerId(callLog.getCustomer().getId())
                .callDate(callLog.getCallDate())
                .result(callLog.getResult())
                .audioUrl(callLog.getAudioUrl())
                .note(callLog.getNote())
                .agent(callLog.getAgent())
                .createdAt(callLog.getCreatedAt())
                .updatedAt(callLog.getUpdatedAt())
                .build();
    }
    
    public static List<CallLogDto> entityListToDtoList(List<CallLog> callLogs) {
        if (callLogs == null) return null;
        return callLogs.stream()
                .map(CallLogDto::entityToDto)
                .collect(Collectors.toList());
    }
} 