package com.crm.api.crm_service.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class WebhookData implements Serializable {
    private String call_id;
    private String analysis;
    private long duration;
    private String conversation;
    private String end_reason;
    private Object pipeline;
    private Fields fields;
    private String number;
    private long timestamp;
}

@Data
@AllArgsConstructor
@NoArgsConstructor
class Fields implements Serializable {
    private String name;
    private String surname;
    private String email;
    private String phone_number;
    private String number_type;
    private String country_code;
    private String timezone;
    private String primary_language;
    private String preferred_contact_time;
    private String preferred_contact_channel;
    private String recovery_issue_type;
    private String alternate_email_verified;
    private String two_factor_method;
    private String account_age_years;
    private String subscription_type;
}
