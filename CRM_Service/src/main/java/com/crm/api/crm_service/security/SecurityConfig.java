package com.crm.api.crm_service.security;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;
import java.util.List;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity
@RequiredArgsConstructor
public class SecurityConfig {

    private final JwtAuthenticationFilter jwtAuthenticationFilter;

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .csrf(AbstractHttpConfigurer::disable)
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(auth -> auth
                // Public endpoints
                .requestMatchers("/api/auth/token").permitAll()
                .requestMatchers("/api/auth/register").permitAll()
                .requestMatchers("/api/auth/login").permitAll()
                .requestMatchers("/api/v1/form/**").permitAll() // Form verisi için public endpoint
                .requestMatchers("/webhook/**").permitAll() // Webhook kendi secret key kontrolü yapar

                // Admin only endpoints
                .requestMatchers("/api/v1/users/**").hasRole("ADMIN")

                // CRUD operations - Admin and CRM_ADMIN
                .requestMatchers("/api/v1/customers/**").hasAnyRole("ADMIN", "CRM_ADMIN")
                .requestMatchers("/api/v1/contacts/**").hasAnyRole("ADMIN", "CRM_ADMIN")
                .requestMatchers("/api/v1/call-logs/**").hasAnyRole("ADMIN", "CRM_ADMIN")
                .requestMatchers("/api/v1/calls/**").hasAnyRole("ADMIN", "CRM_ADMIN")
                .requestMatchers("/api/v1/tags/**").hasAnyRole("ADMIN", "CRM_ADMIN")

                // Campaign operations - CRM_ADMIN can only read
                .requestMatchers(HttpMethod.GET, "/api/v1/campaigns/**").hasAnyRole("ADMIN", "CRM_ADMIN")
                .requestMatchers(HttpMethod.POST, "/api/v1/campaigns/**").hasRole("ADMIN")
                .requestMatchers(HttpMethod.PUT, "/api/v1/campaigns/**").hasRole("ADMIN")
                .requestMatchers(HttpMethod.DELETE, "/api/v1/campaigns/**").hasRole("ADMIN")

                // Admin and CRM_ADMIN endpoints
                .requestMatchers("/api/v1/webhook-events/**").hasAnyRole("ADMIN", "CRM_ADMIN")
                .requestMatchers("/api/v1/question-sets/**").hasRole("ADMIN")
                .requestMatchers("/api/v1/data/**").hasRole("ADMIN")

                // Other endpoints - any authenticated user
                .requestMatchers("/api/test/**").authenticated()
                .requestMatchers("/api/auth/validate").authenticated()
                .requestMatchers("/webhook/**").authenticated()
                .requestMatchers("/actuator/**").hasRole("ADMIN")
                .requestMatchers("/swagger-ui/**").permitAll()
                .requestMatchers("/v3/api-docs/**").permitAll()

                // All other requests require authentication
                .anyRequest().authenticated()
            )
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(List.of("*"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);
        configuration.setExposedHeaders(Arrays.asList("Authorization"));

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }


    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
