package com.crm.api.crm_service.service.impl;

import com.crm.api.crm_service.dto.TagDto;
import com.crm.api.crm_service.dto.TagIUDto;
import com.crm.api.crm_service.model.Tag;
import com.crm.api.crm_service.repository.TagRepository;
import com.crm.api.crm_service.service.TagService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TagServiceImpl implements TagService {
    @Autowired
    private TagRepository tagRepository;

    @Override
    public List<TagDto> getAllTags() {
        return tagRepository.findAll().stream().map(TagDto::entityToDto).collect(Collectors.toList());
    }

    @Override
    public Optional<TagDto> getTagById(Long id) {
        return tagRepository.findById(id).map(TagDto::entityToDto);
    }

    @Override
    public TagDto createTag(TagIUDto tagIUDto) {
        Tag tag = new Tag();
        tag.setName(tagIUDto.getName());
        Tag saved = tagRepository.save(tag);
        return TagDto.entityToDto(saved);
    }

    @Override
    public Optional<TagDto> updateTag(Long id, TagIUDto tagIUDto) {
        return tagRepository.findById(id).map(tag -> {
            tag.setName(tagIUDto.getName());
            Tag updated = tagRepository.save(tag);
            return TagDto.entityToDto(updated);
        });
    }

    @Override
    public boolean deleteTag(Long id) {
        if (tagRepository.existsById(id)) {
            tagRepository.deleteById(id);
            return true;
        }
        return false;
    }
} 