package com.crm.api.crm_service.controller.impl;

import com.crm.api.crm_service.controller.ICustomerController;
import com.crm.api.crm_service.dto.CustomerIUDto;
import com.crm.api.crm_service.dto.CustomerDto;
import com.crm.api.crm_service.service.CustomerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/v1/customers")
@CrossOrigin(origins = "*")
@Slf4j
public class CustomerControllerImpl implements ICustomerController {
    
    @Autowired
    private CustomerService customerService;
    
    @Override
    @GetMapping
    public ResponseEntity<List<CustomerDto>> getAllCustomers() {
        log.info("GET /customers");
        List<CustomerDto> customers = customerService.getAllCustomers();
        return ResponseEntity.ok(customers);
    }
    
    @Override
    @GetMapping("/{id}")
    public ResponseEntity<CustomerDto> getCustomerById(@PathVariable Long id) {
        log.info("GET /customers/{}", id);
        Optional<CustomerDto> customer = customerService.getCustomerById(id);
        return customer.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    @Override
    @GetMapping("/email/{email}")
    public ResponseEntity<CustomerDto> getCustomerByEmail(@PathVariable String email) {
        log.info("GET /customers/email/{}", email);
        Optional<CustomerDto> customer = customerService.getCustomerByEmail(email);
        return customer.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    @Override
    @GetMapping("/username/{username}")
    public ResponseEntity<CustomerDto> getCustomerByUsername(@PathVariable String username) {
        log.info("GET /customers/username/{}", username);
        Optional<CustomerDto> customer = customerService.getCustomerByUsername(username);
        return customer.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    @Override
    @PostMapping
    public ResponseEntity<CustomerDto> createCustomer(@RequestBody CustomerIUDto customerIUDto) {
        log.info("POST /customers - {}", customerIUDto.getUsername());
        
        if (customerService.existsByEmail(customerIUDto.getEmail())) {
            log.warn("Email already exists: {}", customerIUDto.getEmail());
            return ResponseEntity.badRequest().build();
        }
        if (customerService.existsByUsername(customerIUDto.getUsername())) {
            log.warn("Username already exists: {}", customerIUDto.getUsername());
            return ResponseEntity.badRequest().build();
        }
        
        CustomerDto createdCustomer = customerService.createCustomer(customerIUDto);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdCustomer);
    }
    
    @Override
    @PutMapping("/{id}")
    public ResponseEntity<CustomerDto> updateCustomer(@PathVariable Long id, 
                                                    @RequestBody CustomerIUDto customerIUDto) {
        log.info("PUT /customers/{}", id);
        Optional<CustomerDto> updatedCustomer = customerService.updateCustomer(id, customerIUDto);
        return updatedCustomer.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    @Override
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteCustomer(@PathVariable Long id) {
        log.info("DELETE /customers/{}", id);
        boolean deleted = customerService.deleteCustomer(id);
        return deleted ? ResponseEntity.noContent().build() : ResponseEntity.notFound().build();
    }
} 