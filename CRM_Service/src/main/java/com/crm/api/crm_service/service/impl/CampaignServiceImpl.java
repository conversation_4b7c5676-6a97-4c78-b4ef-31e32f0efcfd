package com.crm.api.crm_service.service.impl;

import com.crm.api.crm_service.dto.CampaignDto;
import com.crm.api.crm_service.dto.CampaignIUDto;
import com.crm.api.crm_service.dto.MindhuntersCampaignDto;
import com.crm.api.crm_service.model.Campaign;
import com.crm.api.crm_service.repository.CampaignRepository;
import com.crm.api.crm_service.service.CampaignService;
import com.crm.api.crm_service.service.mindHunters.MindhuntersCampaignIntegrationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.UUID;

@Service
@Slf4j
public class CampaignServiceImpl implements CampaignService {
    @Autowired
    private CampaignRepository campaignRepository;
    
    @Autowired
    private MindhuntersCampaignIntegrationService mindhuntersCampaignIntegrationService;

    @Override
    public Page<CampaignDto> getAllCampaigns(Pageable pageable) {
        return campaignRepository.findAll(pageable).map(CampaignDto::entityToDto);
    }

    @Override
    public Optional<CampaignDto> getCampaignByUuid(UUID uuid) {
        return campaignRepository.findByUuid(uuid).map(CampaignDto::entityToDto);
    }

    @Override
    public CampaignDto createCampaign(CampaignIUDto campaignIUDto) {
        Campaign campaign = new Campaign();
        campaign.setName(campaignIUDto.getName());
        campaign.setDescription(campaignIUDto.getDescription());
        campaign.setStatus(campaignIUDto.getStatus());
        campaign.setCampaignType(campaignIUDto.getCampaignType());
        campaign.setStartDate(campaignIUDto.getStartDate());
        campaign.setEndDate(campaignIUDto.getEndDate());
        campaign.setRules(campaignIUDto.getRules());
        campaign.setAgentId(campaignIUDto.getAgentId());
        campaign.setAgentAppId(campaignIUDto.getAgentAppId());
        Campaign saved = campaignRepository.save(campaign);
        
        // Mindhunters'a gönder
        try {
            MindhuntersCampaignDto mindhuntersCampaignDto = new MindhuntersCampaignDto();
            mindhuntersCampaignDto.setName(campaignIUDto.getName());
            mindhuntersCampaignDto.setDescription(campaignIUDto.getDescription());
            
            // Status'u Mindhunters formatına çevir
            String status = campaignIUDto.getStatus();
            if (status != null) {
                status = status.toLowerCase();
                if (status.equals("draft") || status.equals("active")) {
                    mindhuntersCampaignDto.setStatus(status); // DTO'da getter büyük harfe çevirecek
                } else {
                    mindhuntersCampaignDto.setStatus("draft"); // DTO'da getter "Draft" yapacak
                }
            } else {
                mindhuntersCampaignDto.setStatus("draft"); // DTO'da getter "Draft" yapacak
            }
            
            mindhuntersCampaignDto.setCampaignType(campaignIUDto.getCampaignType());
            mindhuntersCampaignDto.setStartDate(campaignIUDto.getStartDate());
            mindhuntersCampaignDto.setEndDate(campaignIUDto.getEndDate());
            
            mindhuntersCampaignIntegrationService.sendCampaignToMindhunters(mindhuntersCampaignDto);
        } catch (Exception e) {
            log.error("Mindhunters'a campaign gönderilirken hata oluştu: " + e.getMessage(), e);
        }
        
        return CampaignDto.entityToDto(saved);
    }

    @Override
    public Optional<CampaignDto> updateCampaign(UUID uuid, CampaignIUDto campaignIUDto) {
        return campaignRepository.findByUuid(uuid).map(campaign -> {
            campaign.setName(campaignIUDto.getName());
            campaign.setDescription(campaignIUDto.getDescription());
            campaign.setStatus(campaignIUDto.getStatus());
            campaign.setCampaignType(campaignIUDto.getCampaignType());
            campaign.setStartDate(campaignIUDto.getStartDate());
            campaign.setEndDate(campaignIUDto.getEndDate());
            campaign.setRules(campaignIUDto.getRules());
            campaign.setAgentId(campaignIUDto.getAgentId());
            campaign.setAgentAppId(campaignIUDto.getAgentAppId());
            Campaign updated = campaignRepository.save(campaign);
            return CampaignDto.entityToDto(updated);
        });
    }

    @Override
    public boolean deleteCampaign(UUID uuid) {
        if (campaignRepository.existsById(uuid)) {
            campaignRepository.deleteById(uuid);
            return true;
        }
        return false;
    }
} 