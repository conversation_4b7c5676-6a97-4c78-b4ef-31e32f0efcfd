package com.crm.api.crm_service.controller;

import com.crm.api.crm_service.dto.ContactDto;
import com.crm.api.crm_service.dto.ContactIUDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;


import java.util.UUID;

public interface IContactController {
    ResponseEntity<Page<ContactDto>> getAllContacts(Pageable pageable);
    ResponseEntity<ContactDto> getContactByUuid(UUID uuid);
    ResponseEntity<ContactDto> createContact(ContactIUDto contactIUDto);
    ResponseEntity<ContactDto> updateContact(UUID uuid, ContactIUDto contactIUDto);
    ResponseEntity<Void> deleteContact(UUID uuid);
    ResponseEntity<Void> addTagToContact(UUID uuid, Long tagId);
    ResponseEntity<Void> removeTagFromContact(UUID uuid, Long tagId);
}
