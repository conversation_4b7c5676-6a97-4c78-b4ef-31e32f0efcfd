package com.crm.api.crm_service.security;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Collections;

@Component
@RequiredArgsConstructor
@Slf4j
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtUtil jwtUtil;

    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                  HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        final String authorizationHeader = request.getHeader("Authorization");
        
        String username = null;
        String jwt = null;
        String role = null;

        // Bearer token kontrolü
        if (authorizationHeader != null && authorizationHeader.startsWith("Bearer ")) {
            jwt = authorizationHeader.substring(7);
            try {
                username = jwtUtil.extractUsername(jwt);
                role = jwtUtil.extractRole(jwt);

                log.info("JWT token found for user: {} with role: {} for path: {}", username, role, request.getRequestURI());
                log.info("JWT token (first 50 chars): {}", jwt.substring(0, Math.min(50, jwt.length())));
            } catch (Exception e) {
                log.error("JWT token parsing error: {}", e.getMessage());
            }
        } else {
            log.warn("No valid Authorization header found for path: {}", request.getRequestURI());
        }

        // Token geçerli ve kullanıcı henüz authenticate edilmemişse
        if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
            if (jwtUtil.validateToken(jwt)) {
                // Authentication object oluştur
                UsernamePasswordAuthenticationToken authToken = 
                    new UsernamePasswordAuthenticationToken(
                        username, 
                        null, 
                        Collections.singletonList(new SimpleGrantedAuthority("ROLE_" + role))
                    );
                
                authToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authToken);
                
                log.info("User {} authenticated successfully with role: {} (Authority: ROLE_{}) for path: {}", username, role, role, request.getRequestURI());
            } else {
                log.warn("Invalid JWT token for user: {}", username);
            }
        }

        filterChain.doFilter(request, response);
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
        String path = request.getRequestURI();
        
        // Bu endpoint'ler için JWT kontrolü yapma
        return path.equals("/api/auth/token") ||
               path.equals("/api/auth/register") ||
               path.equals("/api/auth/login") ||
               path.startsWith("/api/v1/form/") ||  // Form verisi için public endpoint
               path.startsWith("/webhook/") ||  // Webhook kendi secret key kontrolü yapar
               path.startsWith("/swagger-ui/") ||
               path.startsWith("/v3/api-docs");
    }
}
