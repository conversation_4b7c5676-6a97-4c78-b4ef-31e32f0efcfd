package com.crm.api.crm_service.service.impl;

import com.crm.api.crm_service.model.WebhookEvent;
import com.crm.api.crm_service.repository.WebhookEventRepository;
import com.crm.api.crm_service.service.WebhookEventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
public class WebhookEventServiceImpl implements WebhookEventService {
    @Autowired
    private WebhookEventRepository webhookEventRepository;

    @Override
    public WebhookEvent save(WebhookEvent event) {
        return webhookEventRepository.save(event);
    }

    @Override
    public List<WebhookEvent> findAll() {
        return webhookEventRepository.findAll();
    }

    @Override
    public Optional<WebhookEvent> findById(UUID id) {
        return webhookEventRepository.findById(id);
    }

    @Override
    public List<WebhookEvent> findByCallId(String callId) {
        return webhookEventRepository.findByCallId(callId);
    }

    @Override
    public List<WebhookEvent> findByPhoneNumber(String phoneNumber) {
        return webhookEventRepository.findByNumber(phoneNumber);
    }

    @Override
    public void deleteById(UUID id) {
        webhookEventRepository.deleteById(id);
    }
}
