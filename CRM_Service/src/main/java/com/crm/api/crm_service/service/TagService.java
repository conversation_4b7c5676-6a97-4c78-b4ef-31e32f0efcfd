package com.crm.api.crm_service.service;

import com.crm.api.crm_service.dto.TagDto;
import com.crm.api.crm_service.dto.TagIUDto;

import java.util.List;
import java.util.Optional;

public interface TagService {
    List<TagDto> getAllTags();
    Optional<TagDto> getTagById(Long id);
    TagDto createTag(TagIUDto tagIUDto);
    Optional<TagDto> updateTag(Long id, TagIUDto tagIUDto);
    boolean deleteTag(Long id);
} 