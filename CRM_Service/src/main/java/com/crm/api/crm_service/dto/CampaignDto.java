package com.crm.api.crm_service.dto;

import com.crm.api.crm_service.model.Campaign;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CampaignDto implements Serializable {
    private UUID uuid;
    private String name;
    private String description;
    private String status;
    @JsonProperty("campaign_type")
    private String campaignType;
    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    private LocalDateTime startDate;
    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    private LocalDateTime endDate;
    private Map<String, Object> rules;
    @JsonProperty("agent_id")
    private Long agentId;
    @JsonProperty("agent_app_id")
    private Long agentAppId;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime createdAt;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime updatedAt;

    public static CampaignDto entityToDto(Campaign campaign) {
        if (campaign == null) return null;
        return CampaignDto.builder()
                .uuid(campaign.getUuid())
                .name(campaign.getName())
                .description(campaign.getDescription())
                .status(campaign.getStatus())
                .campaignType(campaign.getCampaignType())
                .startDate(campaign.getStartDate())
                .endDate(campaign.getEndDate())
                .rules(campaign.getRules())
                .agentId(campaign.getAgentId())
                .agentAppId(campaign.getAgentAppId())
                .createdAt(campaign.getCreatedAt())
                .updatedAt(campaign.getUpdatedAt())
                .build();
    }
} 