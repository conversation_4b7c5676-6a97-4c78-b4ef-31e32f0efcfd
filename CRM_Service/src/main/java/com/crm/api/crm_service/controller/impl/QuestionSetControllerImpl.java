package com.crm.api.crm_service.controller.impl;

import com.crm.api.crm_service.controller.IQuestionSetController;
import com.crm.api.crm_service.dto.QuestionSetIUDto;
import com.crm.api.crm_service.dto.QuestionSetDto;
import com.crm.api.crm_service.service.QuestionSetService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/question-sets")
@CrossOrigin(origins = "*")
@Slf4j
public class QuestionSetControllerImpl implements IQuestionSetController {
    
    @Autowired
    private QuestionSetService questionSetService;
    
    @Override
    @GetMapping
    public ResponseEntity<List<QuestionSetDto>> getAllQuestionSets() {
        log.info("GET /question-sets");
        List<QuestionSetDto> questionSets = questionSetService.getAllQuestionSets();
        return ResponseEntity.ok(questionSets);
    }
    
    @Override
    @GetMapping("/{id}")
    public ResponseEntity<QuestionSetDto> getQuestionSetById(@PathVariable Long id) {
        log.info("GET /question-sets/{}", id);
        Optional<QuestionSetDto> questionSet = questionSetService.getQuestionSetById(id);
        return questionSet.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    @Override
    @PostMapping
    public ResponseEntity<QuestionSetDto> createQuestionSet(@RequestBody QuestionSetIUDto questionSetIUDto) {
        log.info("POST /question-sets");
        QuestionSetDto createdQuestionSet = questionSetService.createQuestionSet(questionSetIUDto);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdQuestionSet);
    }
    
    @Override
    @PutMapping("/{id}")
    public ResponseEntity<QuestionSetDto> updateQuestionSet(@PathVariable Long id, 
                                                          @RequestBody QuestionSetIUDto questionSetIUDto) {
        log.info("PUT /question-sets/{}", id);
        Optional<QuestionSetDto> updatedQuestionSet = questionSetService.updateQuestionSet(id, questionSetIUDto);
        return updatedQuestionSet.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    @Override
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteQuestionSet(@PathVariable Long id) {
        log.info("DELETE /question-sets/{}", id);
        boolean deleted = questionSetService.deleteQuestionSet(id);
        return deleted ? ResponseEntity.noContent().build() : ResponseEntity.notFound().build();
    }
} 