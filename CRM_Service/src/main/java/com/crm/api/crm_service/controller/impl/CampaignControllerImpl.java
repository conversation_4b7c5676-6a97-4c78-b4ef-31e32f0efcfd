package com.crm.api.crm_service.controller.impl;

import com.crm.api.crm_service.controller.ICampaignController;
import com.crm.api.crm_service.dto.CampaignDto;
import com.crm.api.crm_service.dto.CampaignIUDto;
import com.crm.api.crm_service.service.CampaignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/campaigns")
@CrossOrigin(origins = "*")
@Slf4j
public class CampaignControllerImpl implements ICampaignController {
    @Autowired
    private CampaignService campaignService;

    @GetMapping
    public ResponseEntity<Page<CampaignDto>> getAllCampaigns(Pageable pageable) {
        log.info("GET /api/v1/campaigns");
        Page<CampaignDto> campaigns = campaignService.getAllCampaigns(pageable);
        return ResponseEntity.ok(campaigns);
    }

    @GetMapping("/{uuid}")
    public ResponseEntity<CampaignDto> getCampaignByUuid(@PathVariable UUID uuid) {
        log.info("GET /api/v1/campaigns/{}", uuid);
        Optional<CampaignDto> campaign = campaignService.getCampaignByUuid(uuid);
        return campaign.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    @PostMapping
    public ResponseEntity<CampaignDto> createCampaign(@RequestBody CampaignIUDto campaignIUDto) {
        log.info("POST /api/v1/campaigns - {}", campaignIUDto.getName());
        CampaignDto createdCampaign = campaignService.createCampaign(campaignIUDto);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdCampaign);
    }

    @PutMapping("/{uuid}")
    public ResponseEntity<CampaignDto> updateCampaign(@PathVariable UUID uuid, @RequestBody CampaignIUDto campaignIUDto) {
        log.info("PUT /api/v1/campaigns/{}", uuid);
        Optional<CampaignDto> updatedCampaign = campaignService.updateCampaign(uuid, campaignIUDto);
        return updatedCampaign.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    @DeleteMapping("/{uuid}")
    public ResponseEntity<Void> deleteCampaign(@PathVariable UUID uuid) {
        log.info("DELETE /api/v1/campaigns/{}", uuid);
        boolean deleted = campaignService.deleteCampaign(uuid);
        return deleted ? ResponseEntity.noContent().build() : ResponseEntity.notFound().build();
    }
} 