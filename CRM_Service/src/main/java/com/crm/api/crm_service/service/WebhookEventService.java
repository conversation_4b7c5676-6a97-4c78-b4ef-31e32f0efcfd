package com.crm.api.crm_service.service;

import com.crm.api.crm_service.model.WebhookEvent;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface WebhookEventService {
    WebhookEvent save(WebhookEvent event);
    List<WebhookEvent> findAll();
    Optional<WebhookEvent> findById(UUID id);
    List<WebhookEvent> findByCallId(String callId);
    List<WebhookEvent> findByPhoneNumber(String phoneNumber);
    void deleteById(UUID id);
}
