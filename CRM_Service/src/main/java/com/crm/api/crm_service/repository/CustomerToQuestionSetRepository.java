package com.crm.api.crm_service.repository;

import com.crm.api.crm_service.model.CustomerToQuestionSet;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CustomerToQuestionSetRepository extends JpaRepository<CustomerToQuestionSet, Long> {
    
    List<CustomerToQuestionSet> findByCustomerId(Long customerId);
    
    List<CustomerToQuestionSet> findByQuestionSetId(Long questionSetId);
    
    void deleteByCustomerIdAndQuestionSetId(Long customerId, Long questionSetId);
} 