package com.crm.api.crm_service.service.impl;

import com.crm.api.crm_service.dto.CallLogIUDto;
import com.crm.api.crm_service.dto.CallLogDto;
import com.crm.api.crm_service.model.CallLog;
import com.crm.api.crm_service.model.Customer;
import com.crm.api.crm_service.repository.CallLogRepository;
import com.crm.api.crm_service.repository.CustomerRepository;
import com.crm.api.crm_service.service.CallLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class CallLogServiceImpl implements CallLogService {
    
    @Autowired
    private CallLogRepository callLogRepository;
    
    @Autowired
    private CustomerRepository customerRepository;
    
    @Override
    public List<CallLogDto> getAllCallLogs() {
        List<CallLog> callLogs = callLogRepository.findAll();
        List<CallLogDto> callLogDtos = CallLogDto.entityListToDtoList(callLogs);
        log.info("Found {} call logs", callLogDtos.size());
        return callLogDtos;
    }
    
    @Override
    public Optional<CallLogDto> getCallLogById(Long id) {
        Optional<CallLog> callLog = callLogRepository.findById(id);
        Optional<CallLogDto> callLogDto = callLog.map(CallLogDto::entityToDto);
        if (callLogDto.isEmpty()) {
            log.warn("Call log not found with ID: {}", id);
        }
        return callLogDto;
    }
    
    @Override
    public List<CallLogDto> getCallLogsByCustomerId(Long customerId) {
        List<CallLog> callLogs = callLogRepository.findByCustomerId(customerId);
        List<CallLogDto> callLogDtos = CallLogDto.entityListToDtoList(callLogs);
        log.info("Found {} call logs for customer ID: {}", callLogDtos.size(), customerId);
        return callLogDtos;
    }
    
    @Override
    public List<CallLogDto> getCallLogsByAgent(String agent) {
        List<CallLog> callLogs = callLogRepository.findByAgent(agent);
        List<CallLogDto> callLogDtos = CallLogDto.entityListToDtoList(callLogs);
        log.info("Found {} call logs for agent: {}", callLogDtos.size(), agent);
        return callLogDtos;
    }
    
    @Override
    public List<CallLogDto> getCallLogsByResult(CallLog.CallResult result) {
        List<CallLog> callLogs = callLogRepository.findByResult(result);
        List<CallLogDto> callLogDtos = CallLogDto.entityListToDtoList(callLogs);
        log.info("Found {} call logs with result: {}", callLogDtos.size(), result);
        return callLogDtos;
    }
    
    @Override
    public List<CallLogDto> getCallLogsByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        List<CallLog> callLogs = callLogRepository.findByCallDateBetween(startDate, endDate);
        List<CallLogDto> callLogDtos = CallLogDto.entityListToDtoList(callLogs);
        log.info("Found {} call logs in date range", callLogDtos.size());
        return callLogDtos;
    }
    
    @Override
    public CallLogDto createCallLog(CallLogIUDto callLogIUDto) {
        Optional<Customer> customer = customerRepository.findById(callLogIUDto.getCustomerId());
        if (customer.isEmpty()) {
            log.error("Customer not found: {}", callLogIUDto.getCustomerId());
            throw new RuntimeException("Müşteri bulunamadı: " + callLogIUDto.getCustomerId());
        }
        
        CallLog callLog = new CallLog();
        callLog.setCustomer(customer.get());
        callLog.setCallDate(callLogIUDto.getCallDate());
        callLog.setResult(callLogIUDto.getResult());
        callLog.setAudioUrl(callLogIUDto.getAudioUrl());
        callLog.setNote(callLogIUDto.getNote());
        callLog.setAgent(callLogIUDto.getAgent());
        
        CallLog savedCallLog = callLogRepository.save(callLog);
        CallLogDto callLogDto = CallLogDto.entityToDto(savedCallLog);
        log.info("Call log created: ID: {}", callLogDto.getId());
        return callLogDto;
    }
    
    @Override
    public Optional<CallLogDto> updateCallLog(Long id, CallLogIUDto callLogIUDto) {
        return callLogRepository.findById(id)
                .map(callLog -> {
                    Optional<Customer> customer = customerRepository.findById(callLogIUDto.getCustomerId());
                    if (customer.isPresent()) {
                        callLog.setCustomer(customer.get());
                    }
                    callLog.setCallDate(callLogIUDto.getCallDate());
                    callLog.setResult(callLogIUDto.getResult());
                    callLog.setAudioUrl(callLogIUDto.getAudioUrl());
                    callLog.setNote(callLogIUDto.getNote());
                    callLog.setAgent(callLogIUDto.getAgent());
                    
                    CallLog updatedCallLog = callLogRepository.save(callLog);
                    CallLogDto callLogDto = CallLogDto.entityToDto(updatedCallLog);
                    log.info("Call log updated: ID: {}", callLogDto.getId());
                    return callLogDto;
                })
                .or(() -> {
                    log.warn("Call log not found for update: ID: {}", id);
                    return Optional.empty();
                });
    }
    
    @Override
    public boolean deleteCallLog(Long id) {
        if (callLogRepository.existsById(id)) {
            callLogRepository.deleteById(id);
            log.info("Call log deleted: ID: {}", id);
            return true;
        } else {
            log.warn("Call log not found for deletion: ID: {}", id);
            return false;
        }
    }
} 