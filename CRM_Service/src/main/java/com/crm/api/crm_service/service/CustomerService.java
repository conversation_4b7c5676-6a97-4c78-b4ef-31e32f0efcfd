package com.crm.api.crm_service.service;

import com.crm.api.crm_service.dto.CustomerIUDto;
import com.crm.api.crm_service.dto.CustomerDto;
import com.crm.api.crm_service.model.Customer;

import java.util.List;
import java.util.Optional;

public interface CustomerService {
    
    List<CustomerDto> getAllCustomers();
    
    Optional<CustomerDto> getCustomerById(Long id);
    
    Optional<CustomerDto> getCustomerByEmail(String email);
    
    Optional<CustomerDto> getCustomerByUsername(String username);
    
    CustomerDto createCustomer(CustomerIUDto customerIUDto);
    
    Optional<CustomerDto> updateCustomer(Long id, CustomerIUDto customerIUDto);
    
    boolean deleteCustomer(Long id);
    
    boolean existsByEmail(String email);
    
    boolean existsByUsername(String username);
} 