package com.crm.api.crm_service.service;

import com.crm.api.crm_service.dto.UserDto;
import com.crm.api.crm_service.dto.UserLoginDto;
import com.crm.api.crm_service.dto.UserRegistrationDto;
import com.crm.api.crm_service.model.Role;
import com.crm.api.crm_service.model.User;
import com.crm.api.crm_service.repository.RoleRepository;
import com.crm.api.crm_service.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserService {
    
    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final PasswordEncoder passwordEncoder;
    
    public UserDto registerUser(UserRegistrationDto registrationDto) {
        // Kullanıcı adı kontrolü
        if (userRepository.existsByUsername(registrationDto.getUsername())) {
            throw new RuntimeException("Username already exists");
        }

        // USER rolünü al (yoksa oluştur)
        Role userRole = roleRepository.findByName("USER")
                .orElseGet(() -> {
                    Role newRole = new Role();
                    newRole.setName("USER");
                    newRole.setDescription("Default user role");
                    return roleRepository.save(newRole);
                });

        // Yeni kullanıcı oluştur
        User user = new User();
        user.setUsername(registrationDto.getUsername());
        user.setPasswordHash(passwordEncoder.encode(registrationDto.getPassword()));
        user.setRole(userRole);
        user.setIsActive(true);

        User savedUser = userRepository.save(user);
        log.info("New user registered: {}", savedUser.getUsername());

        return UserDto.fromEntity(savedUser);
    }
    
    public Optional<User> authenticateUser(UserLoginDto loginDto) {
        Optional<User> userOpt = userRepository.findByUsername(loginDto.getUsername());
        
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            if (user.getIsActive() && passwordEncoder.matches(loginDto.getPassword(), user.getPasswordHash())) {
                return Optional.of(user);
            }
        }
        
        return Optional.empty();
    }
    
    public List<UserDto> getAllUsers() {
        return userRepository.findAll().stream()
                .map(UserDto::fromEntity)
                .collect(Collectors.toList());
    }
    
    public Optional<UserDto> getUserById(Long id) {
        return userRepository.findById(id)
                .map(UserDto::fromEntity);
    }
    
    public Optional<User> findByUsername(String username) {
        return userRepository.findByUsername(username);
    }
    
    public UserDto updateUserRole(Long userId, String roleName) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        
        Role role = roleRepository.findByName(roleName)
                .orElseThrow(() -> new RuntimeException("Role not found"));
        
        user.setRole(role);
        User savedUser = userRepository.save(user);
        
        log.info("User {} role updated to {}", user.getUsername(), roleName);
        return UserDto.fromEntity(savedUser);
    }
    

}
