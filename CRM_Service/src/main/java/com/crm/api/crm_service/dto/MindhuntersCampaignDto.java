package com.crm.api.crm_service.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MindhuntersCampaignDto {
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("description")
    private String description;
    
    @JsonProperty("status")
    private String status;
    
    @JsonProperty("campaign_type")
    private String campaignType;
    
    @JsonProperty("start_date")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    private LocalDateTime startDate;
    
    @JsonProperty("end_date")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    private LocalDateTime endDate;
    
    // Status'u "Draft" olarak büyük harfle döndüren getter
    public String getStatus() {
        if (status != null) {
            // Eğer "draft" veya "active" ise büyük harfle çevir
            if (status.toLowerCase().equals("draft")) {
                return "Draft";
            } else if (status.toLowerCase().equals("active")) {
                return "Active";
            }
            return status; // Diğer durumlarda olduğu gibi bırak
        }
        return "Draft"; // Varsayılan değer büyük harfle
    }
} 