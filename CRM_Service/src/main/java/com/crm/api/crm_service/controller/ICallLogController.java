package com.crm.api.crm_service.controller;

import com.crm.api.crm_service.dto.CallLogIUDto;
import com.crm.api.crm_service.dto.CallLogDto;
import com.crm.api.crm_service.model.CallLog;
import org.springframework.http.ResponseEntity;

import java.time.LocalDateTime;
import java.util.List;

public interface ICallLogController {
    
    ResponseEntity<List<CallLogDto>> getAllCallLogs();
    
    ResponseEntity<CallLogDto> getCallLogById(Long id);
    
    ResponseEntity<List<CallLogDto>> getCallLogsByCustomerId(Long customerId);
    
    ResponseEntity<List<CallLogDto>> getCallLogsByAgent(String agent);
    
    ResponseEntity<List<CallLogDto>> getCallLogsByResult(CallLog.CallResult result);
    
    ResponseEntity<List<CallLogDto>> getCallLogsByDateRange(LocalDateTime startDate, LocalDateTime endDate);
    
    ResponseEntity<CallLogDto> createCallLog(CallLogIUDto callLogIUDto);
    
    ResponseEntity<CallLogDto> updateCallLog(Long id, CallLogIUDto callLogIUDto);
    
    ResponseEntity<Void> deleteCallLog(Long id);
} 