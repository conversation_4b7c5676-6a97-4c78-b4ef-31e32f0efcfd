package com.crm.api.crm_service.controller;

import com.crm.api.crm_service.dto.UserDto;
import com.crm.api.crm_service.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/users")
@RequiredArgsConstructor
@Slf4j
public class UserController {
    
    private final UserService userService;
    
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<List<UserDto>> getAllUsers() {
        log.info("GET /api/v1/users - Admin accessing user list");
        List<UserDto> users = userService.getAllUsers();
        return ResponseEntity.ok(users);
    }
    
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<UserDto> getUserById(@PathVariable Long id) {
        log.info("GET /api/v1/users/{} - Admin accessing user details", id);
        return userService.getUserById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    @PutMapping("/{id}/role")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateUserRole(@PathVariable Long id, @RequestBody Map<String, String> request) {
        try {
            String roleName = request.get("roleName");
            if (roleName == null || (!roleName.equals("USER") && !roleName.equals("ADMIN"))) {
                return ResponseEntity.badRequest()
                    .body(Map.of("error", "Invalid role. Must be USER or ADMIN"));
            }
            
            UserDto updatedUser = userService.updateUserRole(id, roleName);
            log.info("Admin updated user {} role to {}", id, roleName);
            
            return ResponseEntity.ok(Map.of(
                "message", "User role updated successfully",
                "user", updatedUser
            ));
            
        } catch (RuntimeException e) {
            log.warn("Role update failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .body(Map.of("error", e.getMessage()));
        }
    }
}
