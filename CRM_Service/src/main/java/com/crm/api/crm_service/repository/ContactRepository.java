package com.crm.api.crm_service.repository;

import com.crm.api.crm_service.model.Contact;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface ContactRepository extends JpaRepository<Contact, UUID> {
    Optional<Contact> findByUuid(UUID uuid);
    Optional<Contact> findByEmail(String email);
    Optional<Contact> findByPhoneNumber(String phoneNumber);
    Optional<Contact> findByEmailAndPhoneNumber(String email, String phoneNumber);
}