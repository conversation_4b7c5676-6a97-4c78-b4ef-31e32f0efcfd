package com.crm.api.crm_service.service.impl;

import com.crm.api.crm_service.dto.CustomerIUDto;
import com.crm.api.crm_service.dto.CustomerDto;
import com.crm.api.crm_service.model.Customer;
import com.crm.api.crm_service.repository.CustomerRepository;
import com.crm.api.crm_service.service.CustomerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class CustomerServiceImpl implements CustomerService {
    
    @Autowired
    private CustomerRepository customerRepository;
    
    @Override
    public List<CustomerDto> getAllCustomers() {
        List<Customer> customers = customerRepository.findAll();
        List<CustomerDto> customerDtos = CustomerDto.entityListToDtoList(customers);
        log.info("Found {} customers", customerDtos.size());
        return customerDtos;
    }
    
    @Override
    public Optional<CustomerDto> getCustomerById(Long id) {
        Optional<Customer> customer = customerRepository.findById(id);
        Optional<CustomerDto> customerDto = customer.map(CustomerDto::entityToDto);
        if (customerDto.isEmpty()) {
            log.warn("Customer not found with ID: {}", id);
        }
        return customerDto;
    }
    
    @Override
    public Optional<CustomerDto> getCustomerByEmail(String email) {
        Optional<Customer> customer = customerRepository.findByEmail(email);
        Optional<CustomerDto> customerDto = customer.map(CustomerDto::entityToDto);
        if (customerDto.isEmpty()) {
            log.warn("Customer not found with email: {}", email);
        }
        return customerDto;
    }
    
    @Override
    public Optional<CustomerDto> getCustomerByUsername(String username) {
        Optional<Customer> customer = customerRepository.findByUsername(username);
        Optional<CustomerDto> customerDto = customer.map(CustomerDto::entityToDto);
        if (customerDto.isEmpty()) {
            log.warn("Customer not found with username: {}", username);
        }
        return customerDto;
    }
    
    @Override
    public CustomerDto createCustomer(CustomerIUDto customerIUDto) {
        Customer customer = new Customer();
        customer.setName(customerIUDto.getName());
        customer.setSurname(customerIUDto.getSurname());
        customer.setUsername(customerIUDto.getUsername());
        customer.setEmail(customerIUDto.getEmail());
        customer.setPhoneNumber(customerIUDto.getPhoneNumber());
        
        Customer savedCustomer = customerRepository.save(customer);
        CustomerDto customerDto = CustomerDto.entityToDto(savedCustomer);
        log.info("Customer created: {}", customerDto.getUsername());
        return customerDto;
    }
    
    @Override
    public Optional<CustomerDto> updateCustomer(Long id, CustomerIUDto customerIUDto) {
        return customerRepository.findById(id)
                .map(customer -> {
                    customer.setName(customerIUDto.getName());
                    customer.setSurname(customerIUDto.getSurname());
                    customer.setUsername(customerIUDto.getUsername());
                    customer.setEmail(customerIUDto.getEmail());
                    customer.setPhoneNumber(customerIUDto.getPhoneNumber());
                    
                    Customer updatedCustomer = customerRepository.save(customer);
                    CustomerDto customerDto = CustomerDto.entityToDto(updatedCustomer);
                    log.info("Customer updated: {}", customerDto.getUsername());
                    return customerDto;
                })
                .or(() -> {
                    log.warn("Customer not found for update: ID: {}", id);
                    return Optional.empty();
                });
    }
    
    @Override
    public boolean deleteCustomer(Long id) {
        if (customerRepository.existsById(id)) {
            customerRepository.deleteById(id);
            log.info("Customer deleted: ID: {}", id);
            return true;
        } else {
            log.warn("Customer not found for deletion: ID: {}", id);
            return false;
        }
    }
    
    @Override
    public boolean existsByEmail(String email) {
        return customerRepository.existsByEmail(email);
    }
    
    @Override
    public boolean existsByUsername(String username) {
        return customerRepository.existsByUsername(username);
    }
} 