package com.crm.api.crm_service.service;

import com.crm.api.crm_service.dto.ContactDto;
import com.crm.api.crm_service.dto.ContactIUDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface ContactService {
    Page<ContactDto> getAllContacts(Pageable pageable);
    List<ContactDto> getAllContactsWithoutPagination();
    Optional<ContactDto> getContactByUuid(UUID uuid);
    ContactDto createContact(ContactIUDto contactIUDto);
    Optional<ContactDto> updateContact(UUID uuid, ContactIUDto contactIUDto);
    boolean deleteContact(UUID uuid);
    boolean addTagToContact(UUID uuid, Long tagId);
    boolean removeTagFromContact(UUID uuid, Long tagId);
}