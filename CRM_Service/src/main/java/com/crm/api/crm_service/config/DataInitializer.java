package com.crm.api.crm_service.config;

import com.crm.api.crm_service.model.Role;
import com.crm.api.crm_service.model.User;
import com.crm.api.crm_service.repository.RoleRepository;
import com.crm.api.crm_service.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class DataInitializer implements CommandLineRunner {

    private final RoleRepository roleRepository;
    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        log.info("Initializing roles...");

        // USER rolü
        if (roleRepository.findByName("USER").isEmpty()) {
            Role userRole = new Role();
            userRole.setName("USER");
            userRole.setDescription("Default user role");
            roleRepository.save(userRole);
            log.info("USER role created");
        } else {
            log.info("USER role already exists");
        }

        // ADMIN rolü
        if (roleRepository.findByName("ADMIN").isEmpty()) {
            Role adminRole = new Role();
            adminRole.setName("ADMIN");
            adminRole.setDescription("Administrator role");
            roleRepository.save(adminRole);
            log.info("ADMIN role created");
        } else {
            log.info("ADMIN role already exists");
        }

        // CRM_ADMIN rolü
        if (roleRepository.findByName("CRM_ADMIN").isEmpty()) {
            Role crmAdminRole = new Role();
            crmAdminRole.setName("CRM_ADMIN");
            crmAdminRole.setDescription("CRM Administrator role for Backend2 integration");
            roleRepository.save(crmAdminRole);
            log.info("CRM_ADMIN role created");
        } else {
            log.info("CRM_ADMIN role already exists");
        }

        log.info("Roles initialization completed.");

        // Admin kullanıcısını oluştur
        if (userRepository.findByUsername("<EMAIL>").isEmpty()) {
            Role adminRole = roleRepository.findByName("ADMIN").get();
            
            User adminUser = new User();
            adminUser.setUsername("<EMAIL>");
            adminUser.setPasswordHash(passwordEncoder.encode("admin123"));
            adminUser.setRole(adminRole);
            adminUser.setIsActive(true);
            
            userRepository.save(adminUser);
            log.info("Admin user created: <EMAIL>");
        } else {
            log.info("Admin user already exists");
        }

        log.info("Data initialization completed.");
    }
}
