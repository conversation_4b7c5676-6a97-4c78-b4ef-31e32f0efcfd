package com.crm.api.crm_service.repository;

import com.crm.api.crm_service.model.CallLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface CallLogRepository extends JpaRepository<CallLog, Long> {
    
    List<CallLog> findByCustomerId(Long customerId);
    
    List<CallLog> findByAgent(String agent);
    
    List<CallLog> findByResult(CallLog.CallResult result);
    
    List<CallLog> findByCallDateBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    List<CallLog> findByCustomerIdAndCallDateBetween(Long customerId, LocalDateTime startDate, LocalDateTime endDate);
} 