package com.crm.api.crm_service.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MindhuntersCallDto {
    @JsonProperty("agentId")
    private String agentId;

    @JsonProperty("message")
    private Message message;

    @JsonProperty("prompt")
    private Prompt prompt;

    @JsonProperty("participant")
    private Participant participant;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Message {
        @JsonProperty("start")
        private String start;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Prompt {
        @JsonProperty("overwrite")
        private boolean overwrite;
        @JsonProperty("content")
        private String content;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Participant {
        @JsonProperty("number")
        private String number;
        @JsonProperty("about")
        private String about;
    }
}
 