package com.crm.api.crm_service.controller.impl;

import com.crm.api.crm_service.controller.ICallController;
import com.crm.api.crm_service.dto.CallDto;
import com.crm.api.crm_service.dto.CallIUDto;
import com.crm.api.crm_service.service.CallService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/calls")
@CrossOrigin(origins = "*")
@Slf4j
public class CallControllerImpl implements ICallController {
    @Autowired
    private CallService callService;

    @PostMapping("/call")
    public ResponseEntity<CallDto> createCall(@RequestBody CallIUDto callIUDto) {
        log.info("POST /api/v1/call");
        CallDto createdCall = callService.createCall(callIUDto);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdCall);
    }

    @GetMapping("/calls")
    public ResponseEntity<Page<CallDto>> getAllCalls(Pageable pageable) {
        log.info("GET /api/v1/calls");
        Page<CallDto> calls = callService.getAllCalls(pageable);
        return ResponseEntity.ok(calls);
    }

    @GetMapping("/calls/{uuid}")
    public ResponseEntity<CallDto> getCallByUuid(@PathVariable UUID uuid) {
        log.info("GET /api/v1/calls/{}", uuid);
        Optional<CallDto> call = callService.getCallByUuid(uuid);
        return call.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }
} 