package com.crm.api.crm_service.controller.impl;

import com.crm.api.crm_service.controller.ITagController;
import com.crm.api.crm_service.dto.TagDto;
import com.crm.api.crm_service.dto.TagIUDto;
import com.crm.api.crm_service.service.TagService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/v1/tags")
@CrossOrigin(origins = "*")
@Slf4j
public class TagControllerImpl implements ITagController {
    @Autowired
    private TagService tagService;

    @GetMapping
    public ResponseEntity<List<TagDto>> getAllTags() {
        log.info("GET /api/v1/tags");
        List<TagDto> tags = tagService.getAllTags();
        return ResponseEntity.ok(tags);
    }

    @GetMapping("/{id}")
    public ResponseEntity<TagDto> getTagById(@PathVariable Long id) {
        log.info("GET /api/v1/tags/{}", id);
        Optional<TagDto> tag = tagService.getTagById(id);
        return tag.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    @PostMapping
    public ResponseEntity<TagDto> createTag(@RequestBody TagIUDto tagIUDto) {
        log.info("POST /api/v1/tags");
        TagDto createdTag = tagService.createTag(tagIUDto);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdTag);
    }

    @PutMapping("/{id}")
    public ResponseEntity<TagDto> updateTag(@PathVariable Long id, @RequestBody TagIUDto tagIUDto) {
        log.info("PUT /api/v1/tags/{}", id);
        Optional<TagDto> updatedTag = tagService.updateTag(id, tagIUDto);
        return updatedTag.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteTag(@PathVariable Long id) {
        log.info("DELETE /api/v1/tags/{}", id);
        boolean deleted = tagService.deleteTag(id);
        return deleted ? ResponseEntity.noContent().build() : ResponseEntity.notFound().build();
    }
} 