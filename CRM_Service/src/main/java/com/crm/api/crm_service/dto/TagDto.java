package com.crm.api.crm_service.dto;

import com.crm.api.crm_service.model.Tag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TagDto implements Serializable {
    private Long id;
    private String name;

    public static TagDto entityToDto(Tag tag) {
        if (tag == null) return null;
        return TagDto.builder()
                .id(tag.getId())
                .name(tag.getName())
                .build();
    }
} 