package com.crm.api.crm_service.service.impl;

import com.crm.api.crm_service.dto.QuestionSetIUDto;
import com.crm.api.crm_service.dto.QuestionSetDto;
import com.crm.api.crm_service.model.QuestionSet;
import com.crm.api.crm_service.repository.QuestionSetRepository;
import com.crm.api.crm_service.service.QuestionSetService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class QuestionSetServiceImpl implements QuestionSetService {
    
    @Autowired
    private QuestionSetRepository questionSetRepository;
    
    @Override
    public List<QuestionSetDto> getAllQuestionSets() {
        List<QuestionSet> questionSets = questionSetRepository.findAll();
        List<QuestionSetDto> questionSetDtos = QuestionSetDto.entityListToDtoList(questionSets);
        log.info("Found {} question sets", questionSetDtos.size());
        return questionSetDtos;
    }
    
    @Override
    public Optional<QuestionSetDto> getQuestionSetById(Long id) {
        Optional<QuestionSet> questionSet = questionSetRepository.findById(id);
        Optional<QuestionSetDto> questionSetDto = questionSet.map(QuestionSetDto::entityToDto);
        if (questionSetDto.isEmpty()) {
            log.warn("Question set not found with ID: {}", id);
        }
        return questionSetDto;
    }
    
    @Override
    public QuestionSetDto createQuestionSet(QuestionSetIUDto questionSetIUDto) {
        QuestionSet questionSet = new QuestionSet();
        questionSet.setQuestion(questionSetIUDto.getQuestion());
        
        QuestionSet savedQuestionSet = questionSetRepository.save(questionSet);
        QuestionSetDto questionSetDto = QuestionSetDto.entityToDto(savedQuestionSet);
        log.info("Question set created: ID: {}", questionSetDto.getId());
        return questionSetDto;
    }
    
    @Override
    public Optional<QuestionSetDto> updateQuestionSet(Long id, QuestionSetIUDto questionSetIUDto) {
        return questionSetRepository.findById(id)
                .map(questionSet -> {
                    questionSet.setQuestion(questionSetIUDto.getQuestion());
                    QuestionSet updatedQuestionSet = questionSetRepository.save(questionSet);
                    QuestionSetDto questionSetDto = QuestionSetDto.entityToDto(updatedQuestionSet);
                    log.info("Question set updated: ID: {}", questionSetDto.getId());
                    return questionSetDto;
                })
                .or(() -> {
                    log.warn("Question set not found for update: ID: {}", id);
                    return Optional.empty();
                });
    }
    
    @Override
    public boolean deleteQuestionSet(Long id) {
        if (questionSetRepository.existsById(id)) {
            questionSetRepository.deleteById(id);
            log.info("Question set deleted: ID: {}", id);
            return true;
        } else {
            log.warn("Question set not found for deletion: ID: {}", id);
            return false;
        }
    }
} 