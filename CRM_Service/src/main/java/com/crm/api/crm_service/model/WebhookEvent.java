package com.crm.api.crm_service.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Table(name = "webhook_events")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class WebhookEvent {
    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "uuid", updatable = false, nullable = false)
    private UUID uuid;

    @Column(name = "event")
    private String event;

    @Column(name = "call_id")
    private String callId;

    @Column(name = "analysis", columnDefinition = "text")
    private String analysis;

    @Column(name = "duration")
    private Long duration;

    @Column(name = "conversation", columnDefinition = "text")
    private String conversation;

    @Column(name = "end_reason")
    private String endReason;

    @Column(name = "pipeline", columnDefinition = "jsonb")
    private String pipeline;

    @Column(name = "fields", columnDefinition = "jsonb")
    private String fields;

    @Column(name = "number")
    private String number;

    @Column(name = "webhook_timestamp")
    private Long webhookTimestamp;

    @CreatedDate
    @Column(name = "send_at", updatable = false)
    private LocalDateTime sendAt;

    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}
