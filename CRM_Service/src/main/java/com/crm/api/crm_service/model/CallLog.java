package com.crm.api.crm_service.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

@Entity
@Table(name = "call_logs")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class CallLog {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne
    @JoinColumn(name = "customer_id")
    private Customer customer;
    
    @Column(name = "call_date")
    private LocalDateTime callDate;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "result")
    private CallResult result;
    
    @Column(name = "audio_url")
    private String audioUrl;
    
    @Column(name = "note", columnDefinition = "text")
    private String note;
    
    @Column(name = "agent")
    private String agent;
    
    @CreatedDate
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    public enum CallResult {
        OLUMLU,    // Olumlu
        OLUMSUZ,   // Olumsuz
        CEVAPSIZ   // Cevapsız
    }
} 