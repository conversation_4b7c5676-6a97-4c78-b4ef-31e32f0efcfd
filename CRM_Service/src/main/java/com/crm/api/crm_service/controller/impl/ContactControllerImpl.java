package com.crm.api.crm_service.controller.impl;

import com.crm.api.crm_service.controller.IContactController;
import com.crm.api.crm_service.dto.ContactDto;
import com.crm.api.crm_service.dto.ContactIUDto;
import com.crm.api.crm_service.service.ContactService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/contacts")
@CrossOrigin(origins = "*")
@Slf4j
public class ContactControllerImpl implements IContactController {
    @Autowired
    private ContactService contactService;

    @PreAuthorize("hasAnyRole('ADMIN', 'CRM_ADMIN')")
    @GetMapping
    public ResponseEntity<Page<ContactDto>> getAllContacts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "1000") int size) {
        log.info("GET /api/v1/contacts - page: {}, size: {}", page, size);
        Pageable pageable = PageRequest.of(page, size);
        Page<ContactDto> contacts = contactService.getAllContacts(pageable);
        return ResponseEntity.ok(contacts);
    }

    @PreAuthorize("hasAnyRole('ADMIN', 'CRM_ADMIN')")
    @Override
    public ResponseEntity<Page<ContactDto>> getAllContacts(Pageable pageable) {
        log.info("GET /api/v1/contacts with pageable");
        Page<ContactDto> contacts = contactService.getAllContacts(pageable);
        return ResponseEntity.ok(contacts);
    }


    @PreAuthorize("hasAnyRole('ADMIN', 'CRM_ADMIN')")
    @GetMapping("/{uuid}")
    public ResponseEntity<ContactDto> getContactByUuid(@PathVariable UUID uuid) {
        log.info("GET /api/v1/contacts/{}", uuid);
        Optional<ContactDto> contact = contactService.getContactByUuid(uuid);
        return contact.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    @PreAuthorize("hasAnyRole('ADMIN', 'CRM_ADMIN')")
    @PostMapping
    public ResponseEntity<ContactDto> createContact(@RequestBody ContactIUDto contactIUDto) {
        log.info("POST /api/v1/contacts - Contact oluşturuluyor");
        ContactDto createdContact = contactService.createContact(contactIUDto);
        log.info("Contact başarıyla oluşturuldu: {}", createdContact.getUuid());
        return ResponseEntity.status(HttpStatus.CREATED).body(createdContact);
    }

    @PutMapping("/{uuid}")
    public ResponseEntity<ContactDto> updateContact(@PathVariable UUID uuid, @RequestBody ContactIUDto contactIUDto) {
        log.info("PUT /api/v1/contacts/{}", uuid);
        Optional<ContactDto> updatedContact = contactService.updateContact(uuid, contactIUDto);
        return updatedContact.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    @DeleteMapping("/{uuid}")
    public ResponseEntity<Void> deleteContact(@PathVariable UUID uuid) {
        log.info("DELETE /api/v1/contacts/{}", uuid);
        boolean deleted = contactService.deleteContact(uuid);
        return deleted ? ResponseEntity.noContent().build() : ResponseEntity.notFound().build();
    }

    @PostMapping("/{uuid}/add-tag")
    public ResponseEntity<Void> addTagToContact(@PathVariable UUID uuid, @RequestBody Long tagId) {
        log.info("POST /api/v1/contacts/{}/add-tag", uuid);
        boolean added = contactService.addTagToContact(uuid, tagId);
        return added ? ResponseEntity.ok().build() : ResponseEntity.notFound().build();
    }

    @PostMapping("/{uuid}/remove-tag")
    public ResponseEntity<Void> removeTagFromContact(@PathVariable UUID uuid, @RequestBody Long tagId) {
        log.info("POST /api/v1/contacts/{}/remove-tag", uuid);
        boolean removed = contactService.removeTagFromContact(uuid, tagId);
        return removed ? ResponseEntity.ok().build() : ResponseEntity.notFound().build();
    }
} 