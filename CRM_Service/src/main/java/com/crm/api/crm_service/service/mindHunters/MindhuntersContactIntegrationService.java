package com.crm.api.crm_service.service.mindHunters;

import com.crm.api.crm_service.dto.ContactIUDto;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class MindhuntersContactIntegrationService {

    private final RestTemplate restTemplate = new RestTemplate();

    public void sendContactToMindhunters(ContactIUDto contactIUDto) {
        // ContactIUDto'yu Mindhunters API formatına çevir
        Map<String, Object> mindhuntersContact = convertToMindhuntersFormat(contactIUDto);
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth("3e569edd-caef-42f7-bc1c-ba414fb5fb1c");

        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(mindhuntersContact, headers);

        try {
            log.info("Mindhunters'a gönderilen contact JSON: {}", mindhuntersContact);
            ResponseEntity<String> response = restTemplate.postForEntity(
                    "https://360avantajli.mindhunters.ai/api/v1/contacts",
                    entity,
                    String.class
            );
            log.info("Başarılı! Status: {}", response.getStatusCode());
            log.info("Cevap: {}", response.getBody());
        } catch (Exception e) {
            log.error("Hata oluştu", e);
        }
    }

    private Map<String, Object> convertToMindhuntersFormat(ContactIUDto contactIUDto) {
        Map<String, Object> contact = new HashMap<>();
        
        // Temel contact bilgileri
        contact.put("name", contactIUDto.getName());
        contact.put("surname", contactIUDto.getSurname());
        contact.put("email", contactIUDto.getEmail());
        
        // Telefon numarası - phoneNumber null ise contactPhone'u kullan
        String phoneNumber = contactIUDto.getPhoneNumber();
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            phoneNumber = contactIUDto.getContactPhone();
        }
        contact.put("phone_number", phoneNumber);
        
        // Contact phone alanını da gönder
        if (contactIUDto.getContactPhone() != null) {
            contact.put("contact_phone", contactIUDto.getContactPhone());
        }
        
        contact.put("number_type", contactIUDto.getNumberType() != null ? contactIUDto.getNumberType() : "mobile");
        
        // Country code'u 2 karakter formatına çevir
        String countryCode = contactIUDto.getCountryCode();
        if (countryCode != null && countryCode.startsWith("+")) {
            // +90 -> TR, +1 -> US, +44 -> GB gibi
            switch (countryCode) {
                case "+90" -> countryCode = "TR";
                case "+44" -> countryCode = "GB";
                case "+49" -> countryCode = "DE";
                case "+33" -> countryCode = "FR";
                case "+39" -> countryCode = "IT";
                case "+34" -> countryCode = "ES";
                case "+31" -> countryCode = "NL";
                case "+32" -> countryCode = "BE";
                case "+46" -> countryCode = "SE";
                case "+47" -> countryCode = "NO";
                case "+45" -> countryCode = "DK";
                case "+358" -> countryCode = "FI";
                case "+48" -> countryCode = "PL";
                case "+420" -> countryCode = "CZ";
                case "+36" -> countryCode = "HU";
                case "+40" -> countryCode = "RO";
                case "+421" -> countryCode = "SK";
                case "+386" -> countryCode = "SI";
                case "+385" -> countryCode = "HR";
                case "+387" -> countryCode = "BA";
                case "+382" -> countryCode = "ME";
                case "+389" -> countryCode = "MK";
                case "+355" -> countryCode = "AL";
                case "+381" -> countryCode = "RS";
                case "+43" -> countryCode = "AT";
                case "+41" -> countryCode = "CH";
                case "+352" -> countryCode = "LU";
                case "+371" -> countryCode = "LV";
                case "+372" -> countryCode = "EE";
                case "+370" -> countryCode = "LT";
                case "+375" -> countryCode = "BY";
                case "+380" -> countryCode = "UA";
                case "+373" -> countryCode = "MD";
                case "+374" -> countryCode = "AM";
                case "+995" -> countryCode = "GE";
                case "+994" -> countryCode = "AZ";
                case "+7" -> countryCode = "RU";
                case "+86" -> countryCode = "CN";
                case "+81" -> countryCode = "JP";
                case "+82" -> countryCode = "KR";
                case "+91" -> countryCode = "IN";
                case "+61" -> countryCode = "AU";
                case "+64" -> countryCode = "NZ";
                case "+27" -> countryCode = "ZA";
                case "+20" -> countryCode = "EG";
                case "+212" -> countryCode = "MA";
                case "+216" -> countryCode = "TN";
                case "+213" -> countryCode = "DZ";
                case "+966" -> countryCode = "SA";
                case "+971" -> countryCode = "AE";
                case "+972" -> countryCode = "IL";
                case "+962" -> countryCode = "JO";
                case "+961" -> countryCode = "LB";
                case "+963" -> countryCode = "SY";
                case "+964" -> countryCode = "IQ";
                case "+98" -> countryCode = "IR";
                case "+93" -> countryCode = "AF";
                case "+92" -> countryCode = "PK";
                case "+880" -> countryCode = "BD";
                case "+977" -> countryCode = "NP";
                case "+95" -> countryCode = "MM";
                case "+66" -> countryCode = "TH";
                case "+84" -> countryCode = "VN";
                case "+60" -> countryCode = "MY";
                case "+65" -> countryCode = "SG";
                case "+62" -> countryCode = "ID";
                case "+63" -> countryCode = "PH";
                case "+852" -> countryCode = "HK";
                case "+886" -> countryCode = "TW";
                case "+55" -> countryCode = "BR";
                case "+54" -> countryCode = "AR";
                case "+56" -> countryCode = "CL";
                case "+57" -> countryCode = "CO";
                case "+58" -> countryCode = "VE";
                case "+51" -> countryCode = "PE";
                case "+593" -> countryCode = "EC";
                case "+595" -> countryCode = "PY";
                case "+598" -> countryCode = "UY";
                case "+591" -> countryCode = "BO";
                case "+507" -> countryCode = "PA";
                case "+506" -> countryCode = "CR";
                case "+502" -> countryCode = "GT";
                case "+503" -> countryCode = "SV";
                case "+504" -> countryCode = "HN";
                case "+505" -> countryCode = "NI";
                case "+52" -> countryCode = "MX";
                case "+1" -> countryCode = "US"; // ABD için +1
                default -> countryCode = "TR"; // Varsayılan olarak TR
            }
        } else if (countryCode == null) {
            countryCode = "TR"; // Varsayılan olarak TR
        }
        
        contact.put("country_code", countryCode);
        contact.put("timezone", contactIUDto.getTimezone() != null ? contactIUDto.getTimezone() : "Europe/Istanbul");
        contact.put("status", contactIUDto.getStatus() != null ? contactIUDto.getStatus() : "Active");
        
        // Campaign bilgileri
        if (contactIUDto.getCampaignName() != null) {
            contact.put("campaign_name", contactIUDto.getCampaignName());
        }
        if (contactIUDto.getCampaignTitle() != null) {
            contact.put("campaign_title", contactIUDto.getCampaignTitle());
        }
        if (contactIUDto.getCampaignDescription() != null) {
            contact.put("campaign_description", contactIUDto.getCampaignDescription());
        }
        if (contactIUDto.getCampaignType() != null) {
            contact.put("campaign_type", contactIUDto.getCampaignType());
        }
        
        return contact;
    }
}