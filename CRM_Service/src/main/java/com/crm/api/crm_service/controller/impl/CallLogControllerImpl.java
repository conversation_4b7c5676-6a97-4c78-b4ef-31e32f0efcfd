package com.crm.api.crm_service.controller.impl;

import com.crm.api.crm_service.controller.ICallLogController;
import com.crm.api.crm_service.dto.CallLogIUDto;
import com.crm.api.crm_service.dto.CallLogDto;
import com.crm.api.crm_service.model.CallLog;
import com.crm.api.crm_service.service.CallLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/v1/call-logs")
@CrossOrigin(origins = "*")
@Slf4j
public class CallLogControllerImpl implements ICallLogController {
    
    @Autowired
    private CallLogService callLogService;
    
    @Override
    @GetMapping
    public ResponseEntity<List<CallLogDto>> getAllCallLogs() {
        log.info("GET /call-logs");
        List<CallLogDto> callLogs = callLogService.getAllCallLogs();
        return ResponseEntity.ok(callLogs);
    }
    
    @Override
    @GetMapping("/{id}")
    public ResponseEntity<CallLogDto> getCallLogById(@PathVariable Long id) {
        log.info("GET /call-logs/{}", id);
        Optional<CallLogDto> callLog = callLogService.getCallLogById(id);
        return callLog.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    @Override
    @GetMapping("/customer/{customerId}")
    public ResponseEntity<List<CallLogDto>> getCallLogsByCustomerId(@PathVariable Long customerId) {
        log.info("GET /call-logs/customer/{}", customerId);
        List<CallLogDto> callLogs = callLogService.getCallLogsByCustomerId(customerId);
        return ResponseEntity.ok(callLogs);
    }
    
    @Override
    @GetMapping("/agent/{agent}")
    public ResponseEntity<List<CallLogDto>> getCallLogsByAgent(@PathVariable String agent) {
        log.info("GET /call-logs/agent/{}", agent);
        List<CallLogDto> callLogs = callLogService.getCallLogsByAgent(agent);
        return ResponseEntity.ok(callLogs);
    }
    
    @Override
    @GetMapping("/result/{result}")
    public ResponseEntity<List<CallLogDto>> getCallLogsByResult(@PathVariable CallLog.CallResult result) {
        log.info("GET /call-logs/result/{}", result);
        List<CallLogDto> callLogs = callLogService.getCallLogsByResult(result);
        return ResponseEntity.ok(callLogs);
    }
    
    @Override
    @GetMapping("/date-range")
    public ResponseEntity<List<CallLogDto>> getCallLogsByDateRange(
            @RequestParam LocalDateTime startDate,
            @RequestParam LocalDateTime endDate) {
        log.info("GET /call-logs/date-range?startDate={}&endDate={}", startDate, endDate);
        List<CallLogDto> callLogs = callLogService.getCallLogsByDateRange(startDate, endDate);
        return ResponseEntity.ok(callLogs);
    }
    
    @Override
    @PostMapping
    public ResponseEntity<CallLogDto> createCallLog(@RequestBody CallLogIUDto callLogIUDto) {
        log.info("POST /call-logs - Customer ID: {}", callLogIUDto.getCustomerId());
        try {
            CallLogDto createdCallLog = callLogService.createCallLog(callLogIUDto);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdCallLog);
        } catch (RuntimeException e) {
            log.error("Error creating call log: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }
    
    @Override
    @PutMapping("/{id}")
    public ResponseEntity<CallLogDto> updateCallLog(@PathVariable Long id, 
                                                   @RequestBody CallLogIUDto callLogIUDto) {
        log.info("PUT /call-logs/{}", id);
        Optional<CallLogDto> updatedCallLog = callLogService.updateCallLog(id, callLogIUDto);
        return updatedCallLog.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    @Override
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteCallLog(@PathVariable Long id) {
        log.info("DELETE /call-logs/{}", id);
        boolean deleted = callLogService.deleteCallLog(id);
        return deleted ? ResponseEntity.noContent().build() : ResponseEntity.notFound().build();
    }
} 