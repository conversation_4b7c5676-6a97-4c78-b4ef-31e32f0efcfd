package com.crm.api.crm_service.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class TextToSpeechFormatter {
    
    private static final String[] BIRLER = {"", "bir", "iki", "üç", "dört", "beş", "altı", "yedi", "sekiz", "dokuz"};
    private static final String[] ONLAR = {"", "on", "yirmi", "otuz", "kırk", "elli", "altmış", "yetmiş", "seksen", "doksan"};
    
    /**
     * Formats numbers in text for better speech synthesis
     * @param text The input text that may contain numbers
     * @return Text with numbers formatted for natural speech in Turkish
     */
    public static String formatNumbersForSpeech(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        
        // First handle comma-separated numbers (Turkish currency format like 175,000)
        text = handleCommaSeparatedNumbers(text);
        
        // Pattern to match decimal numbers and integers
        Pattern pattern = Pattern.compile("\\d+(?:\\.\\d+)?");
        Matcher matcher = pattern.matcher(text);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String number = matcher.group();
            String formattedNumber = formatSingleNumber(number);
            matcher.appendReplacement(result, formattedNumber);
        }
        matcher.appendTail(result);
        
        return result.toString();
    }
    
    private static String handleCommaSeparatedNumbers(String text) {
        // Pattern to match numbers with commas (like 175,000)
        Pattern commaPattern = Pattern.compile("(\\d{1,3}(?:,\\d{3})*)");
        Matcher commaMatcher = commaPattern.matcher(text);
        StringBuffer result = new StringBuffer();
        
        while (commaMatcher.find()) {
            String commaNumber = commaMatcher.group();
            // Remove commas and convert to regular number
            String cleanNumber = commaNumber.replace(",", "");
            commaMatcher.appendReplacement(result, cleanNumber);
        }
        commaMatcher.appendTail(result);
        
        return result.toString();
    }
    
    private static String formatSingleNumber(String number) {
        try {
            if (number.contains(".")) {
                // Handle decimal numbers - check if it's a currency format (like 175.000)
                String[] parts = number.split("\\.");
                if (parts.length == 2) {
                    String integerPart = parts[0];
                    String decimalPart = parts[1];
                    
                    // Check if decimal part is all zeros (like 000, 00, 0)
                    if (decimalPart.matches("0+")) {
                        // This is likely a currency format, treat as integer
                        String fullNumber = integerPart + decimalPart;
                        return convertNumberToWords(fullNumber);
                    } else {
                        // This is a real decimal number
                        String integerWords = convertNumberToWords(integerPart);
                        String decimalWords = convertNumberToWords(decimalPart);
                        return String.format("%s nokta %s", integerWords, decimalWords);
                    }
                }
            } else {
                // Handle integers
                return convertNumberToWords(number);
            }
        } catch (Exception e) {
            // If any error occurs, return the original number
            return number;
        }
        return number;
    }
    
    private static String convertNumberToWords(String numberStr) {
        try {
            // Remove any non-digit characters
            String cleanNumber = numberStr.replaceAll("[^0-9]", "");
            
            // If empty after cleaning, return original
            if (cleanNumber.isEmpty()) {
                return numberStr;
            }
            
            // Convert to number
            long number = Long.parseLong(cleanNumber);
            
            // Special case for zero
            if (number == 0) {
                return "sıfır";
            }
            
            return convertToTurkishWords(number);
        } catch (NumberFormatException e) {
            return numberStr; // Return original if conversion fails
        }
    }
    
    private static String convertToTurkishWords(long number) {
        if (number < 10) {
            return BIRLER[(int) number];
        } else if (number < 100) {
            int onlar = (int) (number / 10);
            int birler = (int) (number % 10);
            return (ONLAR[onlar] + (birler != 0 ? " " + BIRLER[birler] : "")).trim();
        } else if (number < 1000) {
            int yuzler = (int) (number / 100);
            long kalan = number % 100;
            return (yuzler == 1 ? "yüz" : BIRLER[yuzler] + " yüz") + 
                   (kalan != 0 ? " " + convertToTurkishWords(kalan) : "");
        } else if (number < 1_000_000) {
            int binler = (int) (number / 1000);
            long kalan = number % 1000;
            return (binler == 1 ? "bin" : convertToTurkishWords(binler) + " bin") + 
                   (kalan != 0 ? " " + convertToTurkishWords(kalan) : "");
        } else if (number < 1_000_000_000) {
            int milyonlar = (int) (number / 1_000_000);
            long kalan = number % 1_000_000;
            return convertToTurkishWords(milyonlar) + " milyon" + 
                   (kalan != 0 ? " " + convertToTurkishWords(kalan) : "");
        } else if (number < 1_000_000_000_000L) {
            long milyarlar = number / 1_000_000_000;
            long kalan = number % 1_000_000_000;
            return convertToTurkishWords(milyarlar) + " milyar" + 
                   (kalan != 0 ? " " + convertToTurkishWords(kalan) : "");
        } else {
            // For very large numbers, we'll just add spaces for better readability
            return String.format("%,d", number).replace(",", " ");
        }
    }
}
