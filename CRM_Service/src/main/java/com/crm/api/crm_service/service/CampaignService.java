package com.crm.api.crm_service.service;

import com.crm.api.crm_service.dto.CampaignDto;
import com.crm.api.crm_service.dto.CampaignIUDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;
import java.util.UUID;

public interface CampaignService {
    Page<CampaignDto> getAllCampaigns(Pageable pageable);
    Optional<CampaignDto> getCampaignByUuid(UUID uuid);
    CampaignDto createCampaign(CampaignIUDto campaignIUDto);
    Optional<CampaignDto> updateCampaign(UUID uuid, CampaignIUDto campaignIUDto);
    boolean deleteCampaign(UUID uuid);
} 