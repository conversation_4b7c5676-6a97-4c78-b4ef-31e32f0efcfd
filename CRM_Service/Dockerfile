FROM maven:3.8.6-eclipse-temurin-17 AS build
WORKDIR /app

COPY pom.xml /app/pom.xml
COPY Service_Parent /app/Service_Parent
COPY CRM_Service /app/CRM_Service

RUN mvn install -f Service_Parent/pom.xml

WORKDIR /app/CRM_Service
RUN mvn clean package -DskipTests

FROM openjdk:17-jdk-slim
WORKDIR /app
COPY --from=build /app/CRM_Service/target/*.jar app.jar
ENTRYPOINT ["java", "-jar", "/app/app.jar"]
EXPOSE 6003 