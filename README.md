# CRM API

Bu proje, mü<PERSON>teri ilişkileri yönetimi (CRM) için geliştirilmiş bir Spring Boot API'sidir.

## 🔐 Güvenlik

### Kullanıcı Rolleri:
- **USER**: <PERSON><PERSON><PERSON> (varsay<PERSON>lan rol)
- **ADMIN**: Tüm CRUD işlemleri yapabilir

### 👤 <PERSON><PERSON> Kullanıcı Oluşturma:
1. Normal kayıt olun: `/api/auth/register`
2. Veritabanından `users` tablosunda kullanıcınızı bulun
3. `role_id` değerini ADMIN rolünün ID'si ile değ<PERSON>:
```sql
-- ADMIN role ID'sini bulun
SELECT id FROM roles WHERE name = 'ADMIN';

-- Kullanıcıyı ADMIN yapın
UPDATE users SET role_id = (SELECT id FROM roles WHERE name = 'ADMIN')
WHERE username = 'your_username';
```

### Ku<PERSON><PERSON><PERSON><PERSON>d<PERSON>:
```bash
curl -X POST https://crm.360avantajli.com/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "kullanici_adi",
    "password": "sifre123"
  }'
```

### Giriş Yapma:
```bash
curl -X POST https://crm.360avantajli.com/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "kullanici_adi",
    "password": "sifre123"
  }'
```

### 🔗 Webhook Kullanımı:
Webhook endpoint'i secret key ile güvenli (API Gateway üzerinden):
```bash
curl -X POST https://crm.360avantajli.com/webhook/conversation-end \
  -H "Content-Type: application/json" \
  -H "X-Webhook-Signature: webhook-secret-key-2025" \
  -d '{
    "event": "conversation_end",
    "data": {
      "call_id": "123",
      "duration": 300,
      "analysis": "Call completed successfully"
    }
  }'
```

**Webhook Konfigürasyonu:**
- **URL**: `https://crm.360avantajli.com/webhook/conversation-end`
- **Secret Key**: `webhook-secret-key-2025`
- **Header**: `X-Webhook-Signature`

### API Kullanımı:
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     https://crm.360avantajli.com/api/v1/customers
```

## Özellikler

- **Müşteri Yönetimi**: Müşteri bilgilerinin CRUD işlemleri
- **Soru Setleri**: Müşterilere sorulacak soruların yönetimi
- **Arama Kayıtları**: Müşteri aramalarının kayıt altına alınması
- **Raporlama**: Arama sonuçlarına göre raporlama

## Veritabanı Şeması

### Tablolar

1. **customers**: Müşteri bilgileri
2. **question_sets**: Soru setleri
3. **customer_to_question_sets**: Müşteri-soru seti ilişkisi
4. **call_logs**: Arama kayıtları

## Teknolojiler

- Spring Boot 3.5.3
- Spring Data JPA
- PostgreSQL
- Lombok 
- Maven

## Kurulum

### Gereksinimler

- Java 17
- Maven
- PostgreSQL

### Veritabanı Kurulumu

1. PostgreSQL'i kurun
2. `crm_db` adında bir veritabanı oluşturun
3. `application.properties` dosyasındaki veritabanı bilgilerini güncelleyin

### Uygulama Çalıştırma

```bash
# Projeyi derleyin
mvn clean install

# Uygulamayı çalıştırın
mvn spring-boot:run
```

## API Endpoints

### 🔓 Public Endpoints (JWT gerektirmez)

- `POST /api/auth/register` - Kullanıcı kaydı
- `POST /api/auth/login` - Kullanıcı girişi
- `POST /api/auth/token` - Master key ile token alma

### 👤 User Management (ADMIN only)

- `GET /api/v1/users` - Tüm kullanıcıları listele
- `GET /api/v1/users/{id}` - Kullanıcı detayı
- `PUT /api/v1/users/{id}/role` - Kullanıcı rolü güncelle

### 👥 Müşteriler (ADMIN only)

- `GET /api/v1/customers` - Tüm müşterileri listele
- `GET /api/v1/customers/{id}` - ID'ye göre müşteri getir
- `GET /api/v1/customers/email/{email}` - Email'e göre müşteri getir
- `GET /api/v1/customers/username/{username}` - Username'e göre müşteri getir
- `POST /api/v1/customers` - Yeni müşteri oluştur
- `PUT /api/v1/customers/{id}` - Müşteri bilgilerini güncelle
- `DELETE /api/v1/customers/{id}` - Müşteri sil

### ❓ Soru Setleri (ADMIN only)

- `GET /api/v1/question-sets` - Tüm soru setlerini listele
- `GET /api/v1/question-sets/{id}` - ID'ye göre soru seti getir
- `POST /api/v1/question-sets` - Yeni soru seti oluştur
- `PUT /api/v1/question-sets/{id}` - Soru setini güncelle
- `DELETE /api/v1/question-sets/{id}` - Soru seti sil

### 📞 Arama Kayıtları (ADMIN only)

- `GET /api/v1/call-logs` - Tüm arama kayıtlarını listele
- `GET /api/v1/call-logs/{id}` - ID'ye göre arama kaydı getir
- `GET /api/v1/call-logs/customer/{customerId}` - Müşteriye ait aramaları getir
- `GET /api/v1/call-logs/agent/{agent}` - Acente'ye ait aramaları getir
- `GET /api/v1/call-logs/result/{result}` - Sonuca göre aramaları getir
- `GET /api/v1/call-logs/date-range` - Tarih aralığına göre aramaları getir

### 🔧 Test Endpoints (Authenticated users)

- `GET /api/test/health` - Sistem durumu
- `GET /api/test/info` - Sistem bilgileri
- `POST /api/call-logs` - Yeni arama kaydı oluştur
- `PUT /api/call-logs/{id}` - Arama kaydını güncelle
- `DELETE /api/call-logs/{id}` - Arama kaydı sil

## Arama Sonuçları

- `OLUMLU`: Olumlu sonuç
- `OLUMSUZ`: Olumsuz sonuç
- `CEVAPSIZ`: Cevapsız arama

## Örnek Kullanım

### Müşteri Oluşturma

```json
POST /api/customers
{
    "name": "Ahmet",
    "surname": "Yılmaz",
    "username": "ahmetyilmaz",
    "email": "<EMAIL>",
    "phoneNumber": "05551234567"
}
```

### Arama Kaydı Oluşturma

```json
POST /api/call-logs
{
    "customerId": 1,
    "callDate": "2024-01-15T10:30:00",
    "result": "OLUMLU",
    "audioUrl": "https://example.com/audio/123.mp3",
    "note": "Müşteri ürün hakkında bilgi aldı",
    "agent": "Mehmet"
}
``` 