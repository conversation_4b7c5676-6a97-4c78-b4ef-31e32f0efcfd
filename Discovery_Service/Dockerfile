FROM maven:3.8.6-eclipse-temurin-17 AS build
WORKDIR /app

# Root düzeyinden her şeyi kopyala
COPY pom.xml /app/pom.xml
COPY Service_Parent /app/Service_Parent
COPY Discovery_Service /app/Discovery_Service

RUN mvn install -f Service_Parent/pom.xml

WORKDIR /app/Discovery_Service
RUN mvn clean package -DskipTests

FROM openjdk:17-jdk-slim
WORKDIR /app
COPY --from=build /app/Discovery_Service/target/*.jar app.jar
ENTRYPOINT ["java", "-jar", "/app/app.jar"]
EXPOSE 8761
