spring:
  application:
    name: discovery-service
  cloud:
    config:
      enabled: false
server:
  port: 8761

eureka:
  instance:
    hostname: discovery-service
    prefer-ip-address: true
  client:
    register-with-eureka: false
    fetch-registry: false
    serviceUrl:
      defaultZone: http://discovery-service:8761/eureka/

management:
  endpoints:
    web:
      exposure:
        include: "*"
