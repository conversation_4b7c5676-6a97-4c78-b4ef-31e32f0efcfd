spring:
  application:
    name: crm-discovery-service
  cloud:
    config:
      enabled: false
    compatibility-verifier:
      enabled: false
server:
  port: 6000

eureka:
  instance:
    hostname: crm-discovery-service
    prefer-ip-address: true
  client:
    register-with-eureka: false
    fetch-registry: false
    serviceUrl:
      defaultZone: http://crm-discovery-service:6000/eureka/

management:
  endpoints:
    web:
      exposure:
        include: "*"
