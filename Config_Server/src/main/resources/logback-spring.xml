<configuration>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Graylog appender disabled for now -->
    <!--
    <appender name="GRAYLOG" class="de.siegmar.logbackgelf.GelfUdpAppender">
        <graylogHost>graylog</graylogHost>
        <graylogPort>12201</graylogPort>
        <facility>config-service</facility>
    </appender>
    -->

    <root level="INFO">
        <appender-ref ref="CONSOLE" />
    </root>

</configuration>

