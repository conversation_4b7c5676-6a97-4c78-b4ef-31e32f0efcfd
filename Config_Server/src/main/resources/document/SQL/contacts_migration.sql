-- Contacts tablosu için migration script
-- <PERSON><PERSON> alan<PERSON> eklemek için ALTER TABLE komutları

-- CustomerToCampaign bilgileri için alanlar
ALTER TABLE "contacts" ADD COLUMN IF NOT EXISTS "customer_to_campaign_id" BIGINT;
ALTER TABLE "contacts" ADD COLUMN IF NOT EXISTS "is_called" BOOLEAN;
ALTER TABLE "contacts" ADD COLUMN IF NOT EXISTS "is_active" BOOLEAN;
ALTER TABLE "contacts" ADD COLUMN IF NOT EXISTS "contact_phone" VARCHAR(255);
ALTER TABLE "contacts" ADD COLUMN IF NOT EXISTS "contact_email" VARCHAR(255);
ALTER TABLE "contacts" ADD COLUMN IF NOT EXISTS "customer_to_campaign_created_at" TIMESTAMP;
ALTER TABLE "contacts" ADD COLUMN IF NOT EXISTS "customer_to_campaign_updated_at" TIMESTAMP;

-- Campaign bilgileri için alanlar
ALTER TABLE "contacts" ADD COLUMN IF NOT EXISTS "campaign_id" BIGINT;
ALTER TABLE "contacts" ADD COLUMN IF NOT EXISTS "campaign_name" VARCHAR(255);
ALTER TABLE "contacts" ADD COLUMN IF NOT EXISTS "campaign_title" TEXT;
ALTER TABLE "contacts" ADD COLUMN IF NOT EXISTS "campaign_description" TEXT;

-- FormToCampaign bilgileri için alanlar
ALTER TABLE "contacts" ADD COLUMN IF NOT EXISTS "form_to_campaign_id" BIGINT;
ALTER TABLE "contacts" ADD COLUMN IF NOT EXISTS "ip_address" VARCHAR(45);
ALTER TABLE "contacts" ADD COLUMN IF NOT EXISTS "form_to_campaign_created_at" TIMESTAMP;
ALTER TABLE "contacts" ADD COLUMN IF NOT EXISTS "form_to_campaign_updated_at" TIMESTAMP;

-- Index'ler ekle
CREATE INDEX IF NOT EXISTS "idx_contacts_customer_to_campaign_id" ON "contacts" ("customer_to_campaign_id");
CREATE INDEX IF NOT EXISTS "idx_contacts_campaign_id" ON "contacts" ("campaign_id");
CREATE INDEX IF NOT EXISTS "idx_contacts_form_to_campaign_id" ON "contacts" ("form_to_campaign_id");
CREATE INDEX IF NOT EXISTS "idx_contacts_is_called" ON "contacts" ("is_called");
CREATE INDEX IF NOT EXISTS "idx_contacts_is_active" ON "contacts" ("is_active");
CREATE INDEX IF NOT EXISTS "idx_contacts_ip_address" ON "contacts" ("ip_address");

-- Foreign key constraint'ler (opsiyonel)
-- ALTER TABLE "contacts" ADD CONSTRAINT "fk_contacts_customer_to_campaign" 
--     FOREIGN KEY ("customer_to_campaign_id") REFERENCES "customer_to_campaigns" ("id");
-- 
-- ALTER TABLE "contacts" ADD CONSTRAINT "fk_contacts_campaign" 
--     FOREIGN KEY ("campaign_id") REFERENCES "campaigns" ("id");
-- 
-- ALTER TABLE "contacts" ADD CONSTRAINT "fk_contacts_form_to_campaign" 
--     FOREIGN KEY ("form_to_campaign_id") REFERENCES "form_to_campaigns" ("id"); 