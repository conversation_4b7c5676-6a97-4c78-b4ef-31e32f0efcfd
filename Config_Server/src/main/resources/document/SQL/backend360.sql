CREATE TABLE "categories"
(
    "id"                 BIGINT PRIMARY KEY,
    "name"               varchar UNIQUE,
    "isActive"           bool,
    "parent_category_id" BIGINT,
    "created_at"         timestamp,
    "updated_at"         timestamp
);

CREATE TABLE "brands"
(
    "id"           BIGINT PRIMARY KEY,
    "name"         varchar,
    "country_code" int,
    "logo"         BYTEA,
    "brand_url"    varchar,
    "isActive"     bool,
    "created_at"   timestamp,
    "updated_at"   timestamp
);

CREATE TABLE "brand_to_categories"
(
    "id"          BIGINT PRIMARY KEY,
    "category_id" BIGINT,
    "brand_id"    BIGINT,
    "isActive"    bool,
    "created_at"  timestamp,
    "updated_at"  timestamp
);

CREATE TABLE "brand_to_campaigns"
(
    "id"          BIGINT PRIMARY KEY,
    "brand_id"    BIGINT,
    "campaign_id" BIGINT,
    "isActive"    bool,
    "created_at"  timestamp,
    "updated_at"  timestamp
);

CREATE TABLE "campaigns"
(
    "id"          BIGINT PRIMARY KEY,
    "name"        varchar,
    "category_id" BIGINT,
    "isActive"    bool,
    "created_at"  timestamp,
    "updated_at"  timestamp
);

CREATE TABLE "automotive_campaigns"
(
    "id"          BIGINT PRIMARY KEY,
    "campaign_id" BIGINT,
    "title"       text,
    "description" text,
    "isActive"    bool,
    "image_url"   varchar,
    "start_date"  timestamp,
    "end_date"    timestamp,
    "created_at"  timestamp,
    "updated_at"  timestamp
);

CREATE TABLE "food_campaigns"
(
    "id"            BIGINT PRIMARY KEY,
    "campaign_id"   BIGINT,
    "title"         text,
    "description"   text,
    "discount_rate" decimal,
    "isActive"      bool,
    "image_url"     varchar,
    "start_date"    timestamp,
    "end_date"      timestamp,
    "created_at"    timestamp,
    "updated_at"    timestamp
);

CREATE TABLE "customers"
(
    "id"           BIGINT PRIMARY KEY,
    "name"         varchar,
    "surname"      varchar,
    "email"        varchar,
    "phone_number" varchar,
    "country"      varchar,
    "city"         varchar,
    "town"         varchar,
    "isActive"     bool,
    "created_at"   timestamp,
    "updated_at"   timestamp
);

CREATE TABLE "customer_to_campaigns"
(
    "id"          BIGINT PRIMARY KEY,
    "customer_id" BIGINT,
    "campaign_id" BIGINT,
    "isCalled"    bool,
    "isActive"    bool,
    "created_at"  timestamp,
    "updated_at"  timestamp
);

CREATE TABLE "forms"
(
    "id"           BIGINT PRIMARY KEY,
    "name"         varchar,
    "surname"      varchar,
    "email"        varchar,
    "phone_number" varchar,
    "country"      varchar,
    "city"         varchar,
    "town"         varchar,
    "isActive"     bool,
    "created_at"   timestamp,
    "updated_at"   timestamp
);

CREATE TABLE "form_to_campaigns"
(
    "id"          BIGINT PRIMARY KEY,
    "form_id"     BIGINT,
    "campaign_id" BIGINT,
    "isActive"    bool,
    "created_at"  timestamp,
    "updated_at"  timestamp
);

CREATE TABLE "campaign_urls"
(
    "id"          BIGINT PRIMARY KEY,
    "campaign_id" BIGINT,
    "url"         text,
    "isActive"    bool
);

ALTER TABLE "campaigns"
    ADD FOREIGN KEY ("category_id") REFERENCES "categories" ("id");

ALTER TABLE "brand_to_categories"
    ADD FOREIGN KEY ("brand_id") REFERENCES "brands" ("id");

ALTER TABLE "brand_to_categories"
    ADD FOREIGN KEY ("category_id") REFERENCES "categories" ("id");

ALTER TABLE "brand_to_campaigns"
    ADD FOREIGN KEY ("brand_id") REFERENCES "brands" ("id");

ALTER TABLE "brand_to_campaigns"
    ADD FOREIGN KEY ("campaign_id") REFERENCES "campaigns" ("id");

ALTER TABLE "customer_to_campaigns"
    ADD FOREIGN KEY ("campaign_id") REFERENCES "campaigns" ("id");

ALTER TABLE "customer_to_campaigns"
    ADD FOREIGN KEY ("customer_id") REFERENCES "customers" ("id");

ALTER TABLE "form_to_campaigns"
    ADD FOREIGN KEY ("form_id") REFERENCES "forms" ("id");

ALTER TABLE "form_to_campaigns"
    ADD FOREIGN KEY ("campaign_id") REFERENCES "campaigns" ("id");

ALTER TABLE "campaign_urls"
    ADD FOREIGN KEY ("campaign_id") REFERENCES "campaigns" ("id");

ALTER TABLE "automotive_campaigns"
    ADD FOREIGN KEY ("campaign_id") REFERENCES "campaigns" ("id");

ALTER TABLE "food_campaigns"
    ADD FOREIGN KEY ("campaign_id") REFERENCES "campaigns" ("id");
