CREATE TABLE "categories"
(
    "id"                 BIGINT PRIMARY KEY,
    "name"               varchar UNIQUE,
    "campaign_detail_id" BIGINT,
    "parent_category_id" BIGINT,
    "isActive"           bool,
    "created_at"         timestamp,
    "updated_at"         timestamp
);

CREATE TABLE "brands"
(
    "id"           BIGINT PRIMARY KEY,
    "name"         varchar,
    "country_code" int,
    "brand_url"    varchar,
    "image_path"   text,
    "isActive"     bool,
    "created_at"   timestamp,
    "updated_at"   timestamp
);

CREATE TABLE "brand_to_categories"
(
    "id"          BIGINT PRIMARY KEY,
    "category_id" BIGINT,
    "brand_id"    BIGINT,
    "isActive"    bool,
    "created_at"  timestamp,
    "updated_at"  timestamp
);

CREATE TABLE "brand_to_campaigns"
(
    "id"          BIGINT PRIMARY KEY,
    "brand_id"    BIGINT,
    "campaign_id" BIGINT,
    "isActive"    bool,
    "created_at"  timestamp,
    "updated_at"  timestamp
);

CREATE TABLE "campaigns"
(
    "id"          BIGINT PRIMARY KEY,
    "category_id" BIGINT,
    "name"        varchar,
    "title"       text,
    "description" text,
    "details"     jsonb,
    "start_date"  timestamp,
    "end_date"    timestamp,
    "isActive"    bool,
    "created_at"  timestamp,
    "updated_at"  timestamp
);

CREATE TABLE "campaign_details"
(
    "id"         BIGINT PRIMARY KEY,
    "details"    jsonb,
    "isActive"   bool,
    "created_at" timestamp,
    "updated_at" timestamp
);

CREATE TABLE "customers"
(
    "id"           BIGINT PRIMARY KEY,
    "name"         varchar,
    "surname"      varchar,
    "email"        varchar,
    "phone_number" varchar,
    "country"      varchar,
    "city"         varchar,
    "town"         varchar,
    "gender"       varchar,
    "birthday"     timestamp,
    "jobId"        BIGINT,
    "remind_me"    bool,
    "isActive"     bool,
    "created_at"   timestamp,
    "updated_at"   timestamp
);

CREATE TABLE "jobs"
(
    "id"          BIGINT PRIMARY KEY,
    "description" varchar
);

CREATE TABLE "customer_to_campaigns"
(
    "id"          BIGINT PRIMARY KEY,
    "customer_id" BIGINT,
    "campaign_id" BIGINT,
    "isCalled"    bool,
    "isActive"    bool,
    "created_at"  timestamp,
    "updated_at"  timestamp
);

CREATE TABLE "forms"
(
    "id"           BIGINT PRIMARY KEY,
    "name"         varchar,
    "surname"      varchar,
    "email"        varchar,
    "phone_number" varchar,
    "country"      varchar,
    "city"         varchar,
    "town"         varchar,
    "gender"       varchar,
    "birthday"     timestamp,
    "isActive"     bool,
    "created_at"   timestamp,
    "updated_at"   timestamp
);

CREATE TABLE "form_to_campaigns"
(
    "id"          BIGINT PRIMARY KEY,
    "form_id"     BIGINT,
    "campaign_id" BIGINT,
    "ip_adress"   varchar,
    "isActive"    bool,
    "created_at"  timestamp,
    "updated_at"  timestamp
);

CREATE TABLE "campaign_urls"
(
    "id"          BIGINT PRIMARY KEY,
    "campaign_id" BIGINT,
    "url"         text,
    "isActive"    bool
);

CREATE TABLE "campaign_to_images"
(
    "id"          BIGINT PRIMARY KEY,
    "campaign_id" BIGINT,
    "image_name"  text,
    "image_path"  text,
    "isShowcase"  bool
);

ALTER TABLE "campaigns"
    ADD FOREIGN KEY ("category_id") REFERENCES "categories" ("id");

ALTER TABLE "brand_to_categories"
    ADD FOREIGN KEY ("brand_id") REFERENCES "brands" ("id");

ALTER TABLE "brand_to_categories"
    ADD FOREIGN KEY ("category_id") REFERENCES "categories" ("id");

ALTER TABLE "brand_to_campaigns"
    ADD FOREIGN KEY ("brand_id") REFERENCES "brands" ("id");

ALTER TABLE "brand_to_campaigns"
    ADD FOREIGN KEY ("campaign_id") REFERENCES "campaigns" ("id");

ALTER TABLE "customer_to_campaigns"
    ADD FOREIGN KEY ("campaign_id") REFERENCES "campaigns" ("id");

ALTER TABLE "customer_to_campaigns"
    ADD FOREIGN KEY ("customer_id") REFERENCES "customers" ("id");

ALTER TABLE "form_to_campaigns"
    ADD FOREIGN KEY ("form_id") REFERENCES "forms" ("id");

ALTER TABLE "form_to_campaigns"
    ADD FOREIGN KEY ("campaign_id") REFERENCES "campaigns" ("id");

ALTER TABLE "campaign_urls"
    ADD FOREIGN KEY ("campaign_id") REFERENCES "campaigns" ("id");

ALTER TABLE "campaign_to_images"
    ADD FOREIGN KEY ("campaign_id") REFERENCES "campaigns" ("id");

ALTER TABLE "customers"
    ADD FOREIGN KEY ("jobId") REFERENCES "jobs" ("id");

ALTER TABLE "categories"
    ADD FOREIGN KEY ("campaign_detail_id") REFERENCES "campaign_details" ("id");
