server:
  port: 8888

spring:
  application:
    name: Config_Server
  cloud:
    config:
      server:
        native:
          search-locations: classpath:/config

  profiles:
    active: native

eureka:
  client:
    serviceUrl:
      defaultZone: http://discovery-service:8761/eureka
    register-with-eureka: true
    fetch-registry: true
  instance:
    hostname: config-service
    prefer-ip-address: true

management:
  endpoints:
    web:
      exposure:
        include: "*"
