server:
  port: 6001

spring:
  application:
    name: crm-config-server
  cloud:
    config:
      server:
        native:
          search-locations: classpath:/config
    compatibility-verifier:
      enabled: false
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration

  profiles:
    active: native

eureka:
  client:
    serviceUrl:
      defaultZone: http://crm-discovery-service:6000/eureka
    register-with-eureka: true
    fetch-registry: true
  instance:
    hostname: crm-config-server
    prefer-ip-address: true
    appname: CRM-CONFIG-SERVER

management:
  endpoints:
    web:
      exposure:
        include: "*"
