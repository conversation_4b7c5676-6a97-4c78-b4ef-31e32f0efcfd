server:
  port: 6002

spring:
  application:
    name: crm-api-gateway

  cloud:
    gateway:
      routes:
        - id: crm-service-v1
          uri: lb://CRM-MAIN-SERVICE
          predicates:
            - Path=/api/v1/**
          filters:
            - StripPrefix=0
        - id: crm-service-auth
          uri: lb://CRM-MAIN-SERVICE
          predicates:
            - Path=/api/auth/**
          filters:
            - StripPrefix=0
        - id: crm-service-test
          uri: lb://CRM-MAIN-SERVICE
          predicates:
            - Path=/api/test/**
          filters:
            - StripPrefix=0
        - id: crm-service-webhook
          uri: lb://CRM-MAIN-SERVICE
          predicates:
            - Path=/webhook/**
          filters:
            - StripPrefix=0
        - id: discovery-service
          uri: lb://CRM-DISCOVERY-SERVICE
          predicates:
            - Path=/api/discovery/**
          filters:
            - StripPrefix=2
        - id: config-service
          uri: lb://CRM-CONFIG-SERVER
          predicates:
            - Path=/api/config/**
          filters:
            - StripPrefix=2
      globalcors:
        corsConfigurations:
          '[/**]':
            allowedOrigins: "*"
            allowedMethods:
              - GET
              - POST
              - PUT
              - DELETE
              - OPTIONS
            allowedHeaders: "*"
            allowCredentials: false
  main:
    web-application-type: reactive



eureka:
  client:
    serviceUrl:
      defaultZone: http://crm-discovery-service:6000/eureka
    register-with-eureka: true
    fetch-registry: true
  instance:
    hostname: crm-api-gateway
    prefer-ip-address: true
    appname: CRM-API-GATEWAY

management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always

logging:
  level:
    org.springframework.cloud.gateway: DEBUG
    org.springframework.web: INFO
    com.crm.api: DEBUG
