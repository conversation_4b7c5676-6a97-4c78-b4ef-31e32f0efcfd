server:
  port: 8084
  servlet:
    context-path: /Auth_Service
spring:
  application:
    name: auth-service

  datasource:
    url: ***********************************************************************
    username: digi_user
    password: 5nS8HDrbvqHU6B

  jpa:
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
    show-sql: true

  kafka:
    bootstrap-servers: kafka:9092
    consumer:
      group-id: auth-service-group
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer

security:
  jwt:
    secret-key: 4bae05212a6665ad4a5356db7c1ca5b80ad4bcc65644d2c6d9329d02fb7dea0b6750f29cda38f559e8000ab94f5a297e756e42e589941cbf46df6db8b8cc9bef147c5c5c7cad8870b7ee6d6ac7c08c5294ae1f59300112f4ae867a5533ba03f294f3970eea7d7e87b90a92327d02d411048b57418fe2659c75d10d8f4fa7f906d07a8e62bab2f03c6dec9a8a42e61b55f72731df70b06ff49e3d0fb869f6778029cb378f26e7a28c9a7d261841f0c95cca12b39c0f615d3f1dc7f173674d0ac5f427210af84ac1f13ce48576b977928fe7f290e14fe7a98f7c839b86568a3f72749929a8dd587a746ba9ccb2d8cf174c2177ae2c638c5d237c1eeedd567efd53e1976103214a9a8239758c9ad9179bcf56e27f955070924bf2838c964c0262db469267ea54c3e938cbe2022114a3ec7ef9b4cdf28662de82e2e25181c53cd9b397a1d7061ed4a250db53d77b6597625e0eb78b8935fbf8bcb666cc9344138562af14e1e93eb7d9f8a1d65dd4058fee3e39d5b7a9185f1af8323e1e1c3811418dca5222626288d9bfd3e82bd90ee2a581f2f6b737348a0cfd2b16f37f8a198176123b0a49146344ef484746896bdd5e68bb98fb28ac36c88f0ad2c3941d2702004df3080b7fb4482ed84324ce440 # En az 256 bitlik bir Base64 string (örnek)
    access-token-expiration: 900000      # 15 dakika (milisaniye cinsinden)
    refresh-token-expiration: 43200000   # 12 saat (milisaniye cinsinden)

logging:
  level:
    org:
      springframework:
        security: DEBUG

eureka:
  client:
    serviceUrl:
      defaultZone: http://discovery-service:8761/eureka

# CRM Integration Configuration
crm:
  api:
    url: http://crm-api-gateway:6002
