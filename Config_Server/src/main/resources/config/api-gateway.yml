server:
  port: 8070

spring:
  application:
    name: api-gateway

  cloud:
    gateway:
      global-cors:
        cors-configurations:
          '[/**]':
            allowedOrigins: 
              - http://localhost:5173
              - https://360avantajli.com
            allowedMethods: "*"
            allowedHeaders: "*"
            exposedHeaders:
              - Authorization
            allowCredentials: true
            maxAge: 3600
        add-to-simple-url-handler-mapping: true
      routes:
        - id: Campaign_Service
          uri: http://campaign-service:9001
          predicates:
            - Path=/Campaign_Service/**
            - Method=POST,PUT,GET,DELETE
          filters:
            - DedupeResponseHeader=Access-Control-Allow-Origin
            - DedupeResponseHeader=Access-Control-Allow-Credentials
        - id: Image_Service
          uri: http://image-service:9002
          predicates:
            - Path=/Image_Service/**
            - Method=POST,PUT,GET,DELETE
          filters:
            - DedupeResponseHeader=Access-Control-Allow-Origin
            - DedupeResponseHeader=Access-Control-Allow-Credentials
            - RemoveRequestHeader=Authorization

        - id: auth-service
          uri: http://auth-service:8084
          predicates:
            - Path=/Auth_Service/**
            - Method=POST,PUT,GET,DELETE
          filters:
            - DedupeResponseHeader=Access-Control-Allow-Origin RETAIN_FIRST
            - DedupeResponseHeader=Access-Control-Allow-Credentials RETAIN_FIRST

        - id: ai-call-center-service
          uri: http://ai-call-center-service:8080
          predicates:
            - Path=/AI_Call_Center_Service/**
            - Method=POST,PUT,GET,DELETE
          filters:
            - DedupeResponseHeader=Access-Control-Allow-Origin RETAIN_FIRST
            - DedupeResponseHeader=Access-Control-Allow-Credentials RETAIN_FIRST
  main:
    web-application-type: reactive

  kafka:
    bootstrap-servers: kafka:9092
    consumer:
      group-id: api-gateway-group
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer

eureka:
  client:
    serviceUrl:
      defaultZone: http://discovery-service:8761/eureka

management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always

logging:
  level:
    root: INFO
    org.springframework.web: DEBUG
    com.backend360: DEBUG
