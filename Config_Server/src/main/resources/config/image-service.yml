server:
  port: 9002
  servlet:
    context-path: /Image_Service
    multipart:
      max-file-size: 3MB
      max-request-size: 3MB

file:
  upload-dir: /root/docker/images/

spring:
  datasource:
    url: jdbc:postgresql://************:1923/360avantajli?currentSchema=campaign
    username: digi_user
    password: 5nS8HDrbvqHU6B

  jpa:
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
    show-sql: true

  kafka:
    bootstrap-servers: kafka:9092
    consumer:
      group-id: image-service-group
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer

eureka:
  client:
    serviceUrl:
      defaultZone: http://discovery-service:8761/eureka

management:
  endpoints:
    web:
      exposure:
        include: "*"
