server:
  port: 7003

spring:
  application:
    name: ai-call-center-service

  datasource:
    url: ***************************************
    username: postgres
    password: postgres
    driver-class-name: org.postgresql.Driver

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true

  main:
    web-application-type: servlet

eureka:
  client:
    serviceUrl:
      defaultZone: http://crm-discovery-service:6000/eureka
    register-with-eureka: true
    fetch-registry: true
  instance:
    hostname: ai-call-center-service
    prefer-ip-address: true
    appname: AI-CALL-CENTER-SERVICE

management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always

logging:
  level:
    com.crm.api.ai_call_center: DEBUG
    org.springframework.web: INFO
    org.springframework.security: DEBUG

# Net GSM SIP Configuration
netgsm:
  api:
    url: https://api.netgsm.com.tr
    username: ${NETGSM_USERNAME:**********}
    password: ${NETGSM_PASSWORD:HJHjgremm8s9}

  # SIP Trunk Configuration
  sip:
    gateway: sip.netgsm.com.tr
    phone_number: **********
    auth_user: **********
    auth_pass: HJHjgremm8s9

# AI Configuration
ai:
  # AI Provider (openai, gemini, claude)
  provider: ${AI_PROVIDER:gemini}

  # OpenAI Configuration
  openai:
    api:
      key: ${OPENAI_API_KEY:your_openai_api_key}

  # Google Gemini Configuration
  gemini:
    api:
      key: ${GEMINI_API_KEY:AIzaSyAfLW_fKFKs9xHchmbj7e22SqbFJpVJt-A}

  # Claude Configuration
  claude:
    api:
      key: ${CLAUDE_API_KEY:your_claude_api_key}

  # Text-to-Speech Configuration
  tts:
    provider: ${TTS_PROVIDER:google}
    google:
      api:
        key: ${GOOGLE_TTS_API_KEY:AIzaSyAfLW_fKFKs9xHchmbj7e22SqbFJpVJt-A}
      voice:
        name: "tr-TR-Wavenet-A"  # En kaliteli Türkçe kadın sesi
        language_code: "tr-TR"
        ssml_gender: "FEMALE"
        speaking_rate: 1.5  # Konuşma hızı (1.5x hızlı)
        pitch: 0.0  # Normal ses tonu
      audio:
        encoding: "MP3"
        sample_rate_hertz: 24000  # Yüksek kalite
    elevenlabs:
      api:
        key: ${ELEVENLABS_API_KEY:your_elevenlabs_api_key}
    azure:
      api:
        key: ${AZURE_TTS_API_KEY:your_azure_tts_api_key}

  # Speech-to-Text Configuration
  stt:
    provider: ${STT_PROVIDER:google}
    google:
      api:
        key: ${GOOGLE_STT_API_KEY:AIzaSyAfLW_fKFKs9xHchmbj7e22SqbFJpVJt-A}
    azure:
      api:
        key: ${AZURE_STT_API_KEY:your_azure_stt_api_key}
    openai:
      api:
        key: ${OPENAI_STT_API_KEY:your_openai_stt_api_key}
    
# Security Configuration
security:
  jwt:
    secret: ${JWT_SECRET:mySecretKey}
    expiration: 86400000
