package com.backend360.campaign_service.client;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Component
@RequiredArgsConstructor
@Slf4j
public class AiCallCenterClient {

    private final RestTemplate restTemplate = new RestTemplate();
    
    @Value("${ai.call.center.url:http://ai-call-center-service:8080}")
    private String aiCallCenterUrl;

    /**
     * AI Call Center'a campaign gönder
     */
    public void sendCampaignToAiCallCenter(Map<String, Object> campaignData) {
        log.info("AI Call Center'a campaign gönderiliyor: {}", campaignData.get("name"));
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(campaignData, headers);
        
        try {
            ResponseEntity<String> response = restTemplate.postForEntity(
                aiCallCenterUrl + "/api/v1/campaigns",
                entity,
                String.class
            );
            log.info("AI Call Center'a campaign başarıyla gönderildi! Status: {}", response.getStatusCode());
            log.info("Response: {}", response.getBody());
        } catch (Exception e) {
            log.error("AI Call Center'a campaign gönderirken hata oluştu", e);
        }
    }

    /**
     * AI Call Center'a contact gönder
     */
    public void sendContactToAiCallCenter(Map<String, Object> contactData) {
        log.info("AI Call Center'a contact gönderiliyor: {}", contactData.get("email"));
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(contactData, headers);
        
        try {
            ResponseEntity<String> response = restTemplate.postForEntity(
                aiCallCenterUrl + "/api/v1/contacts",
                entity,
                String.class
            );
            log.info("AI Call Center'a contact başarıyla gönderildi! Status: {}", response.getStatusCode());
            log.info("Response: {}", response.getBody());
        } catch (Exception e) {
            log.error("AI Call Center'a contact gönderirken hata oluştu", e);
        }
    }

    /**
     * AI Call Center health check
     */
    public boolean isAiCallCenterHealthy() {
        try {
            ResponseEntity<String> response = restTemplate.getForEntity(
                aiCallCenterUrl + "/api/v1/health",
                String.class
            );
            return response.getStatusCode().is2xxSuccessful();
        } catch (Exception e) {
            log.error("AI Call Center health check failed", e);
            return false;
        }
    }
}
