package com.backend360.campaign_service.kafka;

import com.backend360.campaign_service.dto.CampaignDto;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
public class KafkaProducer {

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    private static final String CAMPAIGN_CREATED_TOPIC = "campaign-created";

    public void sendCampaignCreatedEvent(CampaignDto campaignDto) {
        try {
            String campaignJson = objectMapper.writeValueAsString(campaignDto);
            
            log.info("Kafka'ya kampanya oluşturma eventi gönderiliyor. Kampanya: {}, Topic: {}", 
                    campaignDto.getName(), CAMPAIGN_CREATED_TOPIC);
            
            CompletableFuture<SendResult<String, String>> future = kafkaTemplate.send(CAMPAIGN_CREATED_TOPIC, campaignJson);
            
            future.whenComplete((result, ex) -> {
                if (ex == null) {
                    log.info("Kafka event başarıyla gönderildi. Topic: {}, Partition: {}, Offset: {}", 
                            CAMPAIGN_CREATED_TOPIC, result.getRecordMetadata().partition(), result.getRecordMetadata().offset());
                } else {
                    log.error("Kafka event gönderilirken hata oluştu. Topic: {}, Hata: {}", 
                            CAMPAIGN_CREATED_TOPIC, ex.getMessage(), ex);
                }
            });
            
        } catch (JsonProcessingException e) {
            log.error("CampaignDto JSON'a çevrilirken hata oluştu: {}", e.getMessage(), e);
        }
    }
} 