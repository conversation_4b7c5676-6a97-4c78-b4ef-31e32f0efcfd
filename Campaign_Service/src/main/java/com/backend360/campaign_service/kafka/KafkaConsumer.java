package com.backend360.campaign_service.kafka;

import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class KafkaConsumer {

    @KafkaListener(topics = "campaign-events", groupId = "campaign-service-group")
    public void listenCampaignEvents(String message) {
        log.info("Received campaign event: {}", message);
        // Campaign event işleme mantığı burada
    }

    @KafkaListener(topics = "user-events", groupId = "campaign-service-group")
    public void listenUserEvents(String message) {
        log.info("Received user event: {}", message);
        // User event işleme mantığı burada
    }

    @KafkaListener(topics = "notification-events", groupId = "campaign-service-group")
    public void listenNotificationEvents(String message) {
        log.info("Received notification event: {}", message);
        // Notification event işleme mantığı burada
    }
} 