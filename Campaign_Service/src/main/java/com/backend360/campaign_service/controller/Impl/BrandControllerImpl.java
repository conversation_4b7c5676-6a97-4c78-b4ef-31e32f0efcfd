package com.backend360.campaign_service.controller.Impl;

import com.backend360.campaign_service.controller.IBrandController;
import com.backend360.campaign_service.dto.BrandDto;
import com.backend360.campaign_service.dto.BrandIUDto;
import com.backend360.campaign_service.manager.ImageFeignManager;
import com.backend360.campaign_service.service.IBrandService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/brand")
public class BrandControllerImpl implements IBrandController {

    @Autowired
    private IBrandService brandService;

    @Autowired
    private ImageFeignManager imageFeignManager;

    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<BrandDto> createBrand(@ModelAttribute BrandIUDto brandIUDto, @RequestParam("image") MultipartFile image) throws IOException {
        BrandDto createdBrand = brandService.save(brandIUDto, image);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdBrand);
    }

    @Override
    @GetMapping
    public ResponseEntity<List<BrandDto>> getAllBrands() {
        List<BrandDto> brands = brandService.findAll();
        if (brands.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.ok(brands);
    }

    @Override
    @GetMapping("/{id}")
    public ResponseEntity<BrandDto> getBrandById(@PathVariable Long id) {
        BrandDto brand = brandService.findById(id);
        return ResponseEntity.ok(brand);
    }

    @PutMapping(value = "/{id}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<BrandDto> updateBrand(@PathVariable Long id, @ModelAttribute BrandIUDto brandIUDto, @RequestParam("image") MultipartFile image) throws IOException {
        BrandDto updatedBrand = brandService.update(id, brandIUDto, image);
        return ResponseEntity.ok(updatedBrand);
    }

    @Override
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteBrand(@PathVariable Long id) {
        brandService.delete(id);
        return ResponseEntity.noContent().build();
    }
    @GetMapping("/{id}/image")
    public ResponseEntity<byte[]> getBrandImage(@PathVariable Long id) {
        BrandDto brandDto = brandService.findById(id);

        if (brandDto.getImagePath() == null || brandDto.getImagePath().isEmpty()) {
            return ResponseEntity.notFound().build();
        }

        String fileName = brandDto.getImagePath()
                .substring(brandDto.getImagePath().lastIndexOf("/") + 1);

        ResponseEntity<byte[]> response = imageFeignManager.downloadImage(fileName);

        if (!response.getStatusCode().is2xxSuccessful()) {
            return ResponseEntity.status(response.getStatusCode()).build();
        }

        return ResponseEntity
                .status(HttpStatus.OK)
                .contentType(response.getHeaders().getContentType())
                .body(response.getBody());
    }

}
