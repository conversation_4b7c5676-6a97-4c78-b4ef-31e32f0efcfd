package com.backend360.campaign_service.controller.Impl;

import com.backend360.campaign_service.controller.ICampaignToImageController;
import com.backend360.campaign_service.dto.CampaignToImageDto;
import com.backend360.campaign_service.dto.CampaignToImageIUDto;
import com.backend360.campaign_service.manager.ImageFeignManager;
import com.backend360.campaign_service.service.ICampaignToImageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/campaign-image")
public class CampaignToImageControllerImpl implements ICampaignToImageController {

    @Autowired
    private ICampaignToImageService campaignToImageService;

    @Autowired
    private ImageFeignManager imageFeignManager;

    @Value("${image.service.base-url}")
    private String imageBaseUrl;

    @Override
    @PostMapping(value = "/showcase", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<CampaignToImageDto> uploadCampaignImage(@RequestParam("campaignId") Long campaignId,
                                                                  @RequestPart("image") MultipartFile imageFile) throws IOException {
        // Showcase olarak yollanacak
        CampaignToImageDto dto = campaignToImageService.uploadImage(campaignId, true, imageFile);
        return ResponseEntity.status(HttpStatus.CREATED).body(dto);
    }

    @PostMapping(value = "/details", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<List<CampaignToImageDto>> uploadMultipleDetailImages(@RequestParam("campaignId") Long campaignId,
                                                                               @RequestPart("images") MultipartFile[] imageFiles) throws IOException {
        List<CampaignToImageDto> result = new ArrayList<>();

        for (MultipartFile file : imageFiles) {
            CampaignToImageDto dto = campaignToImageService.uploadImage(campaignId, false, file); // hep detay
            result.add(dto);
        }

        return ResponseEntity.status(HttpStatus.CREATED).body(result);
    }

    @Override
    @GetMapping
    public ResponseEntity<List<CampaignToImageDto>> getAllCampaignToImages() {
        List<CampaignToImageDto> campaignToImages = campaignToImageService.findAll();

        if (campaignToImages.isEmpty()) {
            return ResponseEntity.noContent().build();
        }

        return ResponseEntity.ok(campaignToImages);
    }

    @Override
    @GetMapping("/{id}")
    public ResponseEntity<CampaignToImageDto> getCampaignToImageById(@PathVariable Long id) {
        CampaignToImageDto campaignToImage = campaignToImageService.findById(id);
        return ResponseEntity.ok(campaignToImage);
    }

    @Override
    @PutMapping("/{id}")
    public ResponseEntity<CampaignToImageDto> updateCampaignToImage(@PathVariable Long id,
                                                                    @RequestBody CampaignToImageIUDto campaignToImageIUDto) {
        CampaignToImageDto campaignToImage = campaignToImageService.update(id, campaignToImageIUDto);
        return ResponseEntity.ok(campaignToImage);
    }

    @Override
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteCampaignToImage(@PathVariable Long id) {
        campaignToImageService.delete(id);
        return ResponseEntity.noContent().build();
    }

    @Override
    @GetMapping("/showcase/{campaignId}")
    public ResponseEntity<byte[]> getShowcaseImageByCampaignId(@PathVariable Long campaignId) {
        Optional<CampaignToImageDto> dtoOpt = campaignToImageService.getShowcaseImageByCampaignId(campaignId);

        if (dtoOpt.isEmpty() || dtoOpt.get().getImagePath() == null) {
            return ResponseEntity.notFound().build();
        }

        String fileName = dtoOpt.get().getImagePath()
                .substring(dtoOpt.get().getImagePath().lastIndexOf("/") + 1);

        ResponseEntity<byte[]> response = imageFeignManager.downloadImage(fileName);

        if (!response.getStatusCode().is2xxSuccessful()) {
            return ResponseEntity.status(response.getStatusCode()).build();
        }

        return ResponseEntity
                .status(HttpStatus.OK)
                .contentType(response.getHeaders().getContentType())
                .body(response.getBody());
    }

    @Override
    @GetMapping("/details/{campaignId}")
    public ResponseEntity<String> getDetailImagesByCampaignId(@PathVariable Long campaignId) {
        List<CampaignToImageDto> dtos = campaignToImageService.getDetailImagesByCampaignId(campaignId);

        if (dtos.isEmpty()) {
            return ResponseEntity.noContent().build();
        }

        StringBuilder html = new StringBuilder("<html><body>");

        for (CampaignToImageDto dto : dtos) {
            html.append("<img src=\"")
                    .append(imageBaseUrl)
                    .append(dto.getImageName())
                    .append("\" style=\"max-width:400px;margin:10px;\"/>");
        }

        html.append("</body></html>");
        return ResponseEntity.ok().contentType(MediaType.TEXT_HTML).body(html.toString());
    }

    @Override
    @GetMapping("/images/{campaignId}")
    public ResponseEntity<String> getAllImagesByCampaignId(@PathVariable Long campaignId) {
        List<CampaignToImageDto> dtos = campaignToImageService.getAllImagesByCampaignId(campaignId);

        if (dtos.isEmpty()) {
            return ResponseEntity.noContent().build();
        }

        StringBuilder html = new StringBuilder("<html><body>");

        for (CampaignToImageDto dto : dtos) {
            html.append("<img src=\"")
                    .append(imageBaseUrl)
                    .append(dto.getImageName())
                    .append("\" style=\"max-width:400px;margin:10px;\"/>");
        }

        html.append("</body></html>");
        return ResponseEntity.ok().contentType(MediaType.TEXT_HTML).body(html.toString());
    }


    @Override
    @PutMapping(value = "/details/{campaignId}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<CampaignToImageDto> updateImageFile(@PathVariable Long id,
                                                              @RequestPart("image") MultipartFile imageFile) throws IOException {
        CampaignToImageDto updated = campaignToImageService.updateImageFile(id, imageFile);
        return ResponseEntity.ok(updated);
    }

    @Override
    @PutMapping(value = "/showcase/{campaignId}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<CampaignToImageDto> updateShowcaseImageByCampaignId(@PathVariable Long campaignId,
                                                                              @RequestPart("image") MultipartFile imageFile) throws IOException {
        CampaignToImageDto updated = campaignToImageService.updateShowcaseImageByCampaignId(campaignId, imageFile);
        return ResponseEntity.ok(updated);
    }

}
