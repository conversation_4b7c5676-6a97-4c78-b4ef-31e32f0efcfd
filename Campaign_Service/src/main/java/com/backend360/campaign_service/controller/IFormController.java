package com.backend360.campaign_service.controller;

import com.backend360.campaign_service.dto.FormDto;
import com.backend360.campaign_service.dto.FormIUDto;
import org.springframework.http.ResponseEntity;

import java.util.List;

public interface IFormController {
    ResponseEntity<FormDto> createForm(FormIUDto formIUDto);

    ResponseEntity<List<FormDto>> getAllForms();

    ResponseEntity<FormDto> getFormById(Long id);

    ResponseEntity<FormDto> updateForm(Long id, FormIUDto formIUDto);

    ResponseEntity<Void> deleteForm(Long id);
}
