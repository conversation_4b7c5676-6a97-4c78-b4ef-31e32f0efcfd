package com.backend360.campaign_service.controller;

import com.backend360.campaign_service.crm_client.CrmClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Campaign Service CRM Admin Controller
 * Frontend'ten gelen CRM Admin isteklerini CRM-API'ye forward eder
 */
@Slf4j
@RestController
@RequestMapping("/crm-admin")
@CrossOrigin(origins = "*")
public class CrmAdminController {

    @Autowired
    private CrmClient crmClient;

    /**
     * Frontend'ten gelen Authorization header'ını alır ve CRM-API'ye forward eder
     * JWT token kontrolü JwtAuthenticationFilter tarafından yapılır
     */
    private String getAuthHeader() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String authHeader = request.getHeader("Authorization");

                if (authHeader != null && authHeader.startsWith("Bearer ")) {
                    log.debug("JWT token found, forwarding to CRM-API: {}...", authHeader.substring(0, 20));
                    return authHeader;
                } else {
                    log.warn("No Authorization header found in request");
                }
            } else {
                log.warn("No request attributes found");
            }
        } catch (Exception e) {
            log.error("Error getting JWT token from request: {}", e.getMessage());
        }

        log.error("No valid JWT token found in request. User must be authenticated.");
        throw new RuntimeException("Authentication required. Please login first.");
    }

    @GetMapping("/contacts")
    public ResponseEntity<Map<String, Object>> getAllContacts() {
        try {
            log.info("Campaign Service - CRM Admin - Forwarding contacts request to CRM-API");
            return crmClient.getAllContacts(getAuthHeader(), 1000);
        } catch (Exception e) {
            log.error("Error forwarding contacts request: {}", e.getMessage());
            throw new RuntimeException("Failed to get contacts from CRM-API", e);
        }
    }

    @GetMapping("/contacts/{id}")
    public ResponseEntity<?> getContactById(@PathVariable UUID id) {
        try {
            log.info("Campaign Service - CRM Admin - Forwarding contact detail request to CRM-API: {}", id);
            return crmClient.getContactById(id, getAuthHeader());
        } catch (Exception e) {
            log.error("Error forwarding contact detail request: {}", e.getMessage());
            throw new RuntimeException("Failed to get contact from CRM-API", e);
        }
    }

    @GetMapping("/webhook-events")
    public ResponseEntity<List<Map<String, Object>>> getAllWebhookEvents() {
        try {
            log.info("Campaign Service - CRM Admin - Forwarding webhook events request to CRM-API");
            return crmClient.getAllWebhookEvents(getAuthHeader());
        } catch (Exception e) {
            log.error("Error forwarding webhook events request: {}", e.getMessage());
            throw new RuntimeException("Failed to get webhook events from CRM-API", e);
        }
    }

    @GetMapping("/webhook-events/{id}")
    public ResponseEntity<Map<String, Object>> getWebhookEventById(@PathVariable UUID id) {
        try {
            log.info("Campaign Service - CRM Admin - Forwarding webhook event detail request to CRM-API: {}", id);
            return crmClient.getWebhookEventById(id, getAuthHeader());
        } catch (Exception e) {
            log.error("Error forwarding webhook event detail request: {}", e.getMessage());
            throw new RuntimeException("Failed to get webhook event from CRM-API", e);
        }
    }

    @GetMapping("/webhook-events/by-phone/{phoneNumber}")
    public ResponseEntity<List<Map<String, Object>>> getWebhookEventsByPhoneNumber(@PathVariable String phoneNumber) {
        try {
            log.info("Campaign Service - CRM Admin - Forwarding webhook events by phone request to CRM-API: {}", phoneNumber);
            return crmClient.getWebhookEventsByPhoneNumber(phoneNumber, getAuthHeader());
        } catch (Exception e) {
            log.error("Error forwarding webhook events by phone request: {}", e.getMessage());
            throw new RuntimeException("Failed to get webhook events by phone from CRM-API", e);
        }
    }

    @GetMapping("/test-connection")
    public ResponseEntity<Map<String, Object>> testCrmConnection() {
        try {
            log.info("Campaign Service - CRM Admin - Testing CRM connection");
            ResponseEntity<Map<String, Object>> response = crmClient.getAllContacts(getAuthHeader(), 1000);
            
            int contactCount = 0;
            if (response.getBody() != null) {
                Map<String, Object> body = (Map<String, Object>) response.getBody();
                if (body != null) {
                    contactCount = (Integer) body.getOrDefault("totalElements", 0);
                }
            }

            Map<String, Object> result = Map.of(
                "success", true,
                "message", "CRM connection successful",
                "statusCode", response.getStatusCode().value(),
                "contactCount", contactCount
            );
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("CRM connection test failed: {}", e.getMessage());
            Map<String, Object> result = Map.of(
                "success", false,
                "message", "CRM connection failed",
                "error", e.getMessage()
            );
            return ResponseEntity.ok(result);
        }
    }
}
