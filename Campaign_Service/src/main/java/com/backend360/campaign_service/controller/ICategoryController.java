package com.backend360.campaign_service.controller;

import com.backend360.campaign_service.dto.CategoryDto;
import com.backend360.campaign_service.dto.CategoryIUDto;
import org.springframework.http.ResponseEntity;

import java.util.List;

public interface ICategoryController {
    ResponseEntity<CategoryDto> createCategory(CategoryIUDto categoryIUDto);

    ResponseEntity<List<CategoryDto>> getAllCategories();

    ResponseEntity<CategoryDto> getCategoryById(Long id);

    ResponseEntity<CategoryDto> updateCategory(Long id, CategoryIUDto categoryIUDto);

    ResponseEntity<Void> deleteCategory(Long id);
}
