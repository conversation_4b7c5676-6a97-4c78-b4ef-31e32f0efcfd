package com.backend360.campaign_service.controller;

import com.backend360.campaign_service.dto.BrandDto;
import com.backend360.campaign_service.dto.BrandIUDto;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface IBrandController {

    ResponseEntity<BrandDto> createBrand(BrandIUDto brandIUDto, MultipartFile image) throws IOException;

    ResponseEntity<List<BrandDto>> getAllBrands();

    ResponseEntity<BrandDto> getBrandById(Long id);

    ResponseEntity<BrandDto> updateBrand(Long id, BrandIUDto brandIUDto, MultipartFile image) throws IOException;

    ResponseEntity<Void> deleteBrand(Long id);
}