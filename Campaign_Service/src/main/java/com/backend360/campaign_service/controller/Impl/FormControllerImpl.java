package com.backend360.campaign_service.controller.Impl;

import com.backend360.campaign_service.controller.IFormController;
import com.backend360.campaign_service.dto.FormDto;
import com.backend360.campaign_service.dto.FormIUDto;
import com.backend360.campaign_service.service.IFormService;
import com.backend360.campaign_service.service.Impl.EmailServiceImpl;
import com.backend360.campaign_service.service.IFormToCampaignService;
import com.backend360.campaign_service.service.ICampaignService;
import com.backend360.campaign_service.dto.CampaignDto;
import com.backend360.campaign_service.dto.FormToCampaignDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/form")
public class FormControllerImpl implements IFormController {
    
    @Autowired
    private IFormService formService;

    @Autowired
    private EmailServiceImpl emailService;

    @Autowired
    private IFormToCampaignService formToCampaignService;

    @Autowired
    private ICampaignService campaignService;

    @Override
    @PostMapping
    public ResponseEntity<FormDto> createForm(@RequestBody FormIUDto formIUDto) {
        try {
            FormDto response = formService.save(formIUDto);
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (Exception e) {
            log.error("Form oluşturulurken hata oluştu: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Override
    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('CRM_ADMIN')")
    public ResponseEntity<List<FormDto>> getAllForms() {
        try {
            List<FormDto> forms = formService.findAll();
            
            if (forms.isEmpty()) {
                return ResponseEntity.noContent().build();
            }
            
            return ResponseEntity.ok(forms);
        } catch (Exception e) {
            log.error("Formlar getirilirken hata oluştu: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Override
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('CRM_ADMIN')")
    public ResponseEntity<FormDto> getFormById(@PathVariable Long id) {
        try {
            FormDto form = formService.findById(id);
            return ResponseEntity.ok(form);
        } catch (Exception e) {
            log.error("Form getirilirken hata oluştu (ID: {}): {}", id, e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }

    @Override
    @PutMapping("/{id}")
    public ResponseEntity<FormDto> updateForm(@PathVariable Long id, @RequestBody FormIUDto formIUDto) {
        try {
            FormDto form = formService.update(id, formIUDto);
            return ResponseEntity.ok(form);
        } catch (Exception e) {
            log.error("Form güncellenirken hata oluştu (ID: {}): {}", id, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Override
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteForm(@PathVariable Long id) {
        try {
            formService.delete(id);
            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            log.error("Form silinirken hata oluştu (ID: {}): {}", id, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/debug-auth")
    public ResponseEntity<?> debugAuth(HttpServletRequest request, Authentication authentication) {
        Map<String, Object> debug = new HashMap<>();
        debug.put("authenticated", authentication != null);
        debug.put("principal", authentication != null ? authentication.getName() : null);
        debug.put("authorities", authentication != null ? authentication.getAuthorities() : null);
        debug.put("cookies", request.getCookies() != null ?
            Arrays.stream(request.getCookies())
                .collect(Collectors.toMap(Cookie::getName, c -> c.getValue().substring(0, Math.min(20, c.getValue().length())) + "..."))
            : null);
        debug.put("authHeader", request.getHeader("Authorization"));
        log.info("Debug auth endpoint called: {}", debug);
        return ResponseEntity.ok(debug);
    }
}
