package com.backend360.campaign_service.controller;

import com.backend360.campaign_service.dto.CustomerToCampaignDto;
import com.backend360.campaign_service.dto.CustomerToCampaignIUDto;
import org.springframework.http.ResponseEntity;

import java.util.List;

public interface ICustomerToCampaignController {
    ResponseEntity<CustomerToCampaignDto> createCustomerToCampaign(CustomerToCampaignIUDto customerToCampaignIUDto);

    ResponseEntity<List<CustomerToCampaignDto>> getAllCustomerToCampaigns();

    ResponseEntity<CustomerToCampaignDto> getCustomerToCampaignById(Long id);

    ResponseEntity<CustomerToCampaignDto> updateCustomerToCampaign(Long id, CustomerToCampaignIUDto customerToCampaignIUDto);

    ResponseEntity<Void> changeIsCalledStatus(Long id, Boolean status);

    ResponseEntity<Void> deleteCustomerToCampaign(Long id);
}
