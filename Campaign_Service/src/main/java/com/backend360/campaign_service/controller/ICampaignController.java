package com.backend360.campaign_service.controller;

import com.backend360.campaign_service.dto.CampaignDto;
import com.backend360.campaign_service.dto.CampaignIUDto;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface ICampaignController {
    ResponseEntity<CampaignDto> createCampaign(CampaignIUDto campaignIUDto);

    ResponseEntity<List<CampaignDto>> getAllCampaigns();

    ResponseEntity<CampaignDto> getCampaignById(Long id);

    ResponseEntity<CampaignDto> updateCampaign(Long id, CampaignIUDto campaignIUDto);

    ResponseEntity<Void> deleteCampaign(Long id);


}
