package com.backend360.campaign_service.controller;

import com.backend360.campaign_service.service.CrmTokenManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/v1/crm-token")
@CrossOrigin(origins = "*")
@Slf4j
public class CrmTokenController {

    @Autowired
    private CrmTokenManager crmTokenManager;

    /**
     * CRM token durumunu kontrol eder
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getTokenStatus() {
        try {
            Map<String, Object> tokenInfo = crmTokenManager.getTokenInfo();
            log.info("CRM Token Status: {}", tokenInfo);
            return ResponseEntity.ok(tokenInfo);
        } catch (Exception e) {
            log.error("Token status kontrolü başarısız: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * CRM token'ını manuel olarak yeniler
     */
    @PostMapping("/refresh")
    public ResponseEntity<Map<String, Object>> refreshToken() {
        try {
            log.info("Manuel token yenileme başlatıldı");
            
            // Cache'i temizle
            crmTokenManager.clearTokenCache();
            
            // Yeni token al
            String newToken = crmTokenManager.getValidToken();
            
            Map<String, Object> response = Map.of(
                "message", "Token başarıyla yenilendi",
                "tokenPreview", newToken.substring(0, 20) + "...",
                "tokenInfo", crmTokenManager.getTokenInfo()
            );
            
            log.info("Manuel token yenileme başarılı");
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Manuel token yenileme başarısız: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of("error", e.getMessage()));
        }
    }
}
