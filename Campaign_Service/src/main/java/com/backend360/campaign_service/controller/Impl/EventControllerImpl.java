package com.backend360.campaign_service.controller.Impl;

import com.backend360.campaign_service.controller.IEventController;
import com.backend360.campaign_service.dto.EventDto;
import com.backend360.campaign_service.dto.EventIUDto;
import com.backend360.campaign_service.dto.PopularItemDto;
import com.backend360.campaign_service.service.IEventService;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/events")
@RequiredArgsConstructor
public class EventControllerImpl implements IEventController {
    private final IEventService eventService;

    @Override
    @PostMapping
    public ResponseEntity<EventDto> createEvent(@RequestBody EventIUDto eventIUDto) {
        return ResponseEntity.ok(eventService.createEvent(eventIUDto));
    }

    @Override
    @GetMapping("/popular/campaigns")
    public ResponseEntity<List<PopularItemDto>> getPopularCampaigns() {
        return ResponseEntity.ok(eventService.getPopularCampaigns());
    }

    @Override
    @GetMapping("/popular/brands")
    public ResponseEntity<List<PopularItemDto>> getPopularBrands() {
        return ResponseEntity.ok(eventService.getPopularBrands());
    }

    @GetMapping("/user/{userId}/popular/campaigns")
    public ResponseEntity<List<PopularItemDto>> getUserPopularCampaigns(@PathVariable String userId) {
        return ResponseEntity.ok(eventService.getUserPopularCampaigns(userId));
    }

    @GetMapping("/user/{userId}/popular/brands")
    public ResponseEntity<List<PopularItemDto>> getUserPopularBrands(@PathVariable String userId) {
        return ResponseEntity.ok(eventService.getUserPopularBrands(userId));
    }
} 