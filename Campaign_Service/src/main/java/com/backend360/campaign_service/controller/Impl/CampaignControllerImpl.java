package com.backend360.campaign_service.controller.Impl;

import com.backend360.campaign_service.controller.ICampaignController;
import com.backend360.campaign_service.dto.CampaignDto;
import com.backend360.campaign_service.dto.CampaignIUDto;
import com.backend360.campaign_service.dto.CampaignDetailIUDto;
import com.backend360.campaign_service.service.ICampaignService;
import com.backend360.campaign_service.service.ICampaignDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
// Security dependency eklendikten sonra CRM_ADMIN kontrolü eklenecek
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/campaign")
public class CampaignControllerImpl implements ICampaignController {
    @Autowired
    private ICampaignService campaignService;
    
    @Autowired
    private ICampaignDetailService campaignDetailService;

    @Override
    @PostMapping
    public ResponseEntity<CampaignDto> createCampaign(@RequestBody CampaignIUDto campaignIUDto) {
        CampaignDto response = campaignService.save(campaignIUDto);

        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Override
    @GetMapping
    public ResponseEntity<List<CampaignDto>> getAllCampaigns() {
        List<CampaignDto> campaigns = campaignService.findAll();

        if (campaigns.isEmpty()) {
            return ResponseEntity.noContent().build();
        }

        return ResponseEntity.ok(campaigns);
    }

    @Override
    @GetMapping("/{id}")
    public ResponseEntity<CampaignDto> getCampaignById(@PathVariable Long id) {
        CampaignDto campaign = campaignService.findById(id);

        return ResponseEntity.ok(campaign);
    }

    @Override
    @PutMapping("/{id}")
    public ResponseEntity<CampaignDto> updateCampaign(@PathVariable Long id, @RequestBody CampaignIUDto campaignIUDto) {
        CampaignDto campaign = campaignService.update(id, campaignIUDto);

        return ResponseEntity.ok(campaign);
    }

    @Override
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteCampaign(@PathVariable Long id) {
        campaignService.delete(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/customer/remind-me")
    public ResponseEntity<List<String>> getRemindMeCustomerEmails() {
        List<String> remindMeEmails = campaignService.getRemindMeCustomerEmails();
        return ResponseEntity.ok(remindMeEmails);
    }
    
    @PutMapping("/{id}/detail")
    public ResponseEntity<CampaignDto> updateCampaignDetail(@PathVariable Long id, @RequestBody CampaignDetailIUDto campaignDetailIUDto) {
        try {
            // Kampanya detayını güncelle
            campaignDetailService.update(id, campaignDetailIUDto);
            
            // Güncellenmiş kampanyayı döndür
            CampaignDto campaign = campaignService.findById(id);
            return ResponseEntity.ok(campaign);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            
        }
    }
}
