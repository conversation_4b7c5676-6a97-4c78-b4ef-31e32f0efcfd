package com.backend360.campaign_service.controller.Impl;

import com.backend360.campaign_service.controller.ICustomerToCampaignController;
import com.backend360.campaign_service.dto.CustomerToCampaignDto;
import com.backend360.campaign_service.dto.CustomerToCampaignIUDto;
import com.backend360.campaign_service.service.ICustomerToCampaignService;
import com.backend360.campaign_service.service.Impl.EmailServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/customer-to-campaign")
public class CustomerToCampaignControllerImpl implements ICustomerToCampaignController {
    @Autowired
    private ICustomerToCampaignService customerToCampaignService;

    @Autowired
    private EmailServiceImpl emailService;

    @Override
    @PostMapping
    public ResponseEntity<CustomerToCampaignDto> createCustomerToCampaign(@RequestBody CustomerToCampaignIUDto customerToCampaignIUDto) {
        log.info("Customer-to-campaign oluşturuluyor: {}", customerToCampaignIUDto);

        // Debug: Authentication bilgilerini logla
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && auth.isAuthenticated() && !"anonymousUser".equals(auth.getName())) {
            log.info("Campaign Service - Customer-to-campaign POST - Authenticated user: {}, authorities: {}",
                auth.getName(), auth.getAuthorities());
        } else {
            log.info("Campaign Service - Customer-to-campaign POST - Anonymous user request (no authentication required)");
        }

        CustomerToCampaignDto response = customerToCampaignService.save(customerToCampaignIUDto);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Override
    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('CRM_ADMIN')")
    public ResponseEntity<List<CustomerToCampaignDto>> getAllCustomerToCampaigns() {
        List<CustomerToCampaignDto> customerToCampaigns = customerToCampaignService.findAll();

        if (customerToCampaigns.isEmpty()) {
            return ResponseEntity.noContent().build();
        }

        return ResponseEntity.ok(customerToCampaigns);
    }

    @Override
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('CRM_ADMIN')")
    public ResponseEntity<CustomerToCampaignDto> getCustomerToCampaignById(@PathVariable Long id) {
        CustomerToCampaignDto customerToCampaign = customerToCampaignService.findById(id);

        return ResponseEntity.ok(customerToCampaign);
    }

    @Override
    @PutMapping("/{id}")
    public ResponseEntity<CustomerToCampaignDto> updateCustomerToCampaign(@PathVariable Long id, @RequestBody CustomerToCampaignIUDto customerToCampaignIUDto) {
        CustomerToCampaignDto customerToCampaign = customerToCampaignService.update(id, customerToCampaignIUDto);
        
        return ResponseEntity.ok(customerToCampaign);
    }

    @Override
    @PutMapping("/change-is-called/{id}")
    public ResponseEntity<Void> changeIsCalledStatus(@PathVariable Long id, @RequestParam Boolean status) {
        customerToCampaignService.changeIsCalledStatus(id, status);
        return ResponseEntity.ok().build();
    }

    @Override
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteCustomerToCampaign(@PathVariable Long id) {
        customerToCampaignService.delete(id);

        return ResponseEntity.noContent().build();
    }
}
