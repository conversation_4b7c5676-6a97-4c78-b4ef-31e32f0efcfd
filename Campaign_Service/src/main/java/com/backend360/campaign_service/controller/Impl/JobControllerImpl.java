package com.backend360.campaign_service.controller.Impl;

import com.backend360.campaign_service.controller.IJobController;
import com.backend360.campaign_service.dto.JobDto;
import com.backend360.campaign_service.dto.JobIUDto;
import com.backend360.campaign_service.service.IJobService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/job")
public class JobControllerImpl implements IJobController {
    @Autowired
    private IJobService jobService;

    @Override
    @PostMapping
    public ResponseEntity<JobDto> createJob(@RequestBody JobIUDto jobIUDto) {
        JobDto response = jobService.save(jobIUDto);

        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Override
    @GetMapping
    public ResponseEntity<List<JobDto>> getAllJobs() {
        List<JobDto> jobs = jobService.findAll();

        if (jobs.isEmpty()) {
            return ResponseEntity.noContent().build();
        }

        return ResponseEntity.ok(jobs);
    }

    @Override
    @GetMapping("/{id}")
    public ResponseEntity<JobDto> getJobById(@PathVariable Long id) {
        JobDto job = jobService.findById(id);

        return ResponseEntity.ok(job);
    }

    @Override
    @PutMapping("/{id}")
    public ResponseEntity<JobDto> updateJob(@PathVariable Long id, @RequestBody JobIUDto jobIUDto) {
        JobDto job = jobService.update(id, jobIUDto);

        return ResponseEntity.ok(job);
    }

    @Override
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteJob(@PathVariable Long id) {
        jobService.delete(id);
        
        return ResponseEntity.noContent().build();
    }
}
