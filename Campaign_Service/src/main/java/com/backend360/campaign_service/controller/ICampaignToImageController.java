package com.backend360.campaign_service.controller;

import com.backend360.campaign_service.dto.CampaignToImageDto;
import com.backend360.campaign_service.dto.CampaignToImageIUDto;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface ICampaignToImageController {

    ResponseEntity<List<CampaignToImageDto>> getAllCampaignToImages();

    ResponseEntity<CampaignToImageDto> getCampaignToImageById(Long id);

    ResponseEntity<CampaignToImageDto> updateCampaignToImage(Long id, CampaignToImageIUDto campaignToImageIUDto);

    ResponseEntity<Void> deleteCampaignToImage(Long id);

    // Showcase resmi yükleme
    ResponseEntity<CampaignToImageDto> uploadCampaignImage(Long campaignId,
                                                           MultipartFile imageFile) throws IOException;

    // Çoklu detay resim yükleme
    ResponseEntity<List<CampaignToImageDto>> uploadMultipleDetailImages(Long campaignId,
                                                                        MultipartFile[] imageFiles) throws IOException;
    // Vitrin görselini getir (tek resim olarak byte[])
    ResponseEntity<byte[]> getShowcaseImageByCampaignId(Long campaignId);

    // Detay görselleri getir (HTML olarak)
    ResponseEntity<String> getDetailImagesByCampaignId(Long campaignId);

    // Tüm görselleri getir (HTML olarak)
    ResponseEntity<String> getAllImagesByCampaignId(Long campaignId);
    // Güncelleme metodları sadeleşiyor, sadece dosya bazlı güncelleme:
    ResponseEntity<CampaignToImageDto> updateImageFile(Long id, MultipartFile imageFile) throws IOException;

    // Vitrin görselini güncelleme
    ResponseEntity<CampaignToImageDto> updateShowcaseImageByCampaignId(Long campaignId, MultipartFile imageFile) throws IOException;

}
