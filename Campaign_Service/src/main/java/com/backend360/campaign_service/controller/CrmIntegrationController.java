package com.backend360.campaign_service.controller;

import com.backend360.campaign_service.crm_client.CrmClient;
import com.backend360.campaign_service.dto.CustomerToCampaignDto;
import com.backend360.campaign_service.dto.FormToCampaignDto;
import com.backend360.campaign_service.dto.crm.ContactDto;
import com.backend360.campaign_service.service.CrmContactIntegrationService;
import com.backend360.campaign_service.service.CrmTokenManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1")
@CrossOrigin(origins = "*")
@Slf4j
public class CrmIntegrationController {

    @Autowired
    private CrmContactIntegrationService crmContactIntegrationService;

    @Autowired
    private CrmClient crmClient;

    @Autowired
    private CrmTokenManager crmTokenManager;

    @Value("${crm.api.url}")
    private String crmApiUrl;

    @PostMapping("/crm-integration/send-customer")
    public ResponseEntity<String> sendCustomerToCrm(@RequestBody CustomerToCampaignDto customerToCampaign) {
        crmContactIntegrationService.sendCustomerToCrm(customerToCampaign);
        return ResponseEntity.ok("Customer sent to CRM successfully");
    }

    @PostMapping("/crm-integration/send-form")
    public ResponseEntity<String> sendFormToCrm(@RequestBody FormToCampaignDto formToCampaign) {
        log.info("Received request to send form to CRM: {}", formToCampaign.getForm().getEmail());
        crmContactIntegrationService.sendFormToCrm(formToCampaign);
        return ResponseEntity.ok("Form sent to CRM successfully");
    }

    // Test endpoint for CRM connection
    @GetMapping("/crm-admin/test-connection")
    public ResponseEntity<Map<String, Object>> testCrmConnection() {
        Map<String, Object> result = new HashMap<>();
        try {
            ResponseEntity<Map<String, Object>> response = crmClient.getAllContacts(crmTokenManager.getAuthorizationHeader(), 1000);

            result.put("success", true);
            result.put("crmUrl", crmApiUrl);
            result.put("statusCode", response.getStatusCode().value());
            result.put("hasToken", true);
            result.put("message", "CRM bağlantısı başarılı");

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("CRM bağlantı testi başarısız: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("crmUrl", crmApiUrl);
            result.put("hasToken", true);
            result.put("error", e.getMessage());
            result.put("message", "CRM bağlantısı başarısız");
            return ResponseEntity.ok(result);
        }
    }

    @PostMapping("/crm-integration/send-campaign")
    public ResponseEntity<String> sendCampaignToCrm(@RequestBody com.backend360.campaign_service.model.Campaign campaign) {
        log.info("Received request to send campaign to CRM: {}", campaign.getName());
        crmContactIntegrationService.sendCampaignToCrm(campaign);
        return ResponseEntity.ok("Campaign sent to CRM successfully");
    }

    // CRM Admin Contacts Endpoints
    @GetMapping("/crm-admin/contacts")
    public ResponseEntity<List<Map<String, Object>>> getAllContacts() {
        try {
            log.info("CRM Admin - Tüm contact'lar isteniyor");

            ResponseEntity<Map<String, Object>> response = crmClient.getAllContacts(crmTokenManager.getAuthorizationHeader(), 1000);

            Map<String, Object> responseBody = response.getBody();
            if (responseBody != null && responseBody.containsKey("content")) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> contacts = (List<Map<String, Object>>) responseBody.get("content");
                log.info("Contact'lar başarıyla getirildi: {} adet", contacts != null ? contacts.size() : 0);
                return ResponseEntity.ok(contacts != null ? contacts : Collections.emptyList());
            } else {
                log.warn("CRM response'unda content bulunamadı");
                return ResponseEntity.ok(Collections.emptyList());
            }
        } catch (Exception e) {
            log.error("Contact'lar getirilemedi: {}", e.getMessage(), e);
            return ResponseEntity.ok(Collections.emptyList());
        }
    }

    @GetMapping("/crm-admin/contacts/{id}")
    public ResponseEntity<Map<String, Object>> getContactById(@PathVariable String id) {
        try {
            log.info("CRM Admin - Contact detayı isteniyor: {}", id);

            ResponseEntity<ContactDto> response = crmClient.getContactById(UUID.fromString(id), crmTokenManager.getAuthorizationHeader());

            ContactDto contactDto = response.getBody();
            if (contactDto != null) {
                // ContactDto'yu Map'e çevir
                Map<String, Object> contact = new HashMap<>();
                contact.put("uuid", contactDto.getUuid());
                contact.put("name", contactDto.getName());
                contact.put("surname", contactDto.getSurname());
                contact.put("email", contactDto.getEmail());
                contact.put("phoneNumber", contactDto.getPhoneNumber());
                contact.put("countryCode", contactDto.getCountryCode());
                contact.put("status", contactDto.getStatus());
                contact.put("createdAt", contactDto.getCreatedAt());
                return ResponseEntity.ok(contact);
            } else {
                return ResponseEntity.ok(Collections.emptyMap());
            }
        } catch (Exception e) {
            log.error("Contact detayı getirilemedi: {}", e.getMessage(), e);
            return ResponseEntity.ok(Collections.emptyMap());
        }
    }

    // CRM Admin Webhook Events Endpoints
    @GetMapping("/crm-admin/webhook-events")
    public ResponseEntity<List<Map<String, Object>>> getAllWebhookEvents() {
        try {
            log.info("CRM Admin - Tüm webhook event'lar isteniyor");

            ResponseEntity<List<Map<String, Object>>> response = crmClient.getAllWebhookEvents(crmTokenManager.getAuthorizationHeader());

            List<Map<String, Object>> events = response.getBody();
            log.info("Webhook event'lar başarıyla getirildi: {} adet", events != null ? events.size() : 0);
            return ResponseEntity.ok(events != null ? events : Collections.emptyList());
        } catch (Exception e) {
            log.error("Webhook event'lar getirilemedi: {}", e.getMessage(), e);
            return ResponseEntity.ok(Collections.emptyList());
        }
    }

    @GetMapping("/crm-admin/webhook-events/{id}")
    public ResponseEntity<Map<String, Object>> getWebhookEventById(@PathVariable String id) {
        try {
            log.info("CRM Admin - Webhook event detayı isteniyor: {}", id);

            ResponseEntity<Map<String, Object>> response = crmClient.getWebhookEventById(UUID.fromString(id), crmTokenManager.getAuthorizationHeader());

            Map<String, Object> event = response.getBody();
            return ResponseEntity.ok(event != null ? event : Collections.emptyMap());
        } catch (Exception e) {
            log.error("Webhook event detayı getirilemedi: {}", e.getMessage(), e);
            return ResponseEntity.ok(Collections.emptyMap());
        }
    }

    @GetMapping("/crm-admin/webhook-events/by-phone/{phoneNumber}")
    public ResponseEntity<List<Map<String, Object>>> getWebhookEventsByPhoneNumber(@PathVariable String phoneNumber) {
        try {
            log.info("CRM Admin - Telefon numarasına göre webhook event'lar isteniyor: {}", phoneNumber);

            ResponseEntity<List<Map<String, Object>>> response = crmClient.getWebhookEventsByPhoneNumber(phoneNumber, crmTokenManager.getAuthorizationHeader());

            List<Map<String, Object>> events = response.getBody();
            log.info("Telefon numarasına göre webhook event'lar başarıyla getirildi: {} adet", events != null ? events.size() : 0);
            return ResponseEntity.ok(events != null ? events : Collections.emptyList());
        } catch (Exception e) {
            log.error("Telefon numarasına göre webhook event'lar getirilemedi: {}", e.getMessage(), e);
            return ResponseEntity.ok(Collections.emptyList());
        }
    }
}