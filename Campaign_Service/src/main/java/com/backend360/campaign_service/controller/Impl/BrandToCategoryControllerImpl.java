package com.backend360.campaign_service.controller.Impl;

import com.backend360.campaign_service.controller.IBrandToCategoryController;
import com.backend360.campaign_service.dto.BrandToCategoryDto;
import com.backend360.campaign_service.dto.BrandToCategoryIUDto;
import com.backend360.campaign_service.service.IBrandToCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/brand-to-category")
public class BrandToCategoryControllerImpl implements IBrandToCategoryController {
    @Autowired
    private IBrandToCategoryService brandToCategoryService;

    @Override
    @PostMapping
    public ResponseEntity<BrandToCategoryDto> createBrandToCategory(@RequestBody BrandToCategoryIUDto brandToCategoryIUDto) {
        BrandToCategoryDto response = brandToCategoryService.save(brandToCategoryIUDto);

        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Override
    @GetMapping
    public ResponseEntity<List<BrandToCategoryDto>> getAllBrandToCategories() {
        List<BrandToCategoryDto> brandToCategories = brandToCategoryService.findAll();

        if (brandToCategories.isEmpty()) {
            return ResponseEntity.noContent().build();
        }

        return ResponseEntity.ok(brandToCategories);
    }

    @Override
    @GetMapping("/{id}")
    public ResponseEntity<BrandToCategoryDto> getBrandToCategoryById(@PathVariable Long id) {
        BrandToCategoryDto brandToCategory = brandToCategoryService.findById(id);

        return ResponseEntity.ok(brandToCategory);
    }

    @Override
    @PutMapping("/{id}")
    public ResponseEntity<BrandToCategoryDto> updateBrandToCategory(@PathVariable Long id, @RequestBody BrandToCategoryIUDto brandToCategoryIUDto) {
        BrandToCategoryDto brandToCategory = brandToCategoryService.update(id, brandToCategoryIUDto);

        return ResponseEntity.ok(brandToCategory);
    }

    @Override
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteBrandToCategory(@PathVariable Long id) {
        brandToCategoryService.delete(id);
        return ResponseEntity.noContent().build();
    }
}
