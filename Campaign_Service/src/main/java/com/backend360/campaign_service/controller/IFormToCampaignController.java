package com.backend360.campaign_service.controller;

import com.backend360.campaign_service.dto.FormToCampaignDto;
import com.backend360.campaign_service.dto.FormToCampaignIUDto;
import org.springframework.http.ResponseEntity;

import java.util.List;

public interface IFormToCampaignController {
    ResponseEntity<FormToCampaignDto> createFormToCampaign(FormToCampaignIUDto formToCampaignIUDto);

    ResponseEntity<List<FormToCampaignDto>> getAllFormToCampaigns();

    ResponseEntity<FormToCampaignDto> getFormToCampaignById(Long id);

    ResponseEntity<FormToCampaignDto> updateFormToCampaign(Long id, FormToCampaignIUDto formToCampaignIUDto);

    ResponseEntity<Void> deleteFormToCampaign(Long id);
}
