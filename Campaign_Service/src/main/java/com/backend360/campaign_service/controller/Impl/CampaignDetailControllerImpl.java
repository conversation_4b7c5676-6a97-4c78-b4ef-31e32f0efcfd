package com.backend360.campaign_service.controller.Impl;

import com.backend360.campaign_service.controller.ICampaignDetailController;
import com.backend360.campaign_service.dto.CampaignDetailDto;
import com.backend360.campaign_service.dto.CampaignDetailIUDto;
import com.backend360.campaign_service.service.ICampaignDetailService;
import com.backend360.campaign_service.service.ICategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/campaign-detail")
public class CampaignDetailControllerImpl implements ICampaignDetailController {
    @Autowired
    private ICampaignDetailService campaignDetailService;
    
    @Autowired
    private ICategoryService categoryService;

    @Override
    @PostMapping
    public ResponseEntity<CampaignDetailDto> createCampaignDetail(@RequestBody CampaignDetailIUDto campaignDetailIUDto) {
        CampaignDetailDto response = campaignDetailService.save(campaignDetailIUDto);

        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Override
    @GetMapping
    public ResponseEntity<List<CampaignDetailDto>> getAllCampaignDetails() {
        List<CampaignDetailDto> campaignDetails = campaignDetailService.findAll();

        if (campaignDetails.isEmpty()) {
            return ResponseEntity.noContent().build();
        }

        return ResponseEntity.ok(campaignDetails);
    }

    @Override
    @GetMapping("/{id}")
    public ResponseEntity<CampaignDetailDto> getCampaignDetailById(@PathVariable Long id) {
        CampaignDetailDto campaignDetail = campaignDetailService.findById(id);

        return ResponseEntity.ok(campaignDetail);
    }

    @Override
    @PutMapping("/{id}")
    public ResponseEntity<CampaignDetailDto> updateCampaignDetail(@PathVariable Long id, @RequestBody CampaignDetailIUDto campaignDetailIUDto) {
        CampaignDetailDto campaignDetail = campaignDetailService.update(id, campaignDetailIUDto);

        return ResponseEntity.ok(campaignDetail);
    }

    @Override
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteCampaignDetail(@PathVariable Long id) {
        campaignDetailService.delete(id);
        return ResponseEntity.noContent().build();
    }
    
    @GetMapping("/category/{categoryId}")
    public ResponseEntity<CampaignDetailDto> getCampaignDetailByCategory(@PathVariable Long categoryId) {
        try {
            CampaignDetailDto campaignDetail = categoryService.findCampaignDetailByCategoryId(categoryId);
            if (campaignDetail != null) {
                return ResponseEntity.ok(campaignDetail);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
