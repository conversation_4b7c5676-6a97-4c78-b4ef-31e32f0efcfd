package com.backend360.campaign_service.controller;

import com.backend360.campaign_service.dto.BrandToCampaignDto;
import com.backend360.campaign_service.dto.BrandToCampaignIUDto;
import org.springframework.http.ResponseEntity;

import java.util.List;

public interface IBrandToCampaignController {
    ResponseEntity<BrandToCampaignDto> createBrandToCampaign(BrandToCampaignIUDto brandToCampaignIUDto);

    ResponseEntity<List<BrandToCampaignDto>> getAllBrandToCampaigns();

    ResponseEntity<BrandToCampaignDto> getBrandToCampaignById(Long id);

    ResponseEntity<BrandToCampaignDto> updateBrandToCampaign(Long id, BrandToCampaignIUDto brandToCampaignIUDto);

    ResponseEntity<Void> deleteBrandToCampaign(Long id);
}
