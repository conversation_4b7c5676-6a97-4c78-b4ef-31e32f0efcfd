package com.backend360.campaign_service.controller.Impl;

import com.backend360.campaign_service.controller.IBrandToCampaignController;
import com.backend360.campaign_service.dto.BrandToCampaignDto;
import com.backend360.campaign_service.dto.BrandToCampaignIUDto;
import com.backend360.campaign_service.service.IBrandToCampaignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/brand-to-campaign")
public class BrandToCampaignControllerImpl implements IBrandToCampaignController {
    @Autowired
    private IBrandToCampaignService brandToCampaignService;

    @Override
    @PostMapping
    public ResponseEntity<BrandToCampaignDto> createBrandToCampaign(@RequestBody BrandToCampaignIUDto brandToCampaignIUDto) {
        BrandToCampaignDto response = brandToCampaignService.save(brandToCampaignIUDto);

        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Override
    @GetMapping
    public ResponseEntity<List<BrandToCampaignDto>> getAllBrandToCampaigns() {
        List<BrandToCampaignDto> brandToCampaigns = brandToCampaignService.findAll();

        if (brandToCampaigns.isEmpty()) {
            return ResponseEntity.noContent().build();
        }

        return ResponseEntity.ok(brandToCampaigns);
    }

    @Override
    @GetMapping("/{id}")
    public ResponseEntity<BrandToCampaignDto> getBrandToCampaignById(@PathVariable Long id) {
        BrandToCampaignDto brandToCampaign = brandToCampaignService.findById(id);

        return ResponseEntity.ok(brandToCampaign);
    }

    @Override
    @PutMapping("/{id}")
    public ResponseEntity<BrandToCampaignDto> updateBrandToCampaign(@PathVariable Long id, @RequestBody BrandToCampaignIUDto brandToCampaignIUDto) {
        BrandToCampaignDto brandToCampaign = brandToCampaignService.update(id, brandToCampaignIUDto);

        return ResponseEntity.ok(brandToCampaign);
    }

    @Override
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteBrandToCampaign(@PathVariable Long id) {
        brandToCampaignService.delete(id);
        
        return ResponseEntity.noContent().build();
    }
}
