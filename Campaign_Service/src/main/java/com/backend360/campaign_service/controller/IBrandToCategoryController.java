package com.backend360.campaign_service.controller;

import com.backend360.campaign_service.dto.BrandToCategoryDto;
import com.backend360.campaign_service.dto.BrandToCategoryIUDto;
import org.springframework.http.ResponseEntity;

import java.util.List;

public interface IBrandToCategoryController {
    ResponseEntity<BrandToCategoryDto> createBrandToCategory(BrandToCategoryIUDto brandIUDto);

    ResponseEntity<List<BrandToCategoryDto>> getAllBrandToCategories();

    ResponseEntity<BrandToCategoryDto> getBrandToCategoryById(Long id);

    ResponseEntity<BrandToCategoryDto> updateBrandToCategory(Long id, BrandToCategoryIUDto brandIUDto);

    ResponseEntity<Void> deleteBrandToCategory(Long id);
}
