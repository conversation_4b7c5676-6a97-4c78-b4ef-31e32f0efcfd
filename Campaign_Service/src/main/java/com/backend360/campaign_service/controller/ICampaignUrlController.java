package com.backend360.campaign_service.controller;

import com.backend360.campaign_service.dto.CampaignUrlDto;
import com.backend360.campaign_service.dto.CampaignUrlIUDto;
import org.springframework.http.ResponseEntity;

import java.util.List;

public interface ICampaignUrlController {
    ResponseEntity<CampaignUrlDto> createCampaignUrl(CampaignUrlIUDto campaignUrlIUDto);

    ResponseEntity<List<CampaignUrlDto>> getAllCampaignUrls();

    ResponseEntity<CampaignUrlDto> getCampaignUrlById(Long id);

    ResponseEntity<CampaignUrlDto> updateCampaignUrl(Long id, CampaignUrlIUDto campaignUrlIUDto);

    ResponseEntity<Void> deleteCampaignUrl(Long id);
}
