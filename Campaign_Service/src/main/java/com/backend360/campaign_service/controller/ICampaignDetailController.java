package com.backend360.campaign_service.controller;

import com.backend360.campaign_service.dto.CampaignDetailDto;
import com.backend360.campaign_service.dto.CampaignDetailIUDto;
import org.springframework.http.ResponseEntity;

import java.util.List;

public interface ICampaignDetailController {
    ResponseEntity<CampaignDetailDto> createCampaignDetail(CampaignDetailIUDto campaignDetailIUDto);

    ResponseEntity<List<CampaignDetailDto>> getAllCampaignDetails();

    ResponseEntity<CampaignDetailDto> getCampaignDetailById(Long id);

    ResponseEntity<CampaignDetailDto> updateCampaignDetail(Long id, CampaignDetailIUDto campaignDetailIUDto);

    ResponseEntity<Void> deleteCampaignDetail(Long id);
}
