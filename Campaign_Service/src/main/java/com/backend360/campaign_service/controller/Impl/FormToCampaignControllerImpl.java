package com.backend360.campaign_service.controller.Impl;

import com.backend360.campaign_service.controller.IFormToCampaignController;
import com.backend360.campaign_service.dto.FormToCampaignDto;
import com.backend360.campaign_service.dto.FormToCampaignIUDto;
import com.backend360.campaign_service.service.IFormToCampaignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/form-to-campaign")
public class FormToCampaignControllerImpl implements IFormToCampaignController {
    @Autowired
    private IFormToCampaignService formToCampaignService;

    @Override
    @PostMapping
    public ResponseEntity<FormToCampaignDto> createFormToCampaign(@RequestBody FormToCampaignIUDto formToCampaignIUDto) {
        log.info("Form-to-campaign oluşturuluyor: {}", formToCampaignIUDto);

        // Debug: Authentication bilgilerini logla
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && auth.isAuthenticated() && !"anonymousUser".equals(auth.getName())) {
            log.info("Campaign Service - Form-to-campaign POST - Authenticated user: {}, authorities: {}",
                auth.getName(), auth.getAuthorities());
        } else {
            log.info("Campaign Service - Form-to-campaign POST - Anonymous user request (no authentication required)");
        }

        FormToCampaignDto response = formToCampaignService.save(formToCampaignIUDto);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Override
    @GetMapping
    public ResponseEntity<List<FormToCampaignDto>> getAllFormToCampaigns() {
        List<FormToCampaignDto> formToCampaigns = formToCampaignService.findAll();

        if (formToCampaigns.isEmpty()) {
            return ResponseEntity.noContent().build();
        }

        return ResponseEntity.ok(formToCampaigns);
    }

    @Override
    @GetMapping("/{id}")
    public ResponseEntity<FormToCampaignDto> getFormToCampaignById(@PathVariable Long id) {
        FormToCampaignDto formToCampaign = formToCampaignService.findById(id);

        return ResponseEntity.ok(formToCampaign);
    }

    @Override
    @PutMapping("/{id}")
    public ResponseEntity<FormToCampaignDto> updateFormToCampaign(@PathVariable Long id, @RequestBody FormToCampaignIUDto formToCampaignIUDto) {
        FormToCampaignDto formToCampaign = formToCampaignService.update(id, formToCampaignIUDto);

        return ResponseEntity.ok(formToCampaign);
    }

    @Override
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteFormToCampaign(@PathVariable Long id) {
        formToCampaignService.delete(id);

        return ResponseEntity.noContent().build();
    }
}
