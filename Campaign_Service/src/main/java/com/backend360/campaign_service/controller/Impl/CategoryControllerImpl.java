package com.backend360.campaign_service.controller.Impl;

import com.backend360.campaign_service.controller.ICategoryController;
import com.backend360.campaign_service.dto.CategoryDto;
import com.backend360.campaign_service.dto.CategoryIUDto;
import com.backend360.campaign_service.service.ICategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@RequestMapping("/category")
public class CategoryControllerImpl implements ICategoryController {
    @Autowired
    private ICategoryService categoryService;

    @Override
    @PostMapping
    public ResponseEntity<CategoryDto> createCategory(@RequestBody CategoryIUDto categoryIUDto) {
        CategoryDto response = categoryService.save(categoryIUDto);

        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Override
    @GetMapping
    public ResponseEntity<List<CategoryDto>> getAllCategories() {
        List<CategoryDto> categories = categoryService.findAll();

        if (categories.isEmpty()) {
            return ResponseEntity.noContent().build();
        }

        return ResponseEntity.ok(categories);
    }

    @Override
    @GetMapping("/{id}")
    public ResponseEntity<CategoryDto> getCategoryById(@PathVariable Long id) {
        CategoryDto category = categoryService.findById(id);

        return ResponseEntity.ok(category);
    }

    @Override
    @PutMapping("/{id}")
    public ResponseEntity<CategoryDto> updateCategory(@PathVariable Long id, @RequestBody CategoryIUDto categoryIUDto) {
        CategoryDto category = categoryService.update(id, categoryIUDto);

        return ResponseEntity.ok(category);
    }

    @Override
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteCategory(@PathVariable Long id) {
        categoryService.delete(id);
        return ResponseEntity.noContent().build();
    }
}
