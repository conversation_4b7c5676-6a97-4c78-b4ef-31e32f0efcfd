package com.backend360.campaign_service.controller.Impl;

import com.backend360.campaign_service.controller.ICampaignUrlController;
import com.backend360.campaign_service.dto.CampaignUrlDto;
import com.backend360.campaign_service.dto.CampaignUrlIUDto;
import com.backend360.campaign_service.service.ICampaignUrlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/campaign-url")
public class CampaignUrlControllerImpl implements ICampaignUrlController {
    @Autowired
    private ICampaignUrlService campaignUrlService;

    @Override
    @PostMapping
    public ResponseEntity<CampaignUrlDto> createCampaignUrl(@RequestBody CampaignUrlIUDto campaignUrlIUDto) {
        CampaignUrlDto response = campaignUrlService.save(campaignUrlIUDto);

        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Override
    @GetMapping
    public ResponseEntity<List<CampaignUrlDto>> getAllCampaignUrls() {
        List<CampaignUrlDto> campaignUrls = campaignUrlService.findAll();

        if (campaignUrls.isEmpty()) {
            return ResponseEntity.noContent().build();
        }

        return ResponseEntity.ok(campaignUrls);
    }

    @Override
    @GetMapping("/{id}")
    public ResponseEntity<CampaignUrlDto> getCampaignUrlById(@PathVariable Long id) {
        CampaignUrlDto campaignUrl = campaignUrlService.findById(id);

        return ResponseEntity.ok(campaignUrl);
    }

    @Override
    @PutMapping("/{id}")
    public ResponseEntity<CampaignUrlDto> updateCampaignUrl(@PathVariable Long id, @RequestBody CampaignUrlIUDto campaignUrlIUDto) {
        CampaignUrlDto campaignUrl = campaignUrlService.update(id, campaignUrlIUDto);

        return ResponseEntity.ok(campaignUrl);
    }

    @Override
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteCampaignUrl(@PathVariable Long id) {
        campaignUrlService.delete(id);
        
        return ResponseEntity.noContent().build();
    }
}
