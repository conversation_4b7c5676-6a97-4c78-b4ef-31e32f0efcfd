package com.backend360.campaign_service.controller;

import com.backend360.campaign_service.crm_client.CrmClient;
import com.backend360.campaign_service.service.CrmTokenManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import com.backend360.campaign_service.dto.crm.ContactIUDto;
import com.backend360.campaign_service.dto.crm.ContactDto;

@RestController
@RequestMapping("/api/v1/crm-token/test")
@CrossOrigin(origins = "*")
@Slf4j
public class CrmTokenTestController {

    @Autowired
    private CrmTokenManager crmTokenManager;

    @Autowired
    private CrmClient crmClient;

    /**
     * CRM bağlantısını test eder (token ile)
     */
    @GetMapping("/connection")
    public ResponseEntity<Map<String, Object>> testConnection() {
        try {
            log.info("CRM bağlantı testi başlatıldı");

            String authHeader = crmTokenManager.getAuthorizationHeader();

            Map<String, Object> response = Map.of(
                "message", "CRM token hazır",
                "authHeaderPreview", authHeader.substring(0, 30) + "...",
                "tokenInfo", crmTokenManager.getTokenInfo()
            );

            log.info("CRM bağlantı testi başarılı");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("CRM bağlantı testi başarısız: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * CRM'den customer bilgisi çeker (test endpoint)
     */
    @GetMapping("/customer-time")
    public ResponseEntity<Map<String, Object>> getCustomerTime() {
        try {
            log.info("CRM'den customer bilgisi çekiliyor (test)");

            String authHeader = crmTokenManager.getAuthorizationHeader();
            
            // Test CRM connection with token
            ResponseEntity<Map<String, Object>> response = crmClient.getAllContacts(authHeader, 1);

            Map<String, Object> result = Map.of(
                "message", "CRM bağlantısı başarılı",
                "statusCode", response.getStatusCode().value(),
                "hasData", response.getBody() != null,
                "tokenInfo", crmTokenManager.getTokenInfo()
            );

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("CRM test başarısız: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of(
                    "error", e.getMessage(),
                    "tokenInfo", crmTokenManager.getTokenInfo()
                ));
        }
    }

    /**
     * CRM token'ını yeniler ve test eder
     */
    @PostMapping("/refresh-token")
    public ResponseEntity<Map<String, Object>> refreshToken() {
        try {
            log.info("CRM token yenileniyor (manuel)");
            
            // Token cache'ini temizle
            crmTokenManager.clearTokenCache();
            
            // Yeni token al
            String authHeader = crmTokenManager.getAuthorizationHeader();
            
            Map<String, Object> result = Map.of(
                "message", "Token başarıyla yenilendi",
                "authHeaderPreview", authHeader.substring(0, 30) + "...",
                "tokenInfo", crmTokenManager.getTokenInfo()
            );

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("Token yenileme başarısız: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of(
                    "error", e.getMessage(),
                    "tokenInfo", crmTokenManager.getTokenInfo()
                ));
        }
    }

    /**
     * CRM login endpoint'ini test eder
     */
    @GetMapping("/test-login")
    public ResponseEntity<Map<String, Object>> testLogin() {
        try {
            log.info("CRM login endpoint test ediliyor");
            
            // CrmTokenManager'dan login URL'ini al
            String loginUrl = "http://crm-api-gateway:6002/api/auth/login";
            
            Map<String, Object> result = Map.of(
                "message", "Login URL hazır",
                "loginUrl", loginUrl,
                "tokenInfo", crmTokenManager.getTokenInfo()
            );

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("Login test başarısız: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of(
                    "error", e.getMessage(),
                    "tokenInfo", crmTokenManager.getTokenInfo()
                ));
        }
    }

    /**
     * CRM bağlantısını CrmTokenManager ile test eder
     */
    @GetMapping("/test-crm-connection")
    public ResponseEntity<Map<String, Object>> testCrmConnection() {
        try {
            log.info("CRM bağlantı testi başlatıldı (CrmTokenManager ile)");
            
            Map<String, Object> result = crmTokenManager.testCrmConnection();
            
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("CRM bağlantı testi başarısız: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of(
                    "error", e.getMessage(),
                    "tokenInfo", crmTokenManager.getTokenInfo()
                ));
        }
    }

    /**
     * CRM'e minimal contact gönderimi test eder
     */
    @PostMapping("/test-contact-creation")
    public ResponseEntity<Map<String, Object>> testContactCreation() {
        try {
            log.info("CRM'e minimal contact gönderimi test ediliyor");
            
            // Minimal contact oluştur
            ContactIUDto minimalContact = ContactIUDto.builder()
                .name("Test User")
                .surname("Test Surname")
                .email("<EMAIL>")
                .phoneNumber("5551234567")
                .countryCode("+90")
                .timezone("Europe/Istanbul")
                .status("Active")
                .build();
            
            String authHeader = crmTokenManager.getAuthorizationHeader();
            
            log.info("Minimal contact gönderiliyor: {}", minimalContact);
            ResponseEntity<ContactDto> response = crmClient.createContact(minimalContact, authHeader);
            
            Map<String, Object> result = Map.of(
                "success", response.getStatusCode().is2xxSuccessful(),
                "statusCode", response.getStatusCode().value(),
                "responseBody", response.getBody(),
                "message", response.getStatusCode().is2xxSuccessful() ? "Contact başarıyla oluşturuldu" : "Contact oluşturulamadı"
            );
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("Contact oluşturma testi başarısız: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of(
                    "error", e.getMessage(),
                    "errorType", e.getClass().getSimpleName(),
                    "tokenInfo", crmTokenManager.getTokenInfo()
                ));
        }
    }

    /**
     * CRM konfigürasyonunu kontrol eder
     */
    @GetMapping("/check-config")
    public ResponseEntity<Map<String, Object>> checkConfig() {
        try {
            log.info("CRM konfigürasyonu kontrol ediliyor");
            
            Map<String, Object> config = Map.of(
                "crmApiUrl", System.getProperty("crm.api.url", "Not set"),
                "crmAuthUsername", System.getProperty("crm.auth.username", "Not set"),
                "crmAuthPassword", System.getProperty("crm.auth.password", "Not set") != null ? "***" : "Not set",
                "environmentVariables", Map.of(
                    "CRM_AUTH_USERNAME", System.getenv("CRM_AUTH_USERNAME") != null ? "Set" : "Not set",
                    "CRM_AUTH_PASSWORD", System.getenv("CRM_AUTH_PASSWORD") != null ? "Set" : "Not set"
                ),
                "tokenInfo", crmTokenManager.getTokenInfo()
            );
            
            return ResponseEntity.ok(config);
            
        } catch (Exception e) {
            log.error("Konfigürasyon kontrolü başarısız: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * CRM API health check
     */
    @GetMapping("/health-check")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        try {
            log.info("CRM API health check başlatıldı");
            
            // RestTemplate ile basit bir GET isteği
            org.springframework.web.client.RestTemplate restTemplate = new org.springframework.web.client.RestTemplate();
            String healthUrl = "http://crm-api-gateway:6002/actuator/health";
            
            try {
                org.springframework.http.ResponseEntity<String> response = restTemplate.getForEntity(healthUrl, String.class);
                
                Map<String, Object> result = Map.of(
                    "reachable", true,
                    "statusCode", response.getStatusCode().value(),
                    "response", response.getBody(),
                    "message", "CRM API erişilebilir"
                );
                
                return ResponseEntity.ok(result);
                
            } catch (Exception e) {
                Map<String, Object> result = Map.of(
                    "reachable", false,
                    "error", e.getMessage(),
                    "message", "CRM API erişilemez"
                );
                
                return ResponseEntity.ok(result);
            }
            
        } catch (Exception e) {
            log.error("Health check başarısız: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * CRM login endpoint'ini test eder
     */
    @GetMapping("/test-login-endpoint")
    public ResponseEntity<Map<String, Object>> testLoginEndpoint() {
        try {
            log.info("CRM login endpoint testi başlatıldı");
            
            Map<String, Object> result = crmTokenManager.testLoginEndpoint();
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("Login endpoint testi başarısız: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * Güvenlik konfigürasyonunu test eder
     */
    @GetMapping("/test-security")
    public ResponseEntity<Map<String, Object>> testSecurity() {
        try {
            log.info("Güvenlik konfigürasyonu test ediliyor");
            
            // Mevcut kullanıcının bilgilerini al
            org.springframework.security.core.Authentication auth = 
                org.springframework.security.core.context.SecurityContextHolder.getContext().getAuthentication();
            
            Map<String, Object> result = Map.of(
                "message", "Güvenlik testi başarılı",
                "authenticated", auth != null && auth.isAuthenticated(),
                "username", auth != null ? auth.getName() : "Anonymous",
                "authorities", auth != null ? auth.getAuthorities().toString() : "None",
                "timestamp", java.time.LocalDateTime.now()
            );
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("Güvenlik testi başarısız: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of("error", e.getMessage()));
        }
    }
}
