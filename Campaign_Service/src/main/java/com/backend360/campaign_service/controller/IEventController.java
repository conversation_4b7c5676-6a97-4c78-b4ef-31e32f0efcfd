package com.backend360.campaign_service.controller;

import com.backend360.campaign_service.dto.EventDto;
import com.backend360.campaign_service.dto.EventIUDto;
import com.backend360.campaign_service.dto.PopularItemDto;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;


public interface IEventController {
    ResponseEntity<EventDto> createEvent(@RequestBody EventIUDto eventIUDto);
    ResponseEntity<List<PopularItemDto>> getPopularCampaigns();
    ResponseEntity<List<PopularItemDto>> getPopularBrands();
    ResponseEntity<List<PopularItemDto>> getUserPopularCampaigns(@PathVariable String userId);
    ResponseEntity<List<PopularItemDto>> getUserPopularBrands(@PathVariable String userId);
} 