package com.backend360.campaign_service.controller.Impl;

import com.backend360.campaign_service.dto.FormDto;
import com.backend360.campaign_service.dto.FormIUDto;
import com.backend360.campaign_service.dto.FormToCampaignDto;
import com.backend360.campaign_service.dto.FormToCampaignIUDto;
import com.backend360.campaign_service.dto.CustomerToCampaignDto;
import com.backend360.campaign_service.dto.CustomerToCampaignIUDto;
import com.backend360.campaign_service.service.IFormService;
import com.backend360.campaign_service.service.IFormToCampaignService;
import com.backend360.campaign_service.service.ICustomerToCampaignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.HashMap;

@Slf4j
@RestController
@RequestMapping("/public/form-submission")
public class PublicFormControllerImpl {

    @Autowired
    private IFormService formService;

    @Autowired
    private IFormToCampaignService formToCampaignService;

    @Autowired
    private ICustomerToCampaignService customerToCampaignService;

    /**
     * Anonim kullanıcılar için form submission
     * Email ile duplicate kontrolü yapar
     */
    @PostMapping("/anonymous")
    public ResponseEntity<Map<String, Object>> submitAnonymousForm(@RequestBody Map<String, Object> request) {
        try {
            log.info("Anonymous form submission başlatılıyor");

            String email = (String) request.get("email");
            Integer campaignId = (Integer) request.get("campaignId");
            
            if (email == null || campaignId == null) {
                return ResponseEntity.badRequest().body(Map.of("error", "Email ve campaignId gerekli"));
            }

            // Email ile duplicate form kontrolü
            boolean existingForm = formService.existsByEmailAndIsActive(email, true);
            FormDto formDto;
            
            if (existingForm) {
                // Mevcut formu bul
                formDto = formService.findByEmailAndIsActive(email, true);
                log.info("Mevcut form bulundu: {}", formDto.getId());
            } else {
                // Yeni form oluştur
                FormIUDto formIUDto = new FormIUDto();
                formIUDto.setEmail(email);
                formIUDto.setName((String) request.get("name"));
                formIUDto.setSurname((String) request.get("surname"));
                formIUDto.setPhoneNumber((String) request.get("phoneNumber"));
                formIUDto.setCountry((String) request.get("country"));
                formIUDto.setCity((String) request.get("city"));
                formIUDto.setTown((String) request.get("town"));
                formIUDto.setGender((String) request.get("gender"));
                formIUDto.setIsActive(true);
                
                formDto = formService.save(formIUDto);
                log.info("Yeni form oluşturuldu: {}", formDto.getId());
            }

            // Form-Campaign ilişkisi kontrolü
            boolean existingRelation = formToCampaignService.existsByFormIdAndCampaignIdAndIsActive(
                formDto.getId(), campaignId.longValue(), true);
            
            FormToCampaignDto relationDto;
            if (existingRelation) {
                relationDto = formToCampaignService.findByFormIdAndCampaignIdAndIsActive(
                    formDto.getId(), campaignId.longValue(), true);
                log.info("Mevcut form-campaign ilişkisi bulundu: {}", relationDto.getId());
            } else {
                // Yeni ilişki oluştur
                FormToCampaignIUDto relationIUDto = new FormToCampaignIUDto();
                relationIUDto.setFormId(formDto.getId());
                relationIUDto.setCampaignId(campaignId.longValue());
                relationIUDto.setIsActive(true);
                relationIUDto.setContactEmail((String) request.get("contactEmail"));
                relationIUDto.setContactPhone((String) request.get("contactPhone"));
                
                relationDto = formToCampaignService.save(relationIUDto);
                log.info("Yeni form-campaign ilişkisi oluşturuldu: {}", relationDto.getId());
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("formId", formDto.getId());
            response.put("relationId", relationDto.getId());
            response.put("message", "Form başarıyla gönderildi");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Anonymous form submission hatası: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Form gönderilirken hata oluştu"));
        }
    }

    /**
     * Giriş yapmış kullanıcılar için form submission
     * CustomerId ile duplicate kontrolü yapar
     */
    @PostMapping("/authenticated")
    public ResponseEntity<Map<String, Object>> submitAuthenticatedForm(@RequestBody Map<String, Object> request) {
        try {
            log.info("Authenticated form submission başlatılıyor");

            Integer customerId = (Integer) request.get("customerId");
            Integer campaignId = (Integer) request.get("campaignId");
            
            if (customerId == null || campaignId == null) {
                return ResponseEntity.badRequest().body(Map.of("error", "CustomerId ve campaignId gerekli"));
            }

            // Customer-Campaign ilişkisi kontrolü
            boolean existingRelation = customerToCampaignService.existsByCustomerIdAndCampaignIdAndIsActive(
                customerId.longValue(), campaignId.longValue(), true);
            
            CustomerToCampaignDto relationDto;
            if (existingRelation) {
                relationDto = customerToCampaignService.findByCustomerIdAndCampaignIdAndIsActive(
                    customerId.longValue(), campaignId.longValue(), true);
                log.info("Mevcut customer-campaign ilişkisi bulundu: {}", relationDto.getId());
            } else {
                // Yeni ilişki oluştur
                CustomerToCampaignIUDto relationIUDto = new CustomerToCampaignIUDto();
                relationIUDto.setCustomerId(customerId.longValue());
                relationIUDto.setCampaignId(campaignId.longValue());
                relationIUDto.setIsActive(true);
                relationIUDto.setContactEmail((String) request.get("contactEmail"));
                relationIUDto.setContactPhone((String) request.get("contactPhone"));
                relationIUDto.setContactName((String) request.get("contactName"));
                relationIUDto.setContactSurname((String) request.get("contactSurname"));
                
                relationDto = customerToCampaignService.save(relationIUDto);
                log.info("Yeni customer-campaign ilişkisi oluşturuldu: {}", relationDto.getId());
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("relationId", relationDto.getId());
            response.put("message", "Form başarıyla gönderildi");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Authenticated form submission hatası: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Form gönderilirken hata oluştu"));
        }
    }

    /**
     * Email ile duplicate form kontrolü (public)
     */
    @GetMapping("/check-duplicate")
    public ResponseEntity<Map<String, Object>> checkDuplicateForm(
            @RequestParam String email, 
            @RequestParam Integer campaignId) {
        try {
            boolean hasExistingForm = formService.existsByEmailAndIsActive(email, true);
            boolean hasExistingRelation = false;
            
            if (hasExistingForm) {
                FormDto existingForm = formService.findByEmailAndIsActive(email, true);
                hasExistingRelation = formToCampaignService.existsByFormIdAndCampaignIdAndIsActive(
                    existingForm.getId(), campaignId.longValue(), true);
            }

            Map<String, Object> response = new HashMap<>();
            response.put("hasExistingForm", hasExistingForm);
            response.put("hasExistingRelation", hasExistingRelation);
            response.put("isDuplicate", hasExistingForm && hasExistingRelation);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Duplicate check hatası: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Kontrol sırasında hata oluştu"));
        }
    }
}
