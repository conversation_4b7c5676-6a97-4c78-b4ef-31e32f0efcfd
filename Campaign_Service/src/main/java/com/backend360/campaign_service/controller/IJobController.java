package com.backend360.campaign_service.controller;

import com.backend360.campaign_service.dto.JobDto;
import com.backend360.campaign_service.dto.JobIUDto;
import org.springframework.http.ResponseEntity;

import java.util.List;

public interface IJobController {
    ResponseEntity<JobDto> createJob(JobIUDto jobIUDto);

    ResponseEntity<List<JobDto>> getAllJobs();

    ResponseEntity<JobDto> getJobById(Long id);

    ResponseEntity<JobDto> updateJob(Long id, JobIUDto jobIUDto);

    ResponseEntity<Void> deleteJob(Long id);
}
