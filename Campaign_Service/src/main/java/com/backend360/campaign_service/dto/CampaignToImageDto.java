package com.backend360.campaign_service.dto;

import com.backend360.campaign_service.model.Campaign;
import com.backend360.campaign_service.model.CampaignToImage;
import lombok.*;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CampaignToImageDto implements Serializable {
    private Long id;
    private Campaign campaign;
    private String imageName;
    private String imagePath;
    private Boolean isShowcase;

    public static CampaignToImageDto entityToDto(CampaignToImage campaignToImage) {
        if (campaignToImage == null) return null;
        return CampaignToImageDto.builder()
                .id(campaignToImage.getId())
                .campaign(campaignToImage.getCampaign())
                .imageName(campaignToImage.getImageName())
                .imagePath(campaignToImage.getImagePath())
                .isShowcase(campaignToImage.getIsShowcase())
                .build();
    }
}
