package com.backend360.campaign_service.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FormToCampaignIUDto implements Serializable {
    private Long formId;
    private Long campaignId;
    private String ipAddress;
    private Boolean isActive;
    private String contactPhone;
    private String contactEmail;
    private String contactName;
    private String contactSurname;
}
