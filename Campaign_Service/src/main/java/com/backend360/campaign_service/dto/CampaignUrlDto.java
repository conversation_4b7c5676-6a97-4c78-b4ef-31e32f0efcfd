package com.backend360.campaign_service.dto;

import com.backend360.campaign_service.model.Campaign;
import com.backend360.campaign_service.model.CampaignUrl;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CampaignUrlDto implements Serializable {
    private Long id;
    private Campaign campaign;
    private String url;
    private Boolean isActive;

    public static CampaignUrlDto entityToDto(CampaignUrl campaignUrl) {
        if (campaignUrl == null) return null;
        return CampaignUrlDto.builder()
                .id(campaignUrl.getId())
                .campaign(campaignUrl.getCampaign())
                .url(campaignUrl.getUrl())
                .isActive(campaignUrl.getIsActive())
                .build();
    }
}
