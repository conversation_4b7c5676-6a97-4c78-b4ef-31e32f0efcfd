package com.backend360.campaign_service.dto;

import com.backend360.campaign_service.model.CampaignDetail;
import com.backend360.campaign_service.model.Category;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Optional;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CategoryDto implements Serializable {
    private Long id;
    private CampaignDetail campaignDetail;
    private String name;
    private Boolean isActive;
    private Long parentCategoryId;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime updatedAt;

    public static CategoryDto entityToDto(Category category) {
        if (category == null) return null;
        return CategoryDto.builder()
                .id(category.getId())
                .campaignDetail(category.getCampaignDetail())
                .name(category.getName())
                .isActive(category.getIsActive())
                .parentCategoryId(category.getParentCategoryId())
                .createdAt(category.getCreatedAt())
                .updatedAt(category.getUpdatedAt())
                .build();
    }

    public static CategoryDto fromEntity(Category category) {
        return Optional.ofNullable(category)
                .map(c -> CategoryDto.builder()
                        .id(c.getId())
                        .campaignDetail(c.getCampaignDetail())
                        .name(c.getName())
                        .isActive(c.getIsActive())
                        .parentCategoryId(c.getParentCategoryId())
                        .createdAt(c.getCreatedAt())
                        .updatedAt(c.getUpdatedAt())
                        .build())
                .orElse(null);
    }
}
