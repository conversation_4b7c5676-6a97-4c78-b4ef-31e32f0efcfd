package com.backend360.campaign_service.dto.crm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContactIUDto implements Serializable {
    private String name;
    private String surname;
    private String email;
    private String phoneNumber;
    private String numberType;
    private String countryCode;
    private String timezone;
    private String primaryLanguage;
    private String preferredContactChannel;
    private String preferredContactTime;
    private String status;
    private String customField1;
    private String customField2;

    // CustomerToCampaign bilgileri
    private Long customerToCampaignId;
    private Boolean isCalled;
    private Boolean isActive;
    private String contactPhone;
    private String contactEmail;
    private String contactName;
    private String contactSurname;
    private LocalDateTime customerToCampaignCreatedAt;
    private LocalDateTime customerToCampaignUpdatedAt;
    private Long campaignId;
    private String campaignName;
    private String campaignTitle;
    private String campaignDescription;
    private String campaignType;
    private String brandName;
    // FormToCampaign bilgileri
    private Long formToCampaignId;
    private String ipAddress;
    private LocalDateTime formToCampaignCreatedAt;
    private LocalDateTime formToCampaignUpdatedAt;
}