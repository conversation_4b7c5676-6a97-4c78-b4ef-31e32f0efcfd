package com.backend360.campaign_service.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomerToCampaignIUDto implements Serializable {
    private Long campaignId;
    private Long customerId;
    private Boolean isCalled;
    private Boolean isActive;
    private String contactPhone;
    private String contactEmail;
    private String contactName;
    private String contactSurname;
}
