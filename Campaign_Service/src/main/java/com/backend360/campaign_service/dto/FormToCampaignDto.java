package com.backend360.campaign_service.dto;

import com.backend360.campaign_service.model.Campaign;
import com.backend360.campaign_service.model.Form;
import com.backend360.campaign_service.model.FormToCampaign;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FormToCampaignDto implements Serializable {
    private Long id;
    private Form form;
    private Campaign campaign;
    private String ipAddress;
    private Boolean isActive;
    private String contactPhone;
    private String contactEmail;
    private String contactName;
    private String contactSurname;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime updatedAt;

    public static FormToCampaignDto entityToDto(FormToCampaign formToCampaign) {
        if (formToCampaign == null) return null;
        return FormToCampaignDto.builder()
                .id(formToCampaign.getId())
                .form(formToCampaign.getForm())
                .campaign(formToCampaign.getCampaign())
                .ipAddress(formToCampaign.getIpAddress())
                .isActive(formToCampaign.getIsActive())
                .contactPhone(formToCampaign.getContactPhone())
                .contactEmail(formToCampaign.getContactEmail())
                .contactName(formToCampaign.getContactName())
                .contactSurname(formToCampaign.getContactSurname())
                .createdAt(formToCampaign.getCreatedAt())
                .updatedAt(formToCampaign.getUpdatedAt())
                .build();
    }
}
