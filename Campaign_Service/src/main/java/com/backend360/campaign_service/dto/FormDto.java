package com.backend360.campaign_service.dto;

import com.backend360.campaign_service.model.Form;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FormDto implements Serializable {
    private Long id;
    private String name;
    private String surname;
    private String email;
    private String phoneNumber;
    private String country;
    private String city;
    private String town;
    private Boolean isActive;
    private String gender;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy")
    private Date birthday;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime updatedAt;

    public static FormDto entityToDto(Form form) {
        if (form == null) return null;
        return FormDto.builder()
                .id(form.getId())
                .name(form.getName())
                .surname(form.getSurname())
                .email(form.getEmail())
                .phoneNumber(form.getPhoneNumber())
                .country(form.getCountry())
                .city(form.getCity())
                .town(form.getTown())
                .isActive(form.getIsActive())
                .gender(form.getGender())
                .birthday(form.getBirthday())
                .createdAt(form.getCreatedAt())
                .updatedAt(form.getUpdatedAt())
                .build();
    }
}
