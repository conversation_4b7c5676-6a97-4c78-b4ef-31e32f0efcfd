package com.backend360.campaign_service.dto;

import com.backend360.campaign_service.model.Brand;
import com.backend360.campaign_service.model.BrandToCampaign;
import com.backend360.campaign_service.model.Campaign;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BrandToCampaignDto implements Serializable {
    private Long id;
    private Campaign campaign;
    private Brand brand;
    private Boolean isActive;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime updatedAt;

    public static BrandToCampaignDto entityToDto(BrandToCampaign brandToCampaign) {
        if (brandToCampaign == null) return null;
        return BrandToCampaignDto.builder()
                .id(brandToCampaign.getId())
                .campaign(brandToCampaign.getCampaign())
                .brand(brandToCampaign.getBrand())
                .isActive(brandToCampaign.getIsActive())
                .createdAt(brandToCampaign.getCreatedAt())
                .updatedAt(brandToCampaign.getUpdatedAt())
                .build();
    }
}
