package com.backend360.campaign_service.dto;

import com.backend360.campaign_service.model.CampaignDetail;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CampaignDetailDto implements Serializable {
    Long id;
    Map<String, Object> details;
    Boolean isActive;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    LocalDateTime createdAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    LocalDateTime updatedAt;

    public static CampaignDetailDto entityToDto(CampaignDetail campaignDetail) {
        if (campaignDetail == null) return null;
        return CampaignDetailDto.builder()
                .id(campaignDetail.getId())
                .details(campaignDetail.getDetails())
                .isActive(campaignDetail.getIsActive())
                .createdAt(campaignDetail.getCreatedAt())
                .updatedAt(campaignDetail.getUpdatedAt())
                .build();
    }
}
