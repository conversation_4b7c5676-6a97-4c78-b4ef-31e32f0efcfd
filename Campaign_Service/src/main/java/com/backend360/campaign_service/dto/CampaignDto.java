package com.backend360.campaign_service.dto;

import com.backend360.campaign_service.model.Campaign;
import com.backend360.campaign_service.model.Category;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CampaignDto implements Serializable {
    private Long id;
    private Category category;
    private String name;
    private String title;
    private String description;
    private Map<String, Object> details;
    private Boolean isActive;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime startDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime endDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime updatedAt;

    private String title2;
    private String description2;
    private String title3;
    private String description3;

    public static CampaignDto entityToDto(Campaign campaign) {
        if (campaign == null) return null;
        return CampaignDto.builder()
                .id(campaign.getId())
                .category(campaign.getCategory())
                .name(campaign.getName())
                .title(campaign.getTitle())
                .description(campaign.getDescription())
                .details(campaign.getDetails())
                .isActive(campaign.getIsActive())
                .startDate(campaign.getStartDate())
                .endDate(campaign.getEndDate())
                .createdAt(campaign.getCreatedAt())
                .updatedAt(campaign.getUpdatedAt())
                .title2(campaign.getTitle2())
                .description2(campaign.getDescription2())
                .title3(campaign.getTitle3())
                .description3(campaign.getDescription3())
                .build();
    }
}
