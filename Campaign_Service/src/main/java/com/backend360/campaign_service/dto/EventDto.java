package com.backend360.campaign_service.dto;

import com.backend360.campaign_service.model.Event;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Optional;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EventDto implements Serializable {
    private Long id;
    private String userId;
    private Long targetId;
    private String targetType;
    private String details;
    private Long brandId;
    private Long campaignId;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime createdAt;

    public static EventDto entityToDto(Event event) {
        if (event == null) return null;
        return EventDto.builder()
                .id(event.getId())
                .userId(event.getUserId())
                .targetId(event.getTargetId())
                .targetType(event.getTargetType())
                .details(event.getDetails())
                .brandId(event.getBrand() != null ? event.getBrand().getId() : null)
                .campaignId(event.getCampaign() != null ? event.getCampaign().getId() : null)
                .createdAt(event.getCreatedAt())
                .build();
    }

    public static EventDto fromEntity(Event event) {
        return Optional.ofNullable(event)
                .map(e -> EventDto.builder()
                        .id(e.getId())
                        .userId(e.getUserId())
                        .targetId(e.getTargetId())
                        .targetType(e.getTargetType())
                        .details(e.getDetails())
                        .brandId(e.getBrand() != null ? e.getBrand().getId() : null)
                        .campaignId(e.getCampaign() != null ? e.getCampaign().getId() : null)
                        .createdAt(e.getCreatedAt())
                        .build())
                .orElse(null);
    }
} 