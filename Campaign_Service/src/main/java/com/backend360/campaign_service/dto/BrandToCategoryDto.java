package com.backend360.campaign_service.dto;

import com.backend360.campaign_service.model.Brand;
import com.backend360.campaign_service.model.BrandToCategory;
import com.backend360.campaign_service.model.Category;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BrandToCategoryDto implements Serializable {
    private Long id;
    private Category category;
    private Brand brand;
    private Boolean isActive;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime updatedAt;

    public static BrandToCategoryDto entityToDto(BrandToCategory brandToCategory) {
        if (brandToCategory == null) return null;
        return BrandToCategoryDto.builder()
                .id(brandToCategory.getId())
                .category(brandToCategory.getCategory())
                .brand(brandToCategory.getBrandId())
                .isActive(brandToCategory.getIsActive())
                .createdAt(brandToCategory.getCreatedAt())
                .updatedAt(brandToCategory.getUpdatedAt())
                .build();
    }
}
