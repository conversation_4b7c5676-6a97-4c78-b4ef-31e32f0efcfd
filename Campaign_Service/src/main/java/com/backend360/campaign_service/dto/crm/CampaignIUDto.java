package com.backend360.campaign_service.dto.crm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CampaignIUDto implements Serializable {
    private String name;
    private String description;
    private String status;
    private String campaignType;
    private LocalDateTime startDate;
    private LocalDateTime endDate;
    private Map<String, Object> rules;
    private Long agentId;
    private Long agentAppId;
}