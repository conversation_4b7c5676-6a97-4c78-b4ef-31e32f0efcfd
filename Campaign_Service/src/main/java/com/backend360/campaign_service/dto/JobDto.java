package com.backend360.campaign_service.dto;

import com.backend360.campaign_service.model.Job;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JobDto implements Serializable {
    private Long id;
    private String description;
    private Boolean isActive;

    public static JobDto entityToDto(Job job) {
        if (job == null) return null;
        return JobDto.builder()
                .id(job.getId())
                .description(job.getDescription())
                .isActive(job.isActive())
                .build();
    }
}
