package com.backend360.campaign_service.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CampaignIUDto implements Serializable {
    private Long categoryId;
    private String name;
    private String title;
    private String description;
    private String title2;
    private String description2;
    private String title3;
    private String description3;
    private Map<String, Object> details;
    private Boolean isActive;
    private LocalDateTime startDate;
    private LocalDateTime endDate;
}
