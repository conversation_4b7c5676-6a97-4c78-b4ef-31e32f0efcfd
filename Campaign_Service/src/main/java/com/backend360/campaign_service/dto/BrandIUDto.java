package com.backend360.campaign_service.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BrandIUDto implements Serializable {
    private String name;
    private Integer countryCode;
    private String brandUrl;
    private String imagePath;
    private Boolean isActive;
    private transient MultipartFile image;
}
