package com.backend360.campaign_service.dto.crm;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContactDto implements Serializable {
    private UUID uuid;
    private String name;
    private String surname;
    private String email;
    private String phoneNumber;
    private String numberType;
    private String countryCode;
    private String timezone;
    private String primaryLanguage;
    private String preferredContactChannel;
    private String preferredContactTime;
    private String status;
    private String customField1;
    private String customField2;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime createdAt;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime updatedAt;
    private Set<Object> tags; // TagDto yerine Object kullanıyoruz çünkü TagDto'yu import etmek istemiyoruz
}