package com.backend360.campaign_service.dto;

import com.backend360.campaign_service.model.Campaign;
import com.backend360.campaign_service.model.Customer;
import com.backend360.campaign_service.model.CustomerToCampaign;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerToCampaignDto implements Serializable {
    private Long id;
    private Customer customer;
    private Campaign campaign;
    private Boolean isCalled;
    private Boolean isActive;
    private String contactPhone;
    private String contactEmail;
    private String contactName;
    private String contactSurname;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime updatedAt;

    public static CustomerToCampaignDto entityToDto(CustomerToCampaign customerToCampaign) {
        if (customerToCampaign == null) return null;
        return CustomerToCampaignDto.builder()
                .id(customerToCampaign.getId())
                .campaign(customerToCampaign.getCampaign())
                .customer(customerToCampaign.getCustomer())
                .isCalled(customerToCampaign.getIsCalled())
                .isActive(customerToCampaign.getIsActive())
                .createdAt(customerToCampaign.getCreatedAt())
                .updatedAt(customerToCampaign.getUpdatedAt())
                .contactPhone(customerToCampaign.getContactPhone())
                .contactEmail(customerToCampaign.getContactEmail())
                .contactName(customerToCampaign.getContactName())
                .contactSurname(customerToCampaign.getContactSurname())
                .build();
    }
}
