package com.backend360.campaign_service.config;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.crypto.SecretKey;
import java.io.IOException;
import java.util.Collections;

@Slf4j
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    @Value("${security.jwt.secret-key}")
    private String secretKey;

    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                  HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        final String authorizationHeader = request.getHeader("Authorization");
        
        String username = null;
        String jwt = null;
        String role = null;

        // Bearer token kontrolü (Authorization header'dan)
        if (authorizationHeader != null && authorizationHeader.startsWith("Bearer ")) {
            jwt = authorizationHeader.substring(7);
            try {
                Claims claims = extractAllClaims(jwt);
                username = claims.getSubject();
                role = claims.get("roles", String.class);
                log.debug("JWT token found in Authorization header for user: {} with role: {}", username, role);
            } catch (Exception e) {
                log.error("JWT token parsing error from Authorization header: {}", e.getMessage());
            }
        }

        // Eğer Authorization header'da token yoksa, cookie'lerden kontrol et
        if (jwt == null && request.getCookies() != null) {
            log.info("Campaign Service - Checking cookies for JWT token. Available cookies:");
            for (Cookie cookie : request.getCookies()) {
                log.info("Campaign Service - Cookie: {} = {}", cookie.getName(), cookie.getValue().substring(0, Math.min(20, cookie.getValue().length())) + "...");
                if ("accessToken".equals(cookie.getName())) {
                    jwt = cookie.getValue();
                    try {
                        Claims claims = extractAllClaims(jwt);
                        username = claims.getSubject();
                        role = claims.get("roles", String.class);
                        log.info("Campaign Service - JWT token found in cookie for user: {} with role: {}", username, role);
                    } catch (Exception e) {
                        log.error("Campaign Service - JWT token parsing error from cookie: {}", e.getMessage());
                        jwt = null; // Reset jwt if parsing fails
                    }
                    break;
                }
            }
            if (jwt == null) {
                log.warn("Campaign Service - No accessToken cookie found for path: {}", request.getRequestURI());
            }
        }

        // Token geçerli ve kullanıcı henüz authenticate edilmemişse
        if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
            log.info("Campaign Service - Validating token for user: {} with role: {} for path: {}", username, role, request.getRequestURI());
            if (validateToken(jwt)) {
                // Authentication object oluştur
                UsernamePasswordAuthenticationToken authToken =
                    new UsernamePasswordAuthenticationToken(
                        username,
                        null,
                        Collections.singletonList(new SimpleGrantedAuthority("ROLE_" + role))
                    );

                authToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authToken);

                log.info("Campaign Service - User {} authenticated successfully with role: ROLE_{} for path: {}", username, role, request.getRequestURI());
                log.info("Campaign Service - Authentication authorities: {}", authToken.getAuthorities());
            } else {
                log.error("Campaign Service - JWT token validation FAILED for user: {} on path: {}", username, request.getRequestURI());
            }
        } else if (username == null) {
            log.error("Campaign Service - Username is NULL from JWT token on path: {}", request.getRequestURI());
        } else {
            log.debug("Campaign Service - User already authenticated: {}", SecurityContextHolder.getContext().getAuthentication().getName());
        }

        filterChain.doFilter(request, response);
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
        String path = request.getRequestURI();
        String method = request.getMethod();
        log.debug("JwtAuthenticationFilter.shouldNotFilter - method: {}, path: '{}'", method, path);
        
        boolean isPost = "POST".equals(method);
        boolean startsWithFormToCampaign = path.startsWith("/form-to-campaign");
        boolean startsWithCustomerToCampaign = path.startsWith("/customer-to-campaign");
        boolean startsWithCampaignServiceFormToCampaign = path.startsWith("/Campaign_Service/form-to-campaign");
        boolean startsWithCampaignServiceCustomerToCampaign = path.startsWith("/Campaign_Service/customer-to-campaign");
        
        boolean shouldSkip = path.startsWith("/actuator/") ||
               path.startsWith("/swagger-ui/") ||
               path.startsWith("/v3/api-docs") ||
               (isPost && (startsWithFormToCampaign || startsWithCustomerToCampaign || startsWithCampaignServiceFormToCampaign || startsWithCampaignServiceCustomerToCampaign));
        
        log.debug("JwtAuthenticationFilter.shouldNotFilter - isPost: {}, startsWithFormToCampaign: {}, startsWithCustomerToCampaign: {}, startsWithCampaignServiceFormToCampaign: {}, startsWithCampaignServiceCustomerToCampaign: {}, shouldSkip: {}", 
            isPost, startsWithFormToCampaign, startsWithCustomerToCampaign, startsWithCampaignServiceFormToCampaign, startsWithCampaignServiceCustomerToCampaign, shouldSkip);
        
        return shouldSkip;
    }

    private Claims extractAllClaims(String token) {
        try {
            // Auth Service ile aynı yöntem: Base64URL decode
            log.debug("Campaign Service - Using JWT secret key (first 20 chars): {}", secretKey.substring(0, Math.min(20, secretKey.length())));
            byte[] keyBytes = io.jsonwebtoken.io.Decoders.BASE64URL.decode(secretKey);
            SecretKey key = Keys.hmacShaKeyFor(keyBytes);
            return Jwts.parser()
                    .verifyWith(key)
                    .build()
                    .parseSignedClaims(token)
                    .getPayload();
        } catch (Exception e) {
            log.error("Campaign Service - JWT parsing failed. Secret key length: {}, Error: {}", secretKey.length(), e.getMessage());
            throw e;
        }
    }

    private boolean validateToken(String token) {
        try {
            extractAllClaims(token);
            return true;
        } catch (Exception e) {
            log.error("JWT validation error: {}", e.getMessage());
            return false;
        }
    }
}
