package com.backend360.campaign_service.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;
import java.util.List;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity
@RequiredArgsConstructor
public class SecurityConfig {

    private final JwtAuthenticationFilter jwtAuthenticationFilter;

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .csrf(AbstractHttpConfigurer::disable)
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(auth -> auth
                // Public endpoints - Frontend public sayfaları için
                .requestMatchers(HttpMethod.GET, "/campaign/**").permitAll()
                .requestMatchers(HttpMethod.GET, "/category/**").permitAll()
                .requestMatchers(HttpMethod.GET, "/brand/**").permitAll()
                .requestMatchers(HttpMethod.GET, "/brand-to-campaign/**").permitAll()
                .requestMatchers(HttpMethod.GET, "/campaign-detail/**").permitAll()
                .requestMatchers(HttpMethod.GET, "/campaign-url/**").permitAll()
                .requestMatchers(HttpMethod.GET, "/campaign-image/**").permitAll()
                .requestMatchers(HttpMethod.GET, "/events/**").permitAll()
                .requestMatchers(HttpMethod.POST, "/events").permitAll() // Event tracking için
                .requestMatchers(HttpMethod.GET, "/job/**").permitAll()

                // Public form submissions - Yeni güvenli endpoint'ler
                .requestMatchers("/public/form-submission/**").permitAll()

                // Actuator ve docs
                .requestMatchers("/actuator/**").hasAnyRole("ADMIN", "CRM_ADMIN")
                .requestMatchers("/swagger-ui/**").hasAnyRole("ADMIN", "CRM_ADMIN")
                .requestMatchers("/v3/api-docs/**").hasAnyRole("ADMIN", "CRM_ADMIN")

                // CRM Admin endpoints
                .requestMatchers("/crm-admin/**").hasAnyRole("ADMIN", "CRM_ADMIN")

                // Admin-only endpoints - Form, form-to-campaign, customer-to-campaign listeleri
                .requestMatchers(HttpMethod.GET, "/form/**").hasAnyRole("ADMIN", "CRM_ADMIN")
                .requestMatchers(HttpMethod.GET, "/form-to-campaign/**").hasAnyRole("ADMIN", "CRM_ADMIN")
                .requestMatchers(HttpMethod.GET, "/customer-to-campaign/**").hasAnyRole("ADMIN", "CRM_ADMIN")
        
               

                // Public okuma endpoint'leri - Kampanya görüntüleme için
                .requestMatchers(HttpMethod.GET, "/campaign/**").permitAll()
                .requestMatchers(HttpMethod.GET, "/brand/**").permitAll()
                .requestMatchers(HttpMethod.GET, "/category/**").permitAll()
                .requestMatchers(HttpMethod.GET, "/campaign-detail/**").permitAll()
                .requestMatchers(HttpMethod.GET, "/campaign-url/**").permitAll()
                .requestMatchers(HttpMethod.GET, "/brand-to-campaign/**").permitAll()

                // Public resim görüntüleme endpoint'leri
                .requestMatchers(HttpMethod.GET, "/campaign-image/showcase/**").permitAll()
                .requestMatchers(HttpMethod.GET, "/campaign-image/details/**").permitAll()
                .requestMatchers(HttpMethod.GET, "/campaign-image/images/**").permitAll()

                // Yazma/güncelleme/silme işlemleri sadece admin
                .requestMatchers(HttpMethod.POST, "/campaign/**").hasAnyRole("ADMIN", "CRM_ADMIN")
                .requestMatchers(HttpMethod.PUT, "/campaign/**").hasAnyRole("ADMIN", "CRM_ADMIN")
                .requestMatchers(HttpMethod.DELETE, "/campaign/**").hasAnyRole("ADMIN", "CRM_ADMIN")

                .requestMatchers(HttpMethod.POST, "/brand/**").hasAnyRole("ADMIN", "CRM_ADMIN")
                .requestMatchers(HttpMethod.PUT, "/brand/**").hasAnyRole("ADMIN", "CRM_ADMIN")
                .requestMatchers(HttpMethod.DELETE, "/brand/**").hasAnyRole("ADMIN", "CRM_ADMIN")

                .requestMatchers(HttpMethod.POST, "/category/**").hasAnyRole("ADMIN", "CRM_ADMIN")
                .requestMatchers(HttpMethod.PUT, "/category/**").hasAnyRole("ADMIN", "CRM_ADMIN")
                .requestMatchers(HttpMethod.DELETE, "/category/**").hasAnyRole("ADMIN", "CRM_ADMIN")

                // Resim yükleme/güncelleme/silme sadece admin
                .requestMatchers(HttpMethod.POST, "/campaign-image/**").hasAnyRole("ADMIN", "CRM_ADMIN")
                .requestMatchers(HttpMethod.PUT, "/campaign-image/**").hasAnyRole("ADMIN", "CRM_ADMIN")
                .requestMatchers(HttpMethod.DELETE, "/campaign-image/**").hasAnyRole("ADMIN", "CRM_ADMIN")

                // Diğer tüm istekler herkese açık
                .anyRequest().permitAll()
            )
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
            
        return http.build();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(List.of("*"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);
        configuration.setExposedHeaders(Arrays.asList("Authorization"));

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
