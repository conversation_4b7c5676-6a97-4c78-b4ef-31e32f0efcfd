package com.backend360.campaign_service.repository;

import com.backend360.campaign_service.model.Form;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface FormRepository extends JpaRepository<Form, Long> {

    boolean existsByEmail(String email);

    // Public form submission için yeni metodlar
    boolean existsByEmailAndIsActive(String email, Boolean isActive);
    Optional<Form> findByEmailAndIsActive(String email, Boolean isActive);
}
