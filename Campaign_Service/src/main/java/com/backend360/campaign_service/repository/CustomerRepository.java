package com.backend360.campaign_service.repository;

import com.backend360.campaign_service.model.Customer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

import java.util.Optional;

@Repository
public interface CustomerRepository extends JpaRepository<Customer, Long> {

    boolean existsByEmail(String email);
    
    boolean existsByUsername(String username);
    
    List<Customer> findByRemindMeTrueAndIsActiveTrue();

    Optional<Customer> findByUsername(String username);
    
    Optional<Customer> findByEmail(String email);
}
