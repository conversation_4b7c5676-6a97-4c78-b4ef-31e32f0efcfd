package com.backend360.campaign_service.repository;

import com.backend360.campaign_service.model.FormToCampaign;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface FormToCampaignRepository extends JpaRepository<FormToCampaign, Long> {

    boolean existsByCampaignIdAndFormId(Long campaignId, Long formId);
    FormToCampaign findByForm_Id(Long formId);

    // Public form submission için yeni metodlar
    boolean existsByForm_IdAndCampaign_IdAndIsActive(Long formId, Long campaignId, Boolean isActive);
    Optional<FormToCampaign> findByForm_IdAndCampaign_IdAndIsActive(Long formId, Long campaignId, Boolean isActive);
}
