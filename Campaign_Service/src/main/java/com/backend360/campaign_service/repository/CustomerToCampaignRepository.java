package com.backend360.campaign_service.repository;

import com.backend360.campaign_service.model.CustomerToCampaign;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface CustomerToCampaignRepository extends JpaRepository<CustomerToCampaign, Long> {

    boolean existsByCampaignIdAndCustomerId(Long campaignId, Long customerId);

    // Public form submission için yeni metodlar
    boolean existsByCustomer_IdAndCampaign_IdAndIsActive(Long customerId, Long campaignId, Boolean isActive);
    Optional<CustomerToCampaign> findByCustomer_IdAndCampaign_IdAndIsActive(Long customerId, Long campaignId, Boolean isActive);
}
