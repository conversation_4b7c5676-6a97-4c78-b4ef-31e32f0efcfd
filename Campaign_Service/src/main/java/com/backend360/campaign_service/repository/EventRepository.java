package com.backend360.campaign_service.repository;

import com.backend360.campaign_service.model.Event;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Repository
public interface EventRepository extends JpaRepository<Event, Long> {
    
    @Query("SELECT COUNT(e) FROM Event e WHERE e.createdAt BETWEEN :startDate AND :endDate")
    Long getClickCount(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    @Query("SELECT e.brand.id, COUNT(e) as cnt FROM Event e WHERE e.targetType = 'CAMPAIGN' AND e.brand.id IS NOT NULL GROUP BY e.brand.id ORDER BY cnt DESC")
    List<Object[]> findPopularBrands();

    @Query("SELECT e.campaign.id, COUNT(e) as cnt FROM Event e WHERE e.targetType = 'CAMPAIGN' GROUP BY e.campaign.id ORDER BY cnt DESC")
    List<Object[]> findPopularCampaigns();

    @Query("SELECT COUNT(e) FROM Event e")
    Long getClickCountAll();

    @Query("SELECT e.campaign.id, COUNT(e) as cnt FROM Event e WHERE e.userId = :userId AND e.targetType = 'CAMPAIGN' GROUP BY e.campaign.id ORDER BY cnt DESC")
    List<Object[]> findUserPopularCampaigns(@Param("userId") String userId);

    @Query("SELECT e.brand.id, COUNT(e) as cnt FROM Event e WHERE e.userId = :userId AND e.targetType = 'CAMPAIGN' AND e.brand.id IS NOT NULL GROUP BY e.brand.id ORDER BY cnt DESC")
    List<Object[]> findUserPopularBrands(@Param("userId") String userId);
} 