package com.backend360.campaign_service.repository;

import com.backend360.campaign_service.model.BrandToCampaign;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface BrandToCampaignRepository extends JpaRepository<BrandToCampaign, Long> {

    boolean existsByCampaignIdAndBrandId(Long campaignId, Long brandId);

    BrandToCampaign findFirstByCampaign_IdAndIsActiveTrue(Long campaignId);
}
