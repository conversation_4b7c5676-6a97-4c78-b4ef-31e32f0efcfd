package com.backend360.campaign_service.repository;

import com.backend360.campaign_service.model.CampaignUrl;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CampaignUrlRepository extends JpaRepository<CampaignUrl, Long> {

    boolean existsByUrl(String url);
    
    boolean existsByCampaignId(Long campaignId);
}
