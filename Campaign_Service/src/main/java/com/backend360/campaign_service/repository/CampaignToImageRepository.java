package com.backend360.campaign_service.repository;

import com.backend360.campaign_service.model.CampaignToImage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CampaignToImageRepository extends JpaRepository<CampaignToImage, Long> {

    // Tüm aktif kayıtlar
    List<CampaignToImage> findAllByIsActiveTrue();

    // Kampanyaya ait tüm aktif görseller
    List<CampaignToImage> findAllByCampaign_IdAndIsActiveTrue(Long campaignId);

    // Kampanyanın aktif showcase görseli (❗ Sadece 1 olmalı)
    Optional<CampaignToImage> findByCampaign_IdAndIsShowcaseTrueAndIsActiveTrue(Long campaignId);

    // Kampanyanın tüm aktif detay görselleri
    List<CampaignToImage> findAllByCampaign_IdAndIsShowcaseFalseAndIsActiveTrue(Long campaignId);

    // Aynı campaign için aynı isimde görsel var mı?
    boolean existsByCampaignIdAndImageName(Long campaignId, String imageName);

}
