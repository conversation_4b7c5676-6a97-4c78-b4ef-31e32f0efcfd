package com.backend360.campaign_service.repository;

import com.backend360.campaign_service.model.Category;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CategoryRepository extends JpaRepository<Category, Long> {

    boolean existsByName(String name);
    
    boolean existsByParentCategoryId(Long parentCategoryId);
}
