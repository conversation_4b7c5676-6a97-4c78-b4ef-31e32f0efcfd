package com.backend360.campaign_service.repository;

import com.backend360.campaign_service.model.BrandToCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface BrandToCategoryRepository extends JpaRepository<BrandToCategory, Long> {

    boolean existsByCategoryIdAndBrandIdId(Long categoryId, Long brandId);
}
