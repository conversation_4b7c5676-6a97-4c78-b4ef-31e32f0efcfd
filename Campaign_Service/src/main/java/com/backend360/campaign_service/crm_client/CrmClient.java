package com.backend360.campaign_service.crm_client;

import com.backend360.campaign_service.dto.crm.ContactDto;
import com.backend360.campaign_service.dto.crm.ContactIUDto;
import com.backend360.campaign_service.dto.crm.CampaignDto;
import com.backend360.campaign_service.dto.crm.CampaignIUDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@FeignClient(name = "crmClient", url = "${crm.api.url}")
public interface CrmClient {

    @PostMapping("/api/v1/contacts")
    ResponseEntity<ContactDto> createContact(
            @RequestBody ContactIUDto contact,
            @RequestHeader("Authorization") String token
    );

    @GetMapping("/api/v1/contacts")
    ResponseEntity<Map<String, Object>> getAllContacts(
            @RequestHeader("Authorization") String token,
            @RequestParam(value = "size", defaultValue = "1000") int size
    );

    @GetMapping("/api/v1/contacts/{id}")
    ResponseEntity<ContactDto> getContactById(
            @PathVariable("id") UUID id,
            @RequestHeader("Authorization") String token
    );

    @PostMapping("/api/v1/campaigns")
    ResponseEntity<CampaignDto> createCampaign(
            @RequestBody CampaignIUDto campaign,
            @RequestHeader("Authorization") String token
    );

    // Webhook Events
    @GetMapping("/api/v1/webhook-events")
    ResponseEntity<List<Map<String, Object>>> getAllWebhookEvents(
            @RequestHeader("Authorization") String token
    );

    @GetMapping("/api/v1/webhook-events/{id}")
    ResponseEntity<Map<String, Object>> getWebhookEventById(
            @PathVariable("id") UUID id,
            @RequestHeader("Authorization") String token
    );

    @GetMapping("/api/v1/webhook-events/by-phone/{phoneNumber}")
    ResponseEntity<List<Map<String, Object>>> getWebhookEventsByPhoneNumber(
            @PathVariable("phoneNumber") String phoneNumber,
            @RequestHeader("Authorization") String token
    );

    @GetMapping("/api/v1/campaigns")
    ResponseEntity<List<Map<String, Object>>> getAllCampaigns(
            @RequestHeader("Authorization") String token
    );

    @GetMapping("/api/v1/campaigns/{uuid}")
    ResponseEntity<Map<String, Object>> getCampaignByUuid(
            @PathVariable("uuid") UUID uuid,
            @RequestHeader("Authorization") String token
    );

    @GetMapping("/api/v1/call-logs")
    ResponseEntity<List<Map<String, Object>>> getAllCalls(
            @RequestHeader("Authorization") String token
    );

    @PostMapping("/api/v1/call-logs")
    ResponseEntity<Map<String, Object>> createCall(
            @RequestBody Map<String, Object> call,
            @RequestHeader("Authorization") String token
    );
}