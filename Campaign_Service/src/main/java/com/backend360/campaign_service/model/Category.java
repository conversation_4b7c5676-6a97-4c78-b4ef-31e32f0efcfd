package com.backend360.campaign_service.model;

import jakarta.persistence.*;
import lombok.*;
import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "categories")
public class Category {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "\"isActive\"")
    private Boolean isActive;

    @ManyToOne
    @JoinColumn(name = "campaign_detail_id")
    private CampaignDetail campaignDetail;

    @Column(name = "parent_category_id")
    private Long parentCategoryId;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}
