package com.backend360.campaign_service.manager;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

@FeignClient(
        name = "image-service",
        url = "http://image-service:9002/Image_Service/image",
        configuration = FeignMultipartSupportConfig.class
)
public interface ImageFeignManager {

    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ResponseEntity<Map<String, Object>> uploadImage(@RequestParam("path") String path,
                                                    @RequestPart("image") MultipartFile file) throws IOException;


    @GetMapping(value = "/{fileName}", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    ResponseEntity<byte[]> downloadImage(@PathVariable("fileName") String fileName);

    @DeleteMapping("/{fileName}")
    void deleteImage(@PathVariable("fileName") String fileName);

}
