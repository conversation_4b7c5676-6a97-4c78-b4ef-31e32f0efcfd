package com.backend360.campaign_service.manager;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.backend360.campaign_service.dto.EmailRequestDto;

@FeignClient(name = "mail-service", url = "http://mail-service:9003/Mail_Service/email")
public interface MailFeignManager {

    @PostMapping("/form-campaign")
    String sendFormToCampaignEmail(@RequestBody EmailRequestDto emailRequest);

    @PostMapping("/customer-campaign")
    String sendCustomerToCampaignEmail(@RequestBody EmailRequestDto emailRequest);

    @PostMapping("/form-success")
    String sendFormSuccessEmail(@RequestBody EmailRequestDto emailRequest);

    @PostMapping("/new-campaign-notification")
    String sendNewCampaignNotificationEmail(@RequestBody EmailRequestDto emailRequest);
} 