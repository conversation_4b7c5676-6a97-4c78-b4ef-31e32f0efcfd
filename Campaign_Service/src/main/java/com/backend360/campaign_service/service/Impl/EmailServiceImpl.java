package com.backend360.campaign_service.service.Impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.backend360.campaign_service.service.IFormService;
import com.backend360.campaign_service.repository.CustomerToCampaignRepository;
import com.backend360.campaign_service.repository.CustomerRepository;
import com.backend360.campaign_service.repository.CampaignRepository;
import com.backend360.campaign_service.model.Customer;
import com.backend360.campaign_service.model.Campaign;
import com.backend360.campaign_service.manager.MailFeignManager;
import java.util.List;
import com.backend360.campaign_service.dto.EmailRequestDto;

@Service
public class EmailServiceImpl {
    private static final Logger logger = LoggerFactory.getLogger(EmailServiceImpl.class);
    
    private final MailFeignManager mailFeignManager;
    private final IFormService formService;
    private final CustomerToCampaignRepository customerToCampaignRepository;
    private final CustomerRepository customerRepository;
    private final CampaignRepository campaignRepository;

    @Autowired
    public EmailServiceImpl(MailFeignManager mailFeignManager, IFormService formService,
                           CustomerToCampaignRepository customerToCampaignRepository,
                           CustomerRepository customerRepository,
                           CampaignRepository campaignRepository) {
        this.mailFeignManager = mailFeignManager;
        this.formService = formService;
        this.customerToCampaignRepository = customerToCampaignRepository;
        this.customerRepository = customerRepository;
        this.campaignRepository = campaignRepository;
    }

    public void sendFormSuccessMail(String to, String fullName, String campaignName, String campaignDescription) {
        // Email zaten kayıtlı mı kontrol et
        if (formService.existsByEmail(to)) {
            logger.warn("Aynı email ile tekrar kayıt olmaya çalışıldı, email gönderilmeyecek: {}", to);
            return;
        }
        try {
            logger.info("Email gönderimi başlıyor - Alıcı: {}", to);
            
            EmailRequestDto emailRequest = new EmailRequestDto(
                to, fullName, campaignName, campaignDescription, "FORM_SUCCESS"
            );

            String response = mailFeignManager.sendFormSuccessEmail(emailRequest);
            logger.info("Email başarıyla gönderildi! Response: {}", response);
            
        } catch (Exception e) {
            logger.error("Email gönderimi sırasında hata oluştu (Alıcı: {}): {}", to, e.getMessage(), e);
            // Email hatası uygulamanın çalışmasını durdurmasın
        }
    }
    
    public void sendCustomerToCampaignMail(Customer customer, Campaign campaign) {
        try {
            String to = customer.getEmail();
            String fullName = customer.getName() + " " + customer.getSurname();
            String campaignName = campaign.getName() != null ? campaign.getName() : "Kampanya";
            String campaignDescription = campaign.getDescription() != null ? campaign.getDescription() : "Kampanya açıklaması";
            
            logger.info("CustomerToCampaign email gönderimi başlıyor - Alıcı: {}", to);
            
            EmailRequestDto emailRequest = new EmailRequestDto(
                to, fullName, campaignName, campaignDescription, "CUSTOMER_TO_CAMPAIGN"
            );

            String response = mailFeignManager.sendCustomerToCampaignEmail(emailRequest);
            logger.info("CustomerToCampaign email başarıyla gönderildi! Response: {}", response);
            
        } catch (Exception e) {
            logger.error("CustomerToCampaign email gönderimi sırasında hata oluştu (customerId: {}, campaignId: {}): {}", customer.getId(), campaign.getId(), e.getMessage(), e);
        }
    }
    
    public void sendFormToCampaignMail(com.backend360.campaign_service.model.Form form, com.backend360.campaign_service.model.Campaign campaign) {
        try {
            String to = form.getEmail();
            String fullName = form.getName() + " " + form.getSurname();
            String campaignName = campaign.getName() != null ? campaign.getName() : "Kampanya";
            String campaignDescription = campaign.getDescription() != null ? campaign.getDescription() : "Kampanya açıklaması";
            
            logger.info("FormToCampaign email gönderimi başlıyor - Alıcı: {}", to);
            
            EmailRequestDto emailRequest = new EmailRequestDto(
                to, fullName, campaignName, campaignDescription, "FORM_TO_CAMPAIGN"
            );

            String response = mailFeignManager.sendFormToCampaignEmail(emailRequest);
            logger.info("FormToCampaign email başarıyla gönderildi! Response: {}", response);
            
        } catch (Exception e) {
            logger.error("FormToCampaign email gönderimi sırasında hata oluştu (formId: {}, campaignId: {}): {}", form.getId(), campaign.getId(), e.getMessage(), e);
        }
    }

    public void sendNewCampaignNotificationToRemindMeCustomers(Campaign campaign) {
        try {
            // remindMe = true olan tüm müşterileri bul
            List<Customer> remindMeCustomers = customerRepository.findByRemindMeTrueAndIsActiveTrue();
            
            logger.info("Yeni kampanya bildirimi gönderiliyor. Toplam {} müşteriye gönderilecek.", remindMeCustomers.size());
            
            for (Customer customer : remindMeCustomers) {
                try {
                    String to = customer.getEmail();
                    String fullName = customer.getName() + " " + customer.getSurname();
                    String campaignName = campaign.getName() != null ? campaign.getName() : "Kampanya";
                    String campaignDescription = campaign.getDescription() != null ? campaign.getDescription() : "Kampanya açıklaması";
                    
                    logger.info("Yeni kampanya bildirimi gönderiliyor - Alıcı: {}", to);
                    
                    EmailRequestDto emailRequest = new EmailRequestDto(
                        to, fullName, campaignName, campaignDescription, "NEW_CAMPAIGN_NOTIFICATION"
                    );

                    String response = mailFeignManager.sendNewCampaignNotificationEmail(emailRequest);
                    logger.info("Yeni kampanya bildirimi başarıyla gönderildi! Alıcı: {}, Response: {}", to, response);
                    
                } catch (Exception e) {
                    logger.error("Yeni kampanya bildirimi gönderimi sırasında hata oluştu (customerId: {}): {}", customer.getId(), e.getMessage(), e);
                }
            }
            
        } catch (Exception e) {
            logger.error("Yeni kampanya bildirimi gönderimi sırasında genel hata oluştu: {}", e.getMessage(), e);
        }
    }
} 