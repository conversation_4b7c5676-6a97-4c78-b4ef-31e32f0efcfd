package com.backend360.campaign_service.service.Impl;

import com.backend360.campaign_service.dto.BrandToCampaignDto;
import com.backend360.campaign_service.dto.BrandToCampaignIUDto;
import com.backend360.campaign_service.exception.AlreadyExistsException;
import com.backend360.campaign_service.exception.BadRequestException;
import com.backend360.campaign_service.exception.CustomNotFoundException;
import com.backend360.campaign_service.model.Brand;
import com.backend360.campaign_service.model.BrandToCampaign;
import com.backend360.campaign_service.model.Campaign;
import com.backend360.campaign_service.repository.BrandRepository;
import com.backend360.campaign_service.repository.BrandToCampaignRepository;
import com.backend360.campaign_service.repository.CampaignRepository;
import com.backend360.campaign_service.service.IBrandToCampaignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BrandToCampaignServiceImpl implements IBrandToCampaignService {
    @Autowired
    private BrandToCampaignRepository brandToCampaignRepository;

    @Autowired
    private CampaignRepository campaignRepository;

    @Autowired
    private BrandRepository brandRepository;

    @Override
    public BrandToCampaignDto save(BrandToCampaignIUDto brandToCampaignIUDto) {
        log.info("Marka-Kampanya ilişkisi kaydetme isteği başlatıldı. Kampanya ID: {}, Marka ID: {}", 
                brandToCampaignIUDto.getCampaignId(), brandToCampaignIUDto.getBrandId());
        
        if (brandToCampaignIUDto.getCampaignId() == null) {
            log.warn("Kampanya ID boş gönderildi");
            throw new BadRequestException("Kampanya ID boş olamaz.");
        }

        if (brandToCampaignIUDto.getBrandId() == null) {
            log.warn("Marka ID boş gönderildi");
            throw new BadRequestException("Marka ID boş olamaz.");
        }

        Campaign campaign = campaignRepository.findById(brandToCampaignIUDto.getCampaignId())
                .orElseThrow(() -> {
                    log.warn("Kampanya bulunamadı. ID: {}", brandToCampaignIUDto.getCampaignId());
                    return new CustomNotFoundException("Kampanya ID " + brandToCampaignIUDto.getCampaignId() + " bulunamadı");
                });

        Brand brand = brandRepository.findById(brandToCampaignIUDto.getBrandId())
                .orElseThrow(() -> {
                    log.warn("Marka bulunamadı. ID: {}", brandToCampaignIUDto.getBrandId());
                    return new CustomNotFoundException("Marka ID " + brandToCampaignIUDto.getBrandId() + " bulunamadı");
                });

        // Aynı marka ve kampanya kombinasyonunun zaten var olup olmadığını kontrol et
        if (brandToCampaignRepository.existsByCampaignIdAndBrandId(brandToCampaignIUDto.getCampaignId(), brandToCampaignIUDto.getBrandId())) {
            log.warn("Mevcut marka-kampanya kombinasyonu ile kayıt girişimi. Kampanya ID: {}, Marka ID: {}", 
                    brandToCampaignIUDto.getCampaignId(), brandToCampaignIUDto.getBrandId());
            throw new AlreadyExistsException("Bu marka ve kampanya kombinasyonu zaten kayıtlı.");
        }

        BrandToCampaign brandToCampaign = BrandToCampaign.builder()
                .campaign(campaign)
                .brand(brand)
                .isActive(brandToCampaignIUDto.getIsActive())
                .createdAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")))
                .updatedAt(null)
                .build();

        BrandToCampaign savedBrandToCampaign = brandToCampaignRepository.save(brandToCampaign);
        log.info("Marka-Kampanya ilişkisi başarıyla kaydedildi. ID: {}", savedBrandToCampaign.getId());
        
        return BrandToCampaignDto.entityToDto(savedBrandToCampaign);
    }

    @Override
    public List<BrandToCampaignDto> findAll() {
        log.info("Tüm marka-kampanya ilişkileri listeleniyor");
        List<BrandToCampaign> brandToCampaigns = brandToCampaignRepository.findAll();
        log.debug("Toplam {} marka-kampanya ilişkisi bulundu", brandToCampaigns.size());

        return brandToCampaigns.stream()
                .map(BrandToCampaignDto::entityToDto)
                .collect(Collectors.toList());
    }

    @Override
    public BrandToCampaignDto findById(Long id) {
        log.info("Marka-Kampanya ilişkisi aranıyor. ID: {}", id);
        BrandToCampaign brandToCampaign = brandToCampaignRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Marka-Kampanya ilişkisi bulunamadı. ID: {}", id);
                    return new CustomNotFoundException("Marka-Kampanya ilişkisi ID " + id + " bulunamadı");
                });
        log.debug("Marka-Kampanya ilişkisi bulundu. ID: {}", brandToCampaign.getId());
        return BrandToCampaignDto.entityToDto(brandToCampaign);
    }

    @Override
    public BrandToCampaignDto update(Long id, BrandToCampaignIUDto brandToCampaignIUDto) {
        log.info("Marka-Kampanya ilişkisi güncelleme isteği başlatıldı. ID: {}", id);
        
        if (brandToCampaignIUDto.getCampaignId() == null) {
            log.warn("Kampanya ID boş gönderildi");
            throw new BadRequestException("Kampanya ID boş olamaz.");
        }

        if (brandToCampaignIUDto.getBrandId() == null) {
            log.warn("Marka ID boş gönderildi");
            throw new BadRequestException("Marka ID boş olamaz.");
        }

        BrandToCampaign brandToCampaign = brandToCampaignRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Güncellenecek marka-kampanya ilişkisi bulunamadı. ID: {}", id);
                    return new CustomNotFoundException("Marka-Kampanya ilişkisi ID " + id + " bulunamadı");
                });

        Campaign campaign = campaignRepository.findById(brandToCampaignIUDto.getCampaignId())
                .orElseThrow(() -> {
                    log.warn("Kampanya bulunamadı. ID: {}", brandToCampaignIUDto.getCampaignId());
                    return new CustomNotFoundException("Kampanya ID " + brandToCampaignIUDto.getCampaignId() + " bulunamadı");
                });

        Brand brand = brandRepository.findById(brandToCampaignIUDto.getBrandId())
                .orElseThrow(() -> {
                    log.warn("Marka bulunamadı. ID: {}", brandToCampaignIUDto.getBrandId());
                    return new CustomNotFoundException("Marka ID " + brandToCampaignIUDto.getBrandId() + " bulunamadı");
                });

        // Güncelleme sırasında farklı bir kayıtta aynı kombinasyonun var olup olmadığını kontrol et
        if (!brandToCampaign.getCampaign().getId().equals(brandToCampaignIUDto.getCampaignId()) ||
            !brandToCampaign.getBrand().getId().equals(brandToCampaignIUDto.getBrandId())) {
            if (brandToCampaignRepository.existsByCampaignIdAndBrandId(brandToCampaignIUDto.getCampaignId(), brandToCampaignIUDto.getBrandId())) {
                log.warn("Mevcut marka-kampanya kombinasyonu ile güncelleme girişimi. Kampanya ID: {}, Marka ID: {}", 
                        brandToCampaignIUDto.getCampaignId(), brandToCampaignIUDto.getBrandId());
                throw new AlreadyExistsException("Bu marka ve kampanya kombinasyonu zaten kayıtlı.");
            }
        }

        log.debug("Marka-Kampanya ilişkisi güncelleniyor. ID: {}", id);
        brandToCampaign.setBrand(brand);
        brandToCampaign.setCampaign(campaign);
        brandToCampaign.setIsActive(brandToCampaignIUDto.getIsActive());
        brandToCampaign.setUpdatedAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")));
        brandToCampaign = brandToCampaignRepository.save(brandToCampaign);

        log.info("Marka-Kampanya ilişkisi başarıyla güncellendi. ID: {}", brandToCampaign.getId());
        return BrandToCampaignDto.entityToDto(brandToCampaign);
    }

    @Override
    public void delete(Long id) {
        log.info("Marka-Kampanya ilişkisi silme isteği başlatıldı. ID: {}", id);
        
        BrandToCampaign brandToCampaign = brandToCampaignRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Silinecek marka-kampanya ilişkisi bulunamadı. ID: {}", id);
                    return new CustomNotFoundException("Marka-Kampanya ilişkisi ID " + id + " bulunamadı");
                });

        brandToCampaign.setIsActive(false);
        brandToCampaign.setUpdatedAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")));
        brandToCampaignRepository.save(brandToCampaign);
        log.info("Marka-Kampanya ilişkisi başarıyla silindi (deaktive edildi). ID: {}", brandToCampaign.getId());
    }
}
