package com.backend360.campaign_service.service.Impl;

import com.backend360.campaign_service.dto.FormToCampaignDto;
import com.backend360.campaign_service.dto.FormToCampaignIUDto;
import com.backend360.campaign_service.exception.AlreadyExistsException;
import com.backend360.campaign_service.exception.BadRequestException;
import com.backend360.campaign_service.exception.CustomNotFoundException;
import com.backend360.campaign_service.model.Campaign;
import com.backend360.campaign_service.model.Form;
import com.backend360.campaign_service.model.FormToCampaign;
import com.backend360.campaign_service.repository.CampaignRepository;
import com.backend360.campaign_service.repository.FormRepository;
import com.backend360.campaign_service.repository.FormToCampaignRepository;
import com.backend360.campaign_service.service.IFormToCampaignService;
import com.backend360.campaign_service.service.CrmContactIntegrationService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FormToCampaignServiceImpl implements IFormToCampaignService {
    @Autowired
    private FormToCampaignRepository formToCampaignRepository;

    @Autowired
    private CampaignRepository campaignRepository;

    @Autowired
    private FormRepository formRepository;

    @Autowired
    private EmailServiceImpl emailService;

    @Autowired
    private CrmContactIntegrationService crmContactIntegrationService;


    @Override
    public FormToCampaignDto save(FormToCampaignIUDto formToCampaignIUDto) {
        // Input validation
        if (formToCampaignIUDto.getCampaignId() == null) {
            throw new BadRequestException("Kampanya ID boş olamaz.");
        }

        if (formToCampaignIUDto.getFormId() == null) {
            throw new BadRequestException("Form ID boş olamaz.");
        }

        // Check if campaign exists
        Campaign campaign = campaignRepository.findById(formToCampaignIUDto.getCampaignId())
                .orElseThrow(() -> new CustomNotFoundException("Kampanya ID " + formToCampaignIUDto.getCampaignId() + " bulunamadı"));

        // Check if form exists
        Form form = formRepository.findById(formToCampaignIUDto.getFormId())
                .orElseThrow(() -> new CustomNotFoundException("Form ID " + formToCampaignIUDto.getFormId() + " bulunamadı"));

        // Check for duplicates
        if (formToCampaignRepository.existsByCampaignIdAndFormId(formToCampaignIUDto.getCampaignId(), formToCampaignIUDto.getFormId())) {
            throw new AlreadyExistsException("Bu form zaten bu kampanya ile ilişkilendirilmiş. Kampanya ID: " + formToCampaignIUDto.getCampaignId() + ", Form ID: " + formToCampaignIUDto.getFormId());
        }

        FormToCampaign formToCampaign = FormToCampaign.builder()
                .campaign(campaign)
                .form(form)
                .ipAddress(formToCampaignIUDto.getIpAddress())
                .isActive(formToCampaignIUDto.getIsActive())
                .contactPhone(formToCampaignIUDto.getContactPhone())
                .contactEmail(formToCampaignIUDto.getContactEmail())
                .contactName(formToCampaignIUDto.getContactName())
                .contactSurname(formToCampaignIUDto.getContactSurname())
                .createdAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")))
                .updatedAt(null)
                .build();

        FormToCampaign saved = formToCampaignRepository.save(formToCampaign);

        // Email gönderimi (kayıt BAŞARILIYSA)
        emailService.sendFormToCampaignMail(form, campaign);

        // CRM'e gönder
        try {
            crmContactIntegrationService.sendFormToCrm(FormToCampaignDto.entityToDto(saved));
        } catch (Exception e) {
            log.error("Failed to send form to CRM: {}", e.getMessage());
            // CRM hatası ana işlemi etkilemesin
        }

        return FormToCampaignDto.entityToDto(saved);
    }

    @Override
    public List<FormToCampaignDto> findAll() {
        List<FormToCampaign> formToCampaigns = formToCampaignRepository.findAll();

        return formToCampaigns.stream()
                .map(FormToCampaignDto::entityToDto)
                .collect(Collectors.toList());
    }

    @Override
    public FormToCampaignDto findById(Long id) {
        FormToCampaign formToCampaign = formToCampaignRepository.findById(id)
                .orElseThrow(() -> new CustomNotFoundException("FormToCampaign ID " + id + " bulunamadı"));

        return FormToCampaignDto.entityToDto(formToCampaign);
    }

    @Override
    public FormToCampaignDto update(Long id, FormToCampaignIUDto formToCampaignIUDto) {
        // Input validation
        if (formToCampaignIUDto.getCampaignId() == null) {
            throw new BadRequestException("Kampanya ID boş olamaz.");
        }

        if (formToCampaignIUDto.getFormId() == null) {
            throw new BadRequestException("Form ID boş olamaz.");
        }

        FormToCampaign formToCampaign = formToCampaignRepository.findById(id)
                .orElseThrow(() -> new CustomNotFoundException("FormToCampaign ID " + id + " bulunamadı"));

        // Check if campaign exists
        Campaign campaign = campaignRepository.findById(formToCampaignIUDto.getCampaignId())
                .orElseThrow(() -> new CustomNotFoundException("Kampanya ID " + formToCampaignIUDto.getCampaignId() + " bulunamadı"));

        // Check if form exists
        Form form = formRepository.findById(formToCampaignIUDto.getFormId())
                .orElseThrow(() -> new CustomNotFoundException("Form ID " + formToCampaignIUDto.getFormId() + " bulunamadı"));

        // Check for duplicates only if the values have changed
        if ((!formToCampaign.getCampaign().getId().equals(formToCampaignIUDto.getCampaignId()) ||
                !formToCampaign.getForm().getId().equals(formToCampaignIUDto.getFormId())) &&
                formToCampaignRepository.existsByCampaignIdAndFormId(formToCampaignIUDto.getCampaignId(), formToCampaignIUDto.getFormId())) {
            throw new AlreadyExistsException("Bu form zaten bu kampanya ile ilişkilendirilmiş. Kampanya ID: " + formToCampaignIUDto.getCampaignId() + ", Form ID: " + formToCampaignIUDto.getFormId());
        }

        formToCampaign.setForm(form);
        formToCampaign.setCampaign(campaign);
        formToCampaign.setIpAddress(formToCampaignIUDto.getIpAddress());
        formToCampaign.setIsActive(formToCampaignIUDto.getIsActive());
        formToCampaign.setContactPhone(formToCampaignIUDto.getContactPhone());
        formToCampaign.setContactEmail(formToCampaignIUDto.getContactEmail());
        formToCampaign.setContactName(formToCampaignIUDto.getContactName());
        formToCampaign.setContactSurname(formToCampaignIUDto.getContactSurname());
        formToCampaign.setUpdatedAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")));
        formToCampaign = formToCampaignRepository.save(formToCampaign);

        return FormToCampaignDto.entityToDto(formToCampaign);
    }

    @Override
    public void delete(Long id) {
        FormToCampaign formToCampaign = formToCampaignRepository.findById(id)
                .orElseThrow(() -> new CustomNotFoundException("FormToCampaign ID " + id + " bulunamadı"));

        formToCampaign.setIsActive(false);
        formToCampaign.setUpdatedAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")));
        formToCampaignRepository.save(formToCampaign);
    }

    @Override
    public FormToCampaignDto findByFormId(Long formId) {
        FormToCampaign entity = formToCampaignRepository.findByForm_Id(formId);
        if (entity == null) return null;
        return FormToCampaignDto.entityToDto(entity);
    }

    @Override
    public boolean existsByFormIdAndCampaignIdAndIsActive(Long formId, Long campaignId, Boolean isActive) {
        return formToCampaignRepository.existsByForm_IdAndCampaign_IdAndIsActive(formId, campaignId, isActive);
    }

    @Override
    public FormToCampaignDto findByFormIdAndCampaignIdAndIsActive(Long formId, Long campaignId, Boolean isActive) {
        FormToCampaign entity = formToCampaignRepository.findByForm_IdAndCampaign_IdAndIsActive(formId, campaignId, isActive)
                .orElseThrow(() -> new CustomNotFoundException("Form-Campaign ilişkisi bulunamadı. FormId: " + formId + ", CampaignId: " + campaignId));
        return FormToCampaignDto.entityToDto(entity);
    }
}
