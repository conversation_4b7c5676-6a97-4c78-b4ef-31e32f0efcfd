package com.backend360.campaign_service.service.Impl;

import com.backend360.campaign_service.dto.CampaignDetailDto;
import com.backend360.campaign_service.dto.CampaignDetailIUDto;
import com.backend360.campaign_service.exception.BadRequestException;
import com.backend360.campaign_service.exception.CustomNotFoundException;
import com.backend360.campaign_service.model.CampaignDetail;
import com.backend360.campaign_service.repository.CampaignDetailRepository;
import com.backend360.campaign_service.service.ICampaignDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CampaignDetailServiceImpl implements ICampaignDetailService {
    @Autowired
    private CampaignDetailRepository campaignDetailRepository;

    @Override
    public CampaignDetailDto save(CampaignDetailIUDto campaignDetailIUDto) {
        log.info("Kampanya detayı kaydetme isteği başlatıldı");
        
        if (campaignDetailIUDto.getDetails() == null) {
            log.warn("Kampanya detayları null gönderildi");
            throw new BadRequestException("Kampanya detayları boş olamaz.");
        }

        // Details Map'inin boş olup olmadığını kontrol et
        Map<String, Object> details = campaignDetailIUDto.getDetails();
        if (details.isEmpty()) {
            log.warn("Kampanya detayları boş Map olarak gönderildi");
            throw new BadRequestException("Kampanya detayları boş olamaz.");
        }

        log.debug("Kampanya detayı kaydediliyor. Detay sayısı: {}", details.size());
        CampaignDetail campaignDetail = CampaignDetail.builder()
                .details(campaignDetailIUDto.getDetails())
                .isActive(campaignDetailIUDto.getIsActive())
                .createdAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")))
                .updatedAt(null)
                .build();

        CampaignDetail savedCampaignDetail = campaignDetailRepository.save(campaignDetail);
        log.info("Kampanya detayı başarıyla kaydedildi. ID: {}", savedCampaignDetail.getId());
        
        return CampaignDetailDto.entityToDto(savedCampaignDetail);
    }

    @Override
    public List<CampaignDetailDto> findAll() {
        log.info("Tüm kampanya detayları listeleniyor");
        List<CampaignDetail> campaignDetails = campaignDetailRepository.findAll();
        log.debug("Toplam {} kampanya detayı bulundu", campaignDetails.size());

        return campaignDetails.stream()
                .map(CampaignDetailDto::entityToDto)
                .collect(Collectors.toList());
    }

    @Override
    public CampaignDetailDto findById(Long id) {
        log.info("Kampanya detayı aranıyor. ID: {}", id);
        CampaignDetail campaignDetail = campaignDetailRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Kampanya detayı bulunamadı. ID: {}", id);
                    return new CustomNotFoundException("Kampanya detayı ID " + id + " bulunamadı");
                });
        log.debug("Kampanya detayı bulundu. ID: {}", campaignDetail.getId());
        return CampaignDetailDto.entityToDto(campaignDetail);
    }

    @Override
    public CampaignDetailDto update(Long id, CampaignDetailIUDto campaignDetailIUDto) {
        log.info("Kampanya detayı güncelleme isteği başlatıldı. ID: {}", id);
        
        if (campaignDetailIUDto.getDetails() == null) {
            log.warn("Kampanya detayları null gönderildi");
            throw new BadRequestException("Kampanya detayları boş olamaz.");
        }

        // Details Map'inin boş olup olmadığını kontrol et
        Map<String, Object> details = campaignDetailIUDto.getDetails();
        if (details.isEmpty()) {
            log.warn("Kampanya detayları boş Map olarak gönderildi");
            throw new BadRequestException("Kampanya detayları boş olamaz.");
        }

        CampaignDetail campaignDetail = campaignDetailRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Güncellenecek kampanya detayı bulunamadı. ID: {}", id);
                    return new CustomNotFoundException("Kampanya detayı ID " + id + " bulunamadı");
                });

        log.debug("Kampanya detayı güncelleniyor. ID: {}, Yeni detay sayısı: {}", id, details.size());
        campaignDetail.setDetails(campaignDetailIUDto.getDetails());
        campaignDetail.setIsActive(campaignDetailIUDto.getIsActive());
        campaignDetail.setUpdatedAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")));
        campaignDetail = campaignDetailRepository.save(campaignDetail);

        log.info("Kampanya detayı başarıyla güncellendi. ID: {}", campaignDetail.getId());
        return CampaignDetailDto.entityToDto(campaignDetail);
    }

    @Override
    public void delete(Long id) {
        log.info("Kampanya detayı silme isteği başlatıldı. ID: {}", id);
        
        CampaignDetail campaignDetail = campaignDetailRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Silinecek kampanya detayı bulunamadı. ID: {}", id);
                    return new CustomNotFoundException("Kampanya detayı ID " + id + " bulunamadı");
                });

        campaignDetail.setIsActive(false);
        campaignDetail.setUpdatedAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")));
        campaignDetailRepository.save(campaignDetail);
        log.info("Kampanya detayı başarıyla silindi (deaktive edildi). ID: {}", campaignDetail.getId());
    }
}
