package com.backend360.campaign_service.service;

import com.backend360.campaign_service.dto.CampaignToImageDto;
import com.backend360.campaign_service.dto.CampaignToImageIUDto;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

public interface ICampaignToImageService {

    List<CampaignToImageDto> findAll();

    CampaignToImageDto findById(Long id);

    CampaignToImageDto update(Long id, CampaignToImageIUDto campaignToImageIUDto);

    void delete(Long id);

    CampaignToImageDto uploadImage(Long campaignId, Boolean isShowcase, MultipartFile imageFile) throws IOException;

    List<CampaignToImageDto> uploadMultipleDetailImages(Long campaignId, MultipartFile[] imageFiles) throws IOException;

    Optional<CampaignToImageDto> getShowcaseImageByCampaignId(Long campaignId);

    List<CampaignToImageDto> getDetailImagesByCampaignId(Long campaignId);

    List<CampaignToImageDto> getAllImagesByCampaignId(Long campaignId);

    CampaignToImageDto updateImageFile(Long id, MultipartFile imageFile) throws IOException;

    CampaignToImageDto updateShowcaseImageByCampaignId(Long campaignId, MultipartFile imageFile) throws IOException;
}
