package com.backend360.campaign_service.service;

import com.backend360.campaign_service.dto.EventDto;
import com.backend360.campaign_service.dto.EventIUDto;
import com.backend360.campaign_service.dto.PopularItemDto;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface IEventService {
    EventDto createEvent(EventIUDto eventIUDto);
    List<PopularItemDto> getPopularCampaigns();
    List<PopularItemDto> getPopularBrands();
    Long getClickCount(LocalDateTime startDate, LocalDateTime endDate);
    List<PopularItemDto> getUserPopularCampaigns(String userId);
    List<PopularItemDto> getUserPopularBrands(String userId);
} 