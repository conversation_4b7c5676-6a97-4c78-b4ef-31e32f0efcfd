package com.backend360.campaign_service.service;

import com.backend360.campaign_service.dto.BrandDto;
import com.backend360.campaign_service.dto.BrandIUDto;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface IBrandService {
    BrandDto save(BrandIUDto brandIUDto, MultipartFile image) throws IOException;

    List<BrandDto> findAll();

    BrandDto findById(Long id);

    BrandDto update(Long id, BrandIUDto brandIUDto, MultipartFile image) throws IOException;

    void delete(Long id);
}
