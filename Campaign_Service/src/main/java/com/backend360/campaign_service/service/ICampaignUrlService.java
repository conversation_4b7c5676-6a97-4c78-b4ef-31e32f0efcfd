package com.backend360.campaign_service.service;

import com.backend360.campaign_service.dto.CampaignUrlDto;
import com.backend360.campaign_service.dto.CampaignUrlIUDto;

import java.util.List;

public interface ICampaignUrlService {
    CampaignUrlDto save(CampaignUrlIUDto campaignUrlIUDto);

    List<CampaignUrlDto> findAll();

    CampaignUrlDto findById(Long id);

    CampaignUrlDto update(Long id, CampaignUrlIUDto campaignUrlIUDto);

    void delete(Long id);
}
