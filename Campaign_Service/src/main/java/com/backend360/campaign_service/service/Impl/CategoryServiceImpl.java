package com.backend360.campaign_service.service.Impl;

import com.backend360.campaign_service.dto.CategoryDto;
import com.backend360.campaign_service.dto.CategoryIUDto;
import com.backend360.campaign_service.dto.CampaignDetailDto;
import com.backend360.campaign_service.exception.AlreadyExistsException;
import com.backend360.campaign_service.exception.BadRequestException;
import com.backend360.campaign_service.exception.CustomNotFoundException;
import com.backend360.campaign_service.model.CampaignDetail;
import com.backend360.campaign_service.model.Category;
import com.backend360.campaign_service.repository.CampaignDetailRepository;
import com.backend360.campaign_service.repository.CategoryRepository;
import com.backend360.campaign_service.service.ICategoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CategoryServiceImpl implements ICategoryService {
    @Autowired
    private CategoryRepository categoryRepository;

    @Autowired
    private CampaignDetailRepository campaignDetailRepository;

    @Override
    public CategoryDto save(CategoryIUDto categoryIUDto) {
        log.info("Kategori kaydetme isteği başlatıldı: {}", categoryIUDto.getName());
        
        if (categoryIUDto.getName() == null || categoryIUDto.getName().isBlank()) {
            log.warn("Kategori adı boş gönderildi");
            throw new BadRequestException("Kategori adı boş olamaz.");
        }

        if (categoryIUDto.getCampaignDetailId() == null) {
            log.warn("Kampanya detay ID boş gönderildi");
            throw new BadRequestException("Kampanya detay ID boş olamaz.");
        }

        if (categoryIUDto.getParentCategoryId() == null) {
            log.warn("Parent category ID boş gönderildi");
            throw new BadRequestException("Parent category ID boş olamaz.");
        }
        if (categoryIUDto.getParentCategoryId() != 0) {
            log.debug("Üst kategori ID kontrol ediliyor: {}", categoryIUDto.getParentCategoryId());
            if (!categoryRepository.existsById(categoryIUDto.getParentCategoryId())) {
                log.warn("Üst kategori bulunamadı. ID: {}", categoryIUDto.getParentCategoryId());
                throw new CustomNotFoundException("Üst kategori ID " + categoryIUDto.getParentCategoryId() + " bulunamadı");
            }
        }

        CampaignDetail campaignDetail = campaignDetailRepository.findById(categoryIUDto.getCampaignDetailId())
                .orElseThrow(() -> {
                    log.warn("Kampanya detayı bulunamadı. ID: {}", categoryIUDto.getCampaignDetailId());
                    return new CustomNotFoundException("Kampanya detayı ID " + categoryIUDto.getCampaignDetailId() + " bulunamadı");
                });

        // Aynı isimde kategori var mı kontrol et
        if (categoryRepository.existsByName(categoryIUDto.getName())) {
            log.warn("Mevcut kategori adı ile kayıt girişimi: {}", categoryIUDto.getName());
            throw new AlreadyExistsException("Bu kategori adı zaten kayıtlı: " + categoryIUDto.getName());
        }

        Category category = Category.builder()
                .name(categoryIUDto.getName())
                .isActive(categoryIUDto.getIsActive())
                .parentCategoryId(categoryIUDto.getParentCategoryId())
                .campaignDetail(campaignDetail)
                .createdAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")))
                .updatedAt(null)
                .build();

        Category savedCategory = categoryRepository.save(category);
        log.info("Kategori başarıyla kaydedildi. ID: {}, Ad: {}", savedCategory.getId(), savedCategory.getName());
        
        return CategoryDto.entityToDto(savedCategory);
    }

    @Override
    public List<CategoryDto> findAll() {
        log.info("Tüm kategoriler listeleniyor");
        List<Category> categories = categoryRepository.findAll();
        log.debug("Toplam {} kategori bulundu", categories.size());

        return categories.stream()
                .map(CategoryDto::entityToDto)
                .collect(Collectors.toList());
    }

    @Override
    public CategoryDto findById(Long id) {
        log.info("Kategori aranıyor. ID: {}", id);
        Category category = categoryRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Kategori bulunamadı. ID: {}", id);
                    return new CustomNotFoundException("Kategori ID " + id + " bulunamadı");
                });
        log.debug("Kategori bulundu: {}", category.getName());
        return CategoryDto.entityToDto(category);
    }

    @Override
    public CategoryDto update(Long id, CategoryIUDto categoryIUDto) {
        log.info("Kategori güncelleme isteği başlatıldı. ID: {}", id);
        
        if (categoryIUDto.getName() == null || categoryIUDto.getName().isBlank()) {
            log.warn("Kategori adı boş gönderildi");
            throw new BadRequestException("Kategori adı boş olamaz.");
        }

        if (categoryIUDto.getCampaignDetailId() == null) {
            log.warn("Kampanya detay ID boş gönderildi");
            throw new BadRequestException("Kampanya detay ID boş olamaz.");
        }

        if (categoryIUDto.getParentCategoryId() == null) {
            log.warn("Parent category ID boş gönderildi");
            throw new BadRequestException("Parent category ID boş olamaz.");
        }
        if (categoryIUDto.getParentCategoryId() != 0) {
            log.debug("Üst kategori ID kontrol ediliyor: {}", categoryIUDto.getParentCategoryId());
            if (!categoryRepository.existsById(categoryIUDto.getParentCategoryId())) {
                log.warn("Üst kategori bulunamadı. ID: {}", categoryIUDto.getParentCategoryId());
                throw new CustomNotFoundException("Üst kategori ID " + categoryIUDto.getParentCategoryId() + " bulunamadı");
            }
            // Kendini parent olarak seçemez
            if (categoryIUDto.getParentCategoryId().equals(id)) {
                log.warn("Kategori kendisini üst kategori olarak seçme girişimi. ID: {}", id);
                throw new BadRequestException("Kategori kendisini üst kategori olarak seçemez.");
            }
        }

        Category category = categoryRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Güncellenecek kategori bulunamadı. ID: {}", id);
                    return new CustomNotFoundException("Kategori ID " + id + " bulunamadı");
                });

        CampaignDetail campaignDetail = campaignDetailRepository.findById(categoryIUDto.getCampaignDetailId())
                .orElseThrow(() -> {
                    log.warn("Kampanya detayı bulunamadı. ID: {}", categoryIUDto.getCampaignDetailId());
                    return new CustomNotFoundException("Kampanya detayı ID " + categoryIUDto.getCampaignDetailId() + " bulunamadı");
                });

        // Kategori adının başka bir kayıtta zaten var olup olmadığını kontrol et
        if (!category.getName().equals(categoryIUDto.getName()) && 
            categoryRepository.existsByName(categoryIUDto.getName())) {
            log.warn("Mevcut kategori adı ile güncelleme girişimi: {}", categoryIUDto.getName());
            throw new AlreadyExistsException("Bu kategori adı zaten kayıtlı: " + categoryIUDto.getName());
        }

        log.debug("Kategori güncelleniyor: {} -> {}", category.getName(), categoryIUDto.getName());
        category.setName(categoryIUDto.getName());
        category.setIsActive(categoryIUDto.getIsActive());
        category.setCampaignDetail(campaignDetail);
        category.setParentCategoryId(categoryIUDto.getParentCategoryId());
        category.setUpdatedAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")));
        category = categoryRepository.save(category);

        log.info("Kategori başarıyla güncellendi. ID: {}, Ad: {}", category.getId(), category.getName());
        return CategoryDto.entityToDto(category);
    }

    @Override
    public void delete(Long id) {
        log.info("Kategori silme isteği başlatıldı. ID: {}", id);
        
        Category category = categoryRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Silinecek kategori bulunamadı. ID: {}", id);
                    return new CustomNotFoundException("Kategori ID " + id + " bulunamadı");
                });

        // Bu kategorinin alt kategorisi var mı kontrol et
        if (categoryRepository.existsByParentCategoryId(id)) {
            log.warn("Alt kategorileri olan kategori silme girişimi. ID: {}", id);
            throw new BadRequestException("Bu kategorinin alt kategorileri bulunduğu için silinemez. Önce alt kategorileri siliniz.");
        }

        category.setIsActive(false);
        category.setUpdatedAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")));
        categoryRepository.save(category);
        log.info("Kategori başarıyla silindi (deaktive edildi). ID: {}, Ad: {}", category.getId(), category.getName());
    }
    
    @Override
    public CampaignDetailDto findCampaignDetailByCategoryId(Long categoryId) {
        log.info("Kategori ID'sine göre kampanya detayı aranıyor: {}", categoryId);
        
        Category category = categoryRepository.findById(categoryId)
                .orElseThrow(() -> {
                    log.warn("Kategori bulunamadı. ID: {}", categoryId);
                    return new CustomNotFoundException("Kategori ID " + categoryId + " bulunamadı");
                });
        
        if (category.getCampaignDetail() == null) {
            log.warn("Kategorinin kampanya detayı bulunamadı. Kategori ID: {}", categoryId);
            return null;
        }
        
        log.debug("Kampanya detayı bulundu. Kategori ID: {}, Campaign Detail ID: {}", 
                 categoryId, category.getCampaignDetail().getId());
        return CampaignDetailDto.entityToDto(category.getCampaignDetail());
    }
}
