package com.backend360.campaign_service.service.Impl;

import com.backend360.campaign_service.dto.BrandToCategoryDto;
import com.backend360.campaign_service.dto.BrandToCategoryIUDto;
import com.backend360.campaign_service.exception.AlreadyExistsException;
import com.backend360.campaign_service.exception.BadRequestException;
import com.backend360.campaign_service.exception.CustomNotFoundException;
import com.backend360.campaign_service.model.Brand;
import com.backend360.campaign_service.model.BrandToCategory;
import com.backend360.campaign_service.model.Category;
import com.backend360.campaign_service.repository.BrandRepository;
import com.backend360.campaign_service.repository.BrandToCategoryRepository;
import com.backend360.campaign_service.repository.CategoryRepository;
import com.backend360.campaign_service.service.IBrandToCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BrandToCategoryServiceImpl implements IBrandToCategoryService {
    @Autowired
    private BrandToCategoryRepository brandToCategoryRepository;

    @Autowired
    private CategoryRepository categoryRepository;

    @Autowired
    private BrandRepository brandRepository;

    @Override
    public BrandToCategoryDto save(BrandToCategoryIUDto brandToCategoryIUDto) {
        log.info("Marka-Kategori ilişkisi kaydetme isteği başlatıldı. Kategori ID: {}, Marka ID: {}", 
                brandToCategoryIUDto.getCategoryId(), brandToCategoryIUDto.getBrandId());
        
        if (brandToCategoryIUDto.getCategoryId() == null) {
            log.warn("Kategori ID boş gönderildi");
            throw new BadRequestException("Kategori ID boş olamaz.");
        }

        if (brandToCategoryIUDto.getBrandId() == null) {
            log.warn("Marka ID boş gönderildi");
            throw new BadRequestException("Marka ID boş olamaz.");
        }

        Category category = categoryRepository.findById(brandToCategoryIUDto.getCategoryId())
                .orElseThrow(() -> {
                    log.warn("Kategori bulunamadı. ID: {}", brandToCategoryIUDto.getCategoryId());
                    return new CustomNotFoundException("Kategori ID " + brandToCategoryIUDto.getCategoryId() + " bulunamadı");
                });

        Brand brand = brandRepository.findById(brandToCategoryIUDto.getBrandId())
                .orElseThrow(() -> {
                    log.warn("Marka bulunamadı. ID: {}", brandToCategoryIUDto.getBrandId());
                    return new CustomNotFoundException("Marka ID " + brandToCategoryIUDto.getBrandId() + " bulunamadı");
                });

        // Aynı marka ve kategori kombinasyonunun zaten var olup olmadığını kontrol et
        if (brandToCategoryRepository.existsByCategoryIdAndBrandIdId(brandToCategoryIUDto.getCategoryId(), brandToCategoryIUDto.getBrandId())) {
            log.warn("Mevcut marka-kategori kombinasyonu ile kayıt girişimi. Kategori ID: {}, Marka ID: {}", 
                    brandToCategoryIUDto.getCategoryId(), brandToCategoryIUDto.getBrandId());
            throw new AlreadyExistsException("Bu marka ve kategori kombinasyonu zaten kayıtlı.");
        }

        BrandToCategory brandToCategory = BrandToCategory.builder()
                .category(category)
                .brandId(brand)
                .isActive(brandToCategoryIUDto.getIsActive())
                .createdAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")))
                .updatedAt(null)
                .build();

        BrandToCategory savedBrandToCategory = brandToCategoryRepository.save(brandToCategory);
        log.info("Marka-Kategori ilişkisi başarıyla kaydedildi. ID: {}", savedBrandToCategory.getId());
        
        return BrandToCategoryDto.entityToDto(savedBrandToCategory);
    }

    @Override
    public List<BrandToCategoryDto> findAll() {
        log.info("Tüm marka-kategori ilişkileri listeleniyor");
        List<BrandToCategory> brandToCategories = brandToCategoryRepository.findAll();
        log.debug("Toplam {} marka-kategori ilişkisi bulundu", brandToCategories.size());

        return brandToCategories.stream()
                .map(BrandToCategoryDto::entityToDto)
                .collect(Collectors.toList());
    }

    @Override
    public BrandToCategoryDto findById(Long id) {
        log.info("Marka-Kategori ilişkisi aranıyor. ID: {}", id);
        BrandToCategory brandToCategory = brandToCategoryRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Marka-Kategori ilişkisi bulunamadı. ID: {}", id);
                    return new CustomNotFoundException("Marka-Kategori ilişkisi ID " + id + " bulunamadı");
                });
        log.debug("Marka-Kategori ilişkisi bulundu. ID: {}", brandToCategory.getId());
        return BrandToCategoryDto.entityToDto(brandToCategory);
    }

    @Override
    public BrandToCategoryDto update(Long id, BrandToCategoryIUDto brandToCategoryIUDto) {
        log.info("Marka-Kategori ilişkisi güncelleme isteği başlatıldı. ID: {}", id);
        
        if (brandToCategoryIUDto.getCategoryId() == null) {
            log.warn("Kategori ID boş gönderildi");
            throw new BadRequestException("Kategori ID boş olamaz.");
        }

        if (brandToCategoryIUDto.getBrandId() == null) {
            log.warn("Marka ID boş gönderildi");
            throw new BadRequestException("Marka ID boş olamaz.");
        }

        BrandToCategory brandToCategory = brandToCategoryRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Güncellenecek marka-kategori ilişkisi bulunamadı. ID: {}", id);
                    return new CustomNotFoundException("Marka-Kategori ilişkisi ID " + id + " bulunamadı");
                });

        Category category = categoryRepository.findById(brandToCategoryIUDto.getCategoryId())
                .orElseThrow(() -> {
                    log.warn("Kategori bulunamadı. ID: {}", brandToCategoryIUDto.getCategoryId());
                    return new CustomNotFoundException("Kategori ID " + brandToCategoryIUDto.getCategoryId() + " bulunamadı");
                });

        Brand brand = brandRepository.findById(brandToCategoryIUDto.getBrandId())
                .orElseThrow(() -> {
                    log.warn("Marka bulunamadı. ID: {}", brandToCategoryIUDto.getBrandId());
                    return new CustomNotFoundException("Marka ID " + brandToCategoryIUDto.getBrandId() + " bulunamadı");
                });

        // Güncelleme sırasında farklı bir kayıtta aynı kombinasyonun var olup olmadığını kontrol et
        if (!brandToCategory.getCategory().getId().equals(brandToCategoryIUDto.getCategoryId()) ||
            !brandToCategory.getBrandId().getId().equals(brandToCategoryIUDto.getBrandId())) {
            if (brandToCategoryRepository.existsByCategoryIdAndBrandIdId(brandToCategoryIUDto.getCategoryId(), brandToCategoryIUDto.getBrandId())) {
                log.warn("Mevcut marka-kategori kombinasyonu ile güncelleme girişimi. Kategori ID: {}, Marka ID: {}", 
                        brandToCategoryIUDto.getCategoryId(), brandToCategoryIUDto.getBrandId());
                throw new AlreadyExistsException("Bu marka ve kategori kombinasyonu zaten kayıtlı.");
            }
        }

        log.debug("Marka-Kategori ilişkisi güncelleniyor. ID: {}", id);
        brandToCategory.setBrandId(brand);
        brandToCategory.setCategory(category);
        brandToCategory.setIsActive(brandToCategoryIUDto.getIsActive());
        brandToCategory.setUpdatedAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")));
        brandToCategory = brandToCategoryRepository.save(brandToCategory);

        log.info("Marka-Kategori ilişkisi başarıyla güncellendi. ID: {}", brandToCategory.getId());
        return BrandToCategoryDto.entityToDto(brandToCategory);
    }

    @Override
    public void delete(Long id) {
        log.info("Marka-Kategori ilişkisi silme isteği başlatıldı. ID: {}", id);
        
        BrandToCategory brandToCategory = brandToCategoryRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Silinecek marka-kategori ilişkisi bulunamadı. ID: {}", id);
                    return new CustomNotFoundException("Marka-Kategori ilişkisi ID " + id + " bulunamadı");
                });

        brandToCategory.setIsActive(false);
        brandToCategory.setUpdatedAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")));
        brandToCategoryRepository.save(brandToCategory);
        log.info("Marka-Kategori ilişkisi başarıyla silindi (deaktive edildi). ID: {}", brandToCategory.getId());
    }
}
