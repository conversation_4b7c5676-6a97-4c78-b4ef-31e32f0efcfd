package com.backend360.campaign_service.service;

import com.backend360.campaign_service.dto.CustomerToCampaignDto;
import com.backend360.campaign_service.dto.CustomerToCampaignIUDto;

import java.util.List;

public interface ICustomerToCampaignService {
    CustomerToCampaignDto save(CustomerToCampaignIUDto customerToCampaignIUDto);

    List<CustomerToCampaignDto> findAll();

    CustomerToCampaignDto findById(Long id);

    CustomerToCampaignDto update(Long id, CustomerToCampaignIUDto customerToCampaignIUDto);

    void changeIsCalledStatus(Long id, Boolean status);

    void delete(Long id);

    // Public form submission için yeni metodlar
    boolean existsByCustomerIdAndCampaignIdAndIsActive(Long customerId, Long campaignId, Boolean isActive);
    CustomerToCampaignDto findByCustomerIdAndCampaignIdAndIsActive(Long customerId, Long campaignId, Boolean isActive);
}
