package com.backend360.campaign_service.service;

import com.backend360.campaign_service.dto.BrandToCampaignDto;
import com.backend360.campaign_service.dto.BrandToCampaignIUDto;

import java.util.List;

public interface IBrandToCampaignService {
    BrandToCampaignDto save(BrandToCampaignIUDto brandToCampaignIUDto);

    List<BrandToCampaignDto> findAll();

    BrandToCampaignDto findById(Long id);

    BrandToCampaignDto update(Long id, BrandToCampaignIUDto brandToCampaignIUDto);

    void delete(Long id);
}
