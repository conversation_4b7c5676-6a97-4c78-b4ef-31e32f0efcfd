package com.backend360.campaign_service.service;

import com.backend360.campaign_service.crm_client.CrmClient;
import com.backend360.campaign_service.dto.CustomerToCampaignDto;
import com.backend360.campaign_service.dto.FormToCampaignDto;
import com.backend360.campaign_service.dto.crm.ContactIUDto;
import com.backend360.campaign_service.dto.crm.ContactDto;
import com.backend360.campaign_service.dto.crm.CampaignIUDto;
import com.backend360.campaign_service.dto.crm.CampaignDto;
import com.backend360.campaign_service.model.Customer;
import com.backend360.campaign_service.model.Form;
import com.backend360.campaign_service.model.Campaign;

import com.backend360.campaign_service.model.BrandToCampaign;
import com.backend360.campaign_service.repository.BrandToCampaignRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class CrmContactIntegrationService {

    @Autowired
    private CrmClient crmClient;

    @Autowired
    private BrandToCampaignRepository brandToCampaignRepository;

    @Autowired
    private CrmTokenManager crmTokenManager;

    public void sendCustomerToCrm(CustomerToCampaignDto customerToCampaign) {
        try {
            // Customer bilgilerini ContactIUDto'ya çevir
            ContactIUDto contact = convertCustomerToContact(customerToCampaign);

            // CRM'e gönder
            ResponseEntity<ContactDto> response = crmClient.createContact(contact, crmTokenManager.getAuthorizationHeader());

            if (response.getStatusCode().is2xxSuccessful()) {
                log.debug("Customer sent to CRM successfully: {}", customerToCampaign.getCustomer().getEmail());
            } else {
                log.error("Failed to send customer to CRM: {}", response.getStatusCode());
            }

        } catch (Exception e) {
            log.warn("CRM-API bağlantısı başarısız, customer kaydedildi ama CRM'e gönderilmedi: {} - {}",
                    customerToCampaign.getCustomer().getEmail(), e.getMessage());
            // CRM hatası customer kaydını engellemez, sadece log'a yazılır
        }
    }

    public void sendFormToCrm(FormToCampaignDto formToCampaign) {
        try {
            log.info("Form-to-CRM gönderimi başlıyor: {}", formToCampaign.getForm().getEmail());

            // Form bilgilerini ContactIUDto'ya çevir
            ContactIUDto contact = convertFormToContact(formToCampaign);
            log.debug("ContactIUDto oluşturuldu: {}", contact);

            // Token bilgisini log'la
            String authHeader = crmTokenManager.getAuthorizationHeader();
            log.debug("CRM Auth Header: {}", authHeader.substring(0, 30) + "...");

            // CRM'e gönder
            log.info("CRM API'ye istek gönderiliyor...");
            ResponseEntity<ContactDto> response = crmClient.createContact(contact, authHeader);

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Form başarıyla CRM'e gönderildi: {}", formToCampaign.getForm().getEmail());
            } else {
                log.error("CRM'e form gönderimi başarısız - Status: {}, Body: {}",
                        response.getStatusCode(), response.getBody());

                // 403 hatası için özel log
                if (response.getStatusCode().value() == 403) {
                    log.error("403 Forbidden hatası - Bu genellikle yetki sorunu veya yanlış endpoint anlamına gelir");
                    log.error("Contact verisi: {}", contact);
                    log.error("Auth header preview: {}", authHeader.substring(0, 30) + "...");
                }
            }

        } catch (Exception e) {
            log.warn("CRM-API bağlantısı başarısız, form kaydedildi ama CRM'e gönderilmedi: {} - {}",
                    formToCampaign.getForm().getEmail(), e.getMessage());
            log.debug("CRM hatası detayı:", e);

            // Exception tipini log'la
            log.error("Exception tipi: {}", e.getClass().getSimpleName());
            if (e.getCause() != null) {
                log.error("Cause: {}", e.getCause().getMessage());
            }

            // CRM hatası form kaydını engellemez, sadece log'a yazılır
        }
    }

    public void sendCampaignToCrm(Campaign campaign) {
        try {
            log.info("Sending campaign to CRM: {}", campaign.getName());

            // Campaign bilgilerini CampaignIUDto'ya çevir
            CampaignIUDto campaignDto = convertCampaignToCrmCampaign(campaign);

            // CRM'e gönder
            ResponseEntity<CampaignDto> response = crmClient.createCampaign(campaignDto, crmTokenManager.getAuthorizationHeader());

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Campaign sent to CRM successfully: {}", response.getBody());
            } else {
                log.error("Failed to send campaign to CRM: {}", response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("Error sending campaign to CRM: {}", e.getMessage(), e);
        }
    }

    private ContactIUDto convertCustomerToContact(CustomerToCampaignDto customerToCampaign) {
        Customer customer = customerToCampaign.getCustomer();
        Campaign campaign = customerToCampaign.getCampaign();
        String brandName = getBrandNameByCampaign(campaign);

        // Campaign type'ı category'den al
        String campaignType = null;
        if (campaign != null && campaign.getCategory() != null) {
            campaignType = campaign.getCategory().getName();
        }

        return ContactIUDto.builder()
                .name(customer.getName())
                .surname(customer.getSurname())
                .email(customer.getEmail())
                .phoneNumber(customer.getPhoneNumber())
                .countryCode("+90") // Türkiye için
                .timezone("Europe/Istanbul")
                .status("Active")
                // CustomerToCampaign bilgileri
                .customerToCampaignId(customerToCampaign.getId())
                .isCalled(customerToCampaign.getIsCalled())
                .isActive(customerToCampaign.getIsActive())
                .contactPhone(customerToCampaign.getContactPhone())
                .contactEmail(customerToCampaign.getContactEmail())
                .contactName(customerToCampaign.getContactName())
                .contactSurname(customerToCampaign.getContactSurname())
                .customerToCampaignCreatedAt(customerToCampaign.getCreatedAt())
                .customerToCampaignUpdatedAt(customerToCampaign.getUpdatedAt())
                // Campaign bilgileri
                .campaignId(campaign != null ? campaign.getId() : null)
                .campaignName(campaign != null ? campaign.getName() : null)
                .campaignTitle(campaign != null ? campaign.getTitle() : null)
                .campaignDescription(campaign != null ? campaign.getDescription() : null)
                .campaignType(campaignType)
                .brandName(brandName)
                .build();
    }

    private ContactIUDto convertFormToContact(FormToCampaignDto formToCampaign) {
        Form form = formToCampaign.getForm();
        Campaign campaign = formToCampaign.getCampaign();
        String brandName = getBrandNameByCampaign(campaign);

        // Campaign type'ı category'den al
        String campaignType = null;
        if (campaign != null && campaign.getCategory() != null) {
            campaignType = campaign.getCategory().getName();
        }

        return ContactIUDto.builder()
                .name(form.getName())
                .surname(form.getSurname())
                .email(form.getEmail())
                .phoneNumber(form.getPhoneNumber())
                .countryCode("+90") // Türkiye için
                .timezone("Europe/Istanbul")
                .status("Active")
                // FormToCampaign bilgileri
                .formToCampaignId(formToCampaign.getId())
                .ipAddress(formToCampaign.getIpAddress())
                .contactPhone(formToCampaign.getContactPhone())
                .contactEmail(formToCampaign.getContactEmail())
                .contactName(formToCampaign.getContactName())
                .contactSurname(formToCampaign.getContactSurname())
                .formToCampaignCreatedAt(formToCampaign.getCreatedAt())
                .formToCampaignUpdatedAt(formToCampaign.getUpdatedAt())
                // Campaign bilgileri
                .campaignId(campaign != null ? campaign.getId() : null)
                .campaignName(campaign != null ? campaign.getName() : null)
                .campaignTitle(campaign != null ? campaign.getTitle() : null)
                .campaignDescription(campaign != null ? campaign.getDescription() : null)
                .campaignType(campaignType)
                .brandName(brandName)
                .build();
    }

    private CampaignIUDto convertCampaignToCrmCampaign(Campaign campaign) {
        Map<String, Object> rules = new HashMap<>();
        if (campaign.getDetails() != null) {
            rules.put("details", campaign.getDetails());
        }

        return CampaignIUDto.builder()
                .name(campaign.getName())
                .description(campaign.getDescription())
                .status(campaign.getIsActive() ? "active" : "inactive")
                .campaignType("web_campaign")
                .startDate(campaign.getStartDate())
                .endDate(campaign.getEndDate())
                .rules(rules)
                .agentId(1L) // Default agent ID
                .agentAppId(1L) // Default app ID
                .build();
    }

    private String getBrandNameByCampaign(Campaign campaign) {
        if (campaign == null || campaign.getId() == null) return null;
        BrandToCampaign btc = brandToCampaignRepository.findFirstByCampaign_IdAndIsActiveTrue(campaign.getId());
        if (btc != null && btc.getBrand() != null) {
            return btc.getBrand().getName();
        }
        return null;
    }
}