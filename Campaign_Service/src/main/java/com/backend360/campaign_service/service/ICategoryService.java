package com.backend360.campaign_service.service;

import com.backend360.campaign_service.dto.CategoryDto;
import com.backend360.campaign_service.dto.CategoryIUDto;
import com.backend360.campaign_service.dto.CampaignDetailDto;

import java.util.List;

public interface ICategoryService {
    CategoryDto save(CategoryIUDto category);

    List<CategoryDto> findAll();

    CategoryDto findById(Long id);

    CategoryDto update(Long id, CategoryIUDto categoryIUDto);

    void delete(Long id);
    
    CampaignDetailDto findCampaignDetailByCategoryId(Long categoryId);
}
