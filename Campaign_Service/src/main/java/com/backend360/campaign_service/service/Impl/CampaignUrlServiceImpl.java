package com.backend360.campaign_service.service.Impl;

import com.backend360.campaign_service.dto.CampaignUrlDto;
import com.backend360.campaign_service.dto.CampaignUrlIUDto;
import com.backend360.campaign_service.exception.AlreadyExistsException;
import com.backend360.campaign_service.exception.BadRequestException;
import com.backend360.campaign_service.exception.CustomNotFoundException;
import com.backend360.campaign_service.model.Campaign;
import com.backend360.campaign_service.model.CampaignUrl;
import com.backend360.campaign_service.repository.CampaignRepository;
import com.backend360.campaign_service.repository.CampaignUrlRepository;
import com.backend360.campaign_service.service.ICampaignUrlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CampaignUrlServiceImpl implements ICampaignUrlService {
    @Autowired
    private CampaignUrlRepository campaignUrlRepository;

    @Autowired
    private CampaignRepository campaignRepository;

    @Override
    public CampaignUrlDto save(CampaignUrlIUDto campaignUrlIUDto) {
        log.info("Kampanya URL kaydetme isteği başlatıldı. Kampanya ID: {}", campaignUrlIUDto.getCampaignId());
        
        if (campaignUrlIUDto.getCampaignId() == null) {
            log.warn("Kampanya ID boş gönderildi");
            throw new BadRequestException("Kampanya ID boş olamaz.");
        }

        if (campaignUrlIUDto.getUrl() == null || campaignUrlIUDto.getUrl().isBlank()) {
            log.warn("URL boş gönderildi");
            throw new BadRequestException("URL boş olamaz.");
        }

        // URL formatının geçerli olup olmadığını kontrol et
        if (!isValidUrl(campaignUrlIUDto.getUrl())) {
            log.warn("Geçersiz URL formatı: {}", campaignUrlIUDto.getUrl());
            throw new BadRequestException("Geçerli bir URL formatı giriniz.");
        }

        Campaign campaign = campaignRepository.findById(campaignUrlIUDto.getCampaignId())
                .orElseThrow(() -> {
                    log.warn("Kampanya bulunamadı. ID: {}", campaignUrlIUDto.getCampaignId());
                    return new CustomNotFoundException("Kampanya ID " + campaignUrlIUDto.getCampaignId() + " bulunamadı");
                });

        // Bu kampanyaya ait zaten URL var mı kontrol et
        if (campaignUrlRepository.existsByCampaignId(campaignUrlIUDto.getCampaignId())) {
            log.warn("Kampanyaya ait zaten URL kaydı mevcut. Kampanya ID: {}", campaignUrlIUDto.getCampaignId());
            throw new AlreadyExistsException("Bu kampanyaya ait zaten bir URL kaydı var. Kampanya ID: " + campaignUrlIUDto.getCampaignId());
        }

        CampaignUrl campaignUrl = CampaignUrl.builder()
                .campaign(campaign)
                .url(campaignUrlIUDto.getUrl())
                .isActive(campaignUrlIUDto.getIsActive())
                .build();

        CampaignUrl savedCampaignUrl = campaignUrlRepository.save(campaignUrl);
        log.info("Kampanya URL başarıyla kaydedildi. ID: {}, URL: {}", savedCampaignUrl.getId(), savedCampaignUrl.getUrl());
        
        return CampaignUrlDto.entityToDto(savedCampaignUrl);
    }

    @Override
    public List<CampaignUrlDto> findAll() {
        log.info("Tüm kampanya URL'leri listeleniyor");
        List<CampaignUrl> campaignUrls = campaignUrlRepository.findAll();
        log.debug("Toplam {} kampanya URL'si bulundu", campaignUrls.size());

        return campaignUrls.stream()
                .map(CampaignUrlDto::entityToDto)
                .collect(Collectors.toList());
    }

    @Override
    public CampaignUrlDto findById(Long id) {
        log.info("Kampanya URL aranıyor. ID: {}", id);
        CampaignUrl campaignUrl = campaignUrlRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Kampanya URL bulunamadı. ID: {}", id);
                    return new CustomNotFoundException("Kampanya URL'si ID " + id + " bulunamadı");
                });
        log.debug("Kampanya URL bulundu: {}", campaignUrl.getUrl());
        return CampaignUrlDto.entityToDto(campaignUrl);
    }

    @Override
    public CampaignUrlDto update(Long id, CampaignUrlIUDto campaignUrlIUDto) {
        log.info("Kampanya URL güncelleme isteği başlatıldı. ID: {}", id);
        
        if (campaignUrlIUDto.getCampaignId() == null) {
            log.warn("Kampanya ID boş gönderildi");
            throw new BadRequestException("Kampanya ID boş olamaz.");
        }

        if (campaignUrlIUDto.getUrl() == null || campaignUrlIUDto.getUrl().isBlank()) {
            log.warn("URL boş gönderildi");
            throw new BadRequestException("URL boş olamaz.");
        }

        // URL formatının geçerli olup olmadığını kontrol et
        if (!isValidUrl(campaignUrlIUDto.getUrl())) {
            log.warn("Geçersiz URL formatı: {}", campaignUrlIUDto.getUrl());
            throw new BadRequestException("Geçerli bir URL formatı giriniz.");
        }

        CampaignUrl campaignUrl = campaignUrlRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Güncellenecek kampanya URL bulunamadı. ID: {}", id);
                    return new CustomNotFoundException("Kampanya URL'si ID " + id + " bulunamadı");
                });

        Campaign campaign = campaignRepository.findById(campaignUrlIUDto.getCampaignId())
                .orElseThrow(() -> {
                    log.warn("Kampanya bulunamadı. ID: {}", campaignUrlIUDto.getCampaignId());
                    return new CustomNotFoundException("Kampanya ID " + campaignUrlIUDto.getCampaignId() + " bulunamadı");
                });

        // Eğer farklı bir campaign'e taşınıyorsa, o campaign'in zaten URL'si var mı kontrol et
        if (!campaignUrl.getCampaign().getId().equals(campaignUrlIUDto.getCampaignId()) && 
            campaignUrlRepository.existsByCampaignId(campaignUrlIUDto.getCampaignId())) {
            log.warn("Hedef kampanyaya ait zaten URL kaydı mevcut. Kampanya ID: {}", campaignUrlIUDto.getCampaignId());
            throw new AlreadyExistsException("Bu kampanyaya ait zaten bir URL kaydı var. Kampanya ID: " + campaignUrlIUDto.getCampaignId());
        }

        log.debug("Kampanya URL güncelleniyor: {} -> {}", campaignUrl.getUrl(), campaignUrlIUDto.getUrl());
        campaignUrl.setCampaign(campaign);
        campaignUrl.setUrl(campaignUrlIUDto.getUrl());
        campaignUrl.setIsActive(campaignUrlIUDto.getIsActive());
        campaignUrl = campaignUrlRepository.save(campaignUrl);

        log.info("Kampanya URL başarıyla güncellendi. ID: {}, URL: {}", campaignUrl.getId(), campaignUrl.getUrl());
        return CampaignUrlDto.entityToDto(campaignUrl);
    }

    @Override
    public void delete(Long id) {
        log.info("Kampanya URL silme isteği başlatıldı. ID: {}", id);
        
        CampaignUrl campaignUrl = campaignUrlRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Silinecek kampanya URL bulunamadı. ID: {}", id);
                    return new CustomNotFoundException("Kampanya URL'si ID " + id + " bulunamadı");
                });

        campaignUrl.setIsActive(false);
        campaignUrlRepository.save(campaignUrl);
        log.info("Kampanya URL başarıyla silindi (deaktive edildi). ID: {}, URL: {}", campaignUrl.getId(), campaignUrl.getUrl());
    }

    /**
     * URL formatının geçerli olup olmadığını kontrol eder
     */
    private boolean isValidUrl(String url) {
        try {
            // Basit URL validation - http:// veya https:// ile başlamalı
            return url.toLowerCase().startsWith("http://") || url.toLowerCase().startsWith("https://");
        } catch (Exception e) {
            return false;
        }
    }
}
