package com.backend360.campaign_service.service.Impl;

import com.backend360.campaign_service.dto.EventDto;
import com.backend360.campaign_service.dto.EventIUDto;
import com.backend360.campaign_service.dto.PopularItemDto;
import com.backend360.campaign_service.model.Brand;
import com.backend360.campaign_service.model.Campaign;
import com.backend360.campaign_service.model.Event;
import com.backend360.campaign_service.repository.BrandRepository;
import com.backend360.campaign_service.repository.CampaignRepository;
import com.backend360.campaign_service.repository.EventRepository;
import com.backend360.campaign_service.service.IEventService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class EventServiceImpl implements IEventService {
    private final EventRepository eventRepository;
    private final BrandRepository brandRepository;
    private final CampaignRepository campaignRepository;

    @Override
    public EventDto createEvent(EventIUDto eventIUDto) {
        if (!"CAMPAIGN".equalsIgnoreCase(eventIUDto.getTargetType())) {
            return null;
        }
        Event event = convertToEntity(eventIUDto);
        Event savedEvent = eventRepository.save(event);
        return EventDto.entityToDto(savedEvent);
    }

    @Override
    public List<PopularItemDto> getPopularCampaigns() {
        List<Object[]> results = eventRepository.findPopularCampaigns();
        return results.stream()
            .map(obj -> new PopularItemDto((Long) obj[0], (Long) obj[1]))
            .collect(Collectors.toList());
    }

    @Override
    public List<PopularItemDto> getPopularBrands() {
        List<Object[]> results = eventRepository.findPopularBrands();
        return results.stream()
            .map(obj -> new PopularItemDto((Long) obj[0], (Long) obj[1]))
            .collect(Collectors.toList());
    }

    @Override
    public Long getClickCount(LocalDateTime startDate, LocalDateTime endDate) {
        return eventRepository.getClickCount(startDate, endDate);
    }

    @Override
    public List<PopularItemDto> getUserPopularCampaigns(String userId) {
        List<Object[]> results = eventRepository.findUserPopularCampaigns(userId);
        return results.stream()
            .map(obj -> new PopularItemDto((Long) obj[0], (Long) obj[1]))
            .collect(Collectors.toList());
    }

    @Override
    public List<PopularItemDto> getUserPopularBrands(String userId) {
        List<Object[]> results = eventRepository.findUserPopularBrands(userId);
        return results.stream()
            .map(obj -> new PopularItemDto((Long) obj[0], (Long) obj[1]))
            .collect(Collectors.toList());
    }

    private Event convertToEntity(EventIUDto dto) {
        Event.EventBuilder builder = Event.builder()
                .userId(dto.getUserId())
                .targetId(dto.getTargetId())
                .targetType(dto.getTargetType())
                .details(dto.getDetails());

        if (dto.getBrandId() != null) {
            builder.brand(brandRepository.findById(dto.getBrandId()).orElse(null));
        }
        if (dto.getCampaignId() != null) {
            builder.campaign(campaignRepository.findById(dto.getCampaignId()).orElse(null));
        }
        return builder.build();
    }
} 