package com.backend360.campaign_service.service.Impl;

import com.backend360.campaign_service.dto.CustomerToCampaignDto;
import com.backend360.campaign_service.dto.CustomerToCampaignIUDto;
import com.backend360.campaign_service.exception.AlreadyExistsException;
import com.backend360.campaign_service.exception.BadRequestException;
import com.backend360.campaign_service.exception.CustomNotFoundException;
import com.backend360.campaign_service.model.Campaign;
import com.backend360.campaign_service.model.Customer;
import com.backend360.campaign_service.model.CustomerToCampaign;
import com.backend360.campaign_service.repository.CampaignRepository;
import com.backend360.campaign_service.repository.CustomerRepository;
import com.backend360.campaign_service.repository.CustomerToCampaignRepository;
import com.backend360.campaign_service.service.ICustomerToCampaignService;
import com.backend360.campaign_service.service.CrmContactIntegrationService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CustomerToCampaignServiceImpl implements ICustomerToCampaignService {
    @Autowired
    private CustomerToCampaignRepository customerToCampaignRepository;

    @Autowired
    private CampaignRepository campaignRepository;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private EmailServiceImpl emailService;

    @Autowired
    private CrmContactIntegrationService crmContactIntegrationService;


    @Override
    public CustomerToCampaignDto save(CustomerToCampaignIUDto customerToCampaignIUDto) {
        if (customerToCampaignIUDto.getCampaignId() == null) {
            throw new BadRequestException("Kampanya ID boş olamaz.");
        }

        if (customerToCampaignIUDto.getCustomerId() == null) {
            throw new BadRequestException("Müşteri ID boş olamaz.");
        }

        Campaign campaign = campaignRepository.findById(customerToCampaignIUDto.getCampaignId())
                .orElseThrow(() -> new CustomNotFoundException("Kampanya ID " + customerToCampaignIUDto.getCampaignId() + " bulunamadı"));

        Customer customer = customerRepository.findById(customerToCampaignIUDto.getCustomerId())
                .orElseThrow(() -> new CustomNotFoundException("Müşteri ID " + customerToCampaignIUDto.getCustomerId() + " bulunamadı"));

        // Aynı müşteri ve kampanya kombinasyonunun zaten var olup olmadığını kontrol et
        if (customerToCampaignRepository.existsByCampaignIdAndCustomerId(customerToCampaignIUDto.getCampaignId(), customerToCampaignIUDto.getCustomerId())) {
            throw new AlreadyExistsException("Bu müşteri ve kampanya kombinasyonu zaten kayıtlı.");
        }

        CustomerToCampaign customerToCampaign = CustomerToCampaign.builder()
                .campaign(campaign)
                .customer(customer)
                .isCalled(customerToCampaignIUDto.getIsCalled())
                .isActive(customerToCampaignIUDto.getIsActive())
                .createdAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")))
                .updatedAt(null)
                .contactPhone(customerToCampaignIUDto.getContactPhone())
                .contactEmail(customerToCampaignIUDto.getContactEmail())
                .contactName(customerToCampaignIUDto.getContactName())
                .contactSurname(customerToCampaignIUDto.getContactSurname())
                .build();

        CustomerToCampaign saved = customerToCampaignRepository.save(customerToCampaign);

        // Email gönderimi (kayıt BAŞARILIYSA)
        emailService.sendCustomerToCampaignMail(customer, campaign);

        // CRM'e gönder
        try {
            crmContactIntegrationService.sendCustomerToCrm(CustomerToCampaignDto.entityToDto(saved));
        } catch (Exception e) {
            log.error("Failed to send customer to CRM: {}", e.getMessage());
            // CRM hatası ana işlemi etkilemesin
        }

        return CustomerToCampaignDto.entityToDto(saved);
    }

    @Override
    public List<CustomerToCampaignDto> findAll() {
        List<CustomerToCampaign> customerToCampaigns = customerToCampaignRepository.findAll();

        return customerToCampaigns.stream()
                .map(CustomerToCampaignDto::entityToDto)
                .collect(Collectors.toList());
    }

    @Override
    public CustomerToCampaignDto findById(Long id) {
        CustomerToCampaign customerToCampaign = customerToCampaignRepository.findById(id)
                .orElseThrow(() -> new CustomNotFoundException("Müşteri-Kampanya ilişkisi ID " + id + " bulunamadı"));

        return CustomerToCampaignDto.entityToDto(customerToCampaign);
    }

    @Override
    public CustomerToCampaignDto update(Long id, CustomerToCampaignIUDto customerToCampaignIUDto) {
        if (customerToCampaignIUDto.getCampaignId() == null) {
            throw new BadRequestException("Kampanya ID boş olamaz.");
        }

        if (customerToCampaignIUDto.getCustomerId() == null) {
            throw new BadRequestException("Müşteri ID boş olamaz.");
        }

        CustomerToCampaign customerToCampaign = customerToCampaignRepository.findById(id)
                .orElseThrow(() -> new CustomNotFoundException("Müşteri-Kampanya ilişkisi ID " + id + " bulunamadı"));

        Campaign campaign = campaignRepository.findById(customerToCampaignIUDto.getCampaignId())
                .orElseThrow(() -> new CustomNotFoundException("Kampanya ID " + customerToCampaignIUDto.getCampaignId() + " bulunamadı"));

        Customer customer = customerRepository.findById(customerToCampaignIUDto.getCustomerId())
                .orElseThrow(() -> new CustomNotFoundException("Müşteri ID " + customerToCampaignIUDto.getCustomerId() + " bulunamadı"));

        // Güncelleme sırasında farklı bir kayıtta aynı kombinasyonun var olup olmadığını kontrol et
        if (!customerToCampaign.getCampaign().getId().equals(customerToCampaignIUDto.getCampaignId()) ||
                !customerToCampaign.getCustomer().getId().equals(customerToCampaignIUDto.getCustomerId())) {
            if (customerToCampaignRepository.existsByCampaignIdAndCustomerId(customerToCampaignIUDto.getCampaignId(), customerToCampaignIUDto.getCustomerId())) {
                throw new AlreadyExistsException("Bu müşteri ve kampanya kombinasyonu zaten kayıtlı.");
            }
        }

        customerToCampaign.setCampaign(campaign);
        customerToCampaign.setCustomer(customer);
        customerToCampaign.setIsCalled(customerToCampaignIUDto.getIsCalled());
        customerToCampaign.setIsActive(customerToCampaignIUDto.getIsActive());
        customerToCampaign.setUpdatedAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")));
        customerToCampaign.setContactPhone(customerToCampaignIUDto.getContactPhone());
        customerToCampaign.setContactEmail(customerToCampaignIUDto.getContactEmail());
        customerToCampaign.setContactName(customerToCampaignIUDto.getContactName());
        customerToCampaign.setContactSurname(customerToCampaignIUDto.getContactSurname());
        customerToCampaign = customerToCampaignRepository.save(customerToCampaign);

        return CustomerToCampaignDto.entityToDto(customerToCampaign);
    }

    @Override
    public void changeIsCalledStatus(Long id, Boolean status) {
        if (status == null) {
            throw new BadRequestException("Arama durumu bilgisi boş olamaz.");
        }

        CustomerToCampaign customerToCampaign = customerToCampaignRepository.findById(id)
                .orElseThrow(() -> new CustomNotFoundException("Müşteri-Kampanya ilişkisi ID " + id + " bulunamadı"));

        customerToCampaign.setUpdatedAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")));
        customerToCampaign.setIsCalled(status);
        customerToCampaignRepository.save(customerToCampaign);
    }

    @Override
    public void delete(Long id) {
        CustomerToCampaign customerToCampaign = customerToCampaignRepository.findById(id)
                .orElseThrow(() -> new CustomNotFoundException("Müşteri-Kampanya ilişkisi ID " + id + " bulunamadı"));

        customerToCampaign.setIsActive(false);
        customerToCampaign.setUpdatedAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")));
        customerToCampaignRepository.save(customerToCampaign);
    }

    @Override
    public boolean existsByCustomerIdAndCampaignIdAndIsActive(Long customerId, Long campaignId, Boolean isActive) {
        return customerToCampaignRepository.existsByCustomer_IdAndCampaign_IdAndIsActive(customerId, campaignId, isActive);
    }

    @Override
    public CustomerToCampaignDto findByCustomerIdAndCampaignIdAndIsActive(Long customerId, Long campaignId, Boolean isActive) {
        CustomerToCampaign entity = customerToCampaignRepository.findByCustomer_IdAndCampaign_IdAndIsActive(customerId, campaignId, isActive)
                .orElseThrow(() -> new CustomNotFoundException("Customer-Campaign ilişkisi bulunamadı. CustomerId: " + customerId + ", CampaignId: " + campaignId));
        return CustomerToCampaignDto.entityToDto(entity);
    }
}
