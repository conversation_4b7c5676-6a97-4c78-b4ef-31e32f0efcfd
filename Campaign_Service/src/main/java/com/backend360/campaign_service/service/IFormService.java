package com.backend360.campaign_service.service;

import com.backend360.campaign_service.dto.FormDto;
import com.backend360.campaign_service.dto.FormIUDto;

import java.util.List;

public interface IFormService {
    FormDto save(FormIUDto formIUDto);

    List<FormDto> findAll();

    FormDto findById(Long id);

    FormDto update(Long id, FormIUDto formIUDto);

    void delete(Long id);

    boolean existsByEmail(String email);

    // Public form submission için yeni metodlar
    boolean existsByEmailAndIsActive(String email, Boolean isActive);
    FormDto findByEmailAndIsActive(String email, Boolean isActive);
}
