package com.backend360.campaign_service.service.Impl;

import com.backend360.campaign_service.dto.CampaignToImageDto;
import com.backend360.campaign_service.dto.CampaignToImageIUDto;
import com.backend360.campaign_service.exception.AlreadyExistsException;
import com.backend360.campaign_service.exception.BadRequestException;
import com.backend360.campaign_service.exception.CustomNotFoundException;
import com.backend360.campaign_service.manager.ImageFeignManager;
import com.backend360.campaign_service.model.Campaign;
import com.backend360.campaign_service.model.CampaignToImage;
import com.backend360.campaign_service.repository.CampaignRepository;
import com.backend360.campaign_service.repository.CampaignToImageRepository;
import com.backend360.campaign_service.service.ICampaignToImageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CampaignToImageServiceImpl implements ICampaignToImageService {

    @Autowired
    private CampaignToImageRepository campaignToImageRepository;

    @Autowired
    private CampaignRepository campaignRepository;

    @Autowired
    private ImageFeignManager imageFeignManager;

    @Value("${file.upload-dir.campaigns}")
    private String campaignImagesPath;

    @Override
    public CampaignToImageDto uploadImage(Long campaignId, Boolean isShowcase, MultipartFile imageFile) throws IOException {
        if (campaignId == null) {
            throw new BadRequestException("Kampanya ID boş olamaz.");
        }

        if (imageFile == null || imageFile.isEmpty()) {
            throw new BadRequestException("Görsel dosyası eklenmedi.");
        }

        if (isShowcase == null) {
            throw new BadRequestException("Showcase bilgisi belirtilmeli.");
        }

        Campaign campaign = campaignRepository.findById(campaignId)
                .orElseThrow(() -> new CustomNotFoundException("Kampanya ID " + campaignId + " bulunamadı"));

        // Showcase zaten varsa hata fırlat
        if (isShowcase) {
            Optional<CampaignToImage> existingShowcase = campaignToImageRepository
                    .findByCampaign_IdAndIsShowcaseTrueAndIsActiveTrue(campaignId);

            if (existingShowcase.isPresent()) {
                throw new AlreadyExistsException("Bu kampanyaya ait zaten bir showcase görseli var!");
            }
        }

        ResponseEntity<Map<String, Object>> response = imageFeignManager.uploadImage(campaignImagesPath, imageFile);

        if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
            Map<String, Object> imageData = response.getBody();

            String imagePath = imageData.get("path").toString();
            String imageName = imageData.get("filename").toString();

            CampaignToImage campaignToImage = CampaignToImage.builder()
                    .campaign(campaign)
                    .imagePath(imagePath)
                    .imageName(imageName)
                    .isShowcase(isShowcase)
                    .isActive(true)
                    .build();

            return CampaignToImageDto.entityToDto(campaignToImageRepository.save(campaignToImage));
        } else {
            throw new BadRequestException("Resim yükleme başarısız: " + response.getStatusCode());
        }
    }

    @Override
    public List<CampaignToImageDto> uploadMultipleDetailImages(Long campaignId, MultipartFile[] imageFiles) throws IOException {
        if (campaignId == null) {
            throw new BadRequestException("Kampanya ID boş olamaz.");
        }

        if (imageFiles == null || imageFiles.length == 0) {
            throw new BadRequestException("Yüklenecek görsel dosyası bulunamadı.");
        }

        // Kampanyanın varlığını kontrol et
        if (!campaignRepository.existsById(campaignId)) {
            throw new CustomNotFoundException("Kampanya ID " + campaignId + " bulunamadı");
        }

        List<CampaignToImageDto> result = new ArrayList<>();

        for (MultipartFile file : imageFiles) {
            if (file != null && !file.isEmpty()) {
                CampaignToImageDto dto = uploadImage(campaignId, false, file); // Detay olarak ekle
                result.add(dto);
            }
        }

        if (result.isEmpty()) {
            throw new BadRequestException("Geçerli görsel dosyası bulunamadı.");
        }

        return result;
    }

    @Override
    public List<CampaignToImageDto> findAll() {
        List<CampaignToImage> campaignToImages = campaignToImageRepository.findAllByIsActiveTrue();

        return campaignToImages.stream()
                .map(CampaignToImageDto::entityToDto)
                .collect(Collectors.toList());
    }

    @Override
    public CampaignToImageDto findById(Long id) {
        CampaignToImage campaignToImage = campaignToImageRepository.findById(id)
                .orElseThrow(() -> new CustomNotFoundException("Kampanya görseli ID " + id + " bulunamadı"));

        return CampaignToImageDto.entityToDto(campaignToImage);
    }

    @Override
    public CampaignToImageDto update(Long id, CampaignToImageIUDto campaignToImageIUDto) {
        if (campaignToImageIUDto.getCampaignId() == null) {
            throw new BadRequestException("Kampanya ID boş olamaz.");
        }

        Campaign campaign = campaignRepository.findById(campaignToImageIUDto.getCampaignId())
                .orElseThrow(() -> new CustomNotFoundException("Kampanya ID " + campaignToImageIUDto.getCampaignId() + " bulunamadı"));

        CampaignToImage campaignToImage = campaignToImageRepository.findById(id)
                .orElseThrow(() -> new CustomNotFoundException("Kampanya görseli ID " + id + " bulunamadı"));

        // Showcase kontrolü
        if (campaignToImageIUDto.getIsShowcase() != null && campaignToImageIUDto.getIsShowcase()) {
            Optional<CampaignToImage> existingShowcase = campaignToImageRepository
                    .findByCampaign_IdAndIsShowcaseTrueAndIsActiveTrue(campaign.getId());

            if (existingShowcase.isPresent() && !existingShowcase.get().getId().equals(campaignToImage.getId())) {
                throw new AlreadyExistsException("Bu kampanyaya ait zaten bir showcase görseli var!");
            }
        }

        campaignToImage.setCampaign(campaign);
        if (campaignToImageIUDto.getImageName() != null) {
            campaignToImage.setImageName(campaignToImageIUDto.getImageName());
        }
        if (campaignToImageIUDto.getImagePath() != null) {
            campaignToImage.setImagePath(campaignToImageIUDto.getImagePath());
        }
        if (campaignToImageIUDto.getIsShowcase() != null) {
            campaignToImage.setIsShowcase(campaignToImageIUDto.getIsShowcase());
        }
        campaignToImage.setIsActive(true);

        return CampaignToImageDto.entityToDto(campaignToImageRepository.save(campaignToImage));
    }

    @Override
    public void delete(Long id) {
        CampaignToImage campaignToImage = campaignToImageRepository.findById(id)
                .orElseThrow(() -> new CustomNotFoundException("Kampanya görseli ID " + id + " bulunamadı"));

        // Dosya adını çıkar
        String imagePath = campaignToImage.getImagePath();
        if (imagePath != null && !imagePath.isEmpty()) {
            String fileName = imagePath.substring(imagePath.lastIndexOf("/") + 1);
            try {
                imageFeignManager.deleteImage(fileName);
            } catch (Exception e) {
                System.err.println("Görsel silinemedi: " + fileName);
            }
        }

        campaignToImage.setIsActive(false);
        campaignToImageRepository.save(campaignToImage);
    }

    @Override
    public Optional<CampaignToImageDto> getShowcaseImageByCampaignId(Long campaignId) {
        if (campaignId == null) {
            throw new BadRequestException("Kampanya ID boş olamaz.");
        }

        return campaignToImageRepository
                .findByCampaign_IdAndIsShowcaseTrueAndIsActiveTrue(campaignId)
                .map(CampaignToImageDto::entityToDto);
    }

    @Override
    public List<CampaignToImageDto> getDetailImagesByCampaignId(Long campaignId) {
        if (campaignId == null) {
            throw new BadRequestException("Kampanya ID boş olamaz.");
        }

        return campaignToImageRepository
                .findAllByCampaign_IdAndIsShowcaseFalseAndIsActiveTrue(campaignId)
                .stream()
                .map(CampaignToImageDto::entityToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<CampaignToImageDto> getAllImagesByCampaignId(Long campaignId) {
        if (campaignId == null) {
            throw new BadRequestException("Kampanya ID boş olamaz.");
        }

        return campaignToImageRepository
                .findAllByCampaign_IdAndIsActiveTrue(campaignId)
                .stream()
                .map(CampaignToImageDto::entityToDto)
                .collect(Collectors.toList());
    }

    @Override
    public CampaignToImageDto updateImageFile(Long id, MultipartFile imageFile) throws IOException {
        if (imageFile == null || imageFile.isEmpty()) {
            throw new BadRequestException("Yeni görsel dosyası eklenmedi.");
        }

        CampaignToImage campaignToImage = campaignToImageRepository.findById(id)
                .orElseThrow(() -> new CustomNotFoundException("Kampanya görseli ID " + id + " bulunamadı"));

        // Unique image name kontrolü: Aynı campaign için aynı isimde başka bir görsel var mı?
        String newFileName = imageFile.getOriginalFilename();
        if (newFileName != null && !newFileName.isBlank()
            && !newFileName.equals(campaignToImage.getImageName())
            && campaignToImageRepository.existsByCampaignIdAndImageName(campaignToImage.getCampaign().getId(), newFileName)) {
            throw new AlreadyExistsException("Bu kampanya için bu isimde bir görsel zaten kayıtlı: " + newFileName);
        }

        // Eski görseli sil
        String oldImagePath = campaignToImage.getImagePath();
        if (oldImagePath != null && !oldImagePath.isEmpty()) {
            String oldFileName = oldImagePath.substring(oldImagePath.lastIndexOf("/") + 1);
            try {
                imageFeignManager.deleteImage(oldFileName);
            } catch (Exception e) {
                System.err.println("Önceki görsel silinemedi: " + oldFileName);
            }
        }

        // Yeni görseli yükle
        ResponseEntity<Map<String, Object>> response = imageFeignManager.uploadImage(campaignImagesPath, imageFile);
        if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
            Map<String, Object> imageData = response.getBody();
            campaignToImage.setImagePath(imageData.get("path").toString());
            campaignToImage.setImageName(imageData.get("filename").toString());

            return CampaignToImageDto.entityToDto(campaignToImageRepository.save(campaignToImage));
        } else {
            throw new BadRequestException("Resim yükleme başarısız: " + response.getStatusCode());
        }
    }

    @Override
    public CampaignToImageDto updateShowcaseImageByCampaignId(Long campaignId, MultipartFile imageFile) throws IOException {
        if (campaignId == null) {
            throw new BadRequestException("Kampanya ID boş olamaz.");
        }

        if (imageFile == null || imageFile.isEmpty()) {
            throw new BadRequestException("Yeni showcase görseli eklenmedi.");
        }

        // Kampanyanın varlığını kontrol et
        if (!campaignRepository.existsById(campaignId)) {
            throw new CustomNotFoundException("Kampanya ID " + campaignId + " bulunamadı");
        }

        Optional<CampaignToImage> showcaseOpt = campaignToImageRepository
                .findByCampaign_IdAndIsShowcaseTrueAndIsActiveTrue(campaignId);

        // Mevcut showcase varsa önce fiziksel görseli sil
        if (showcaseOpt.isPresent()) {
            CampaignToImage existing = showcaseOpt.get();

            String oldImagePath = existing.getImagePath();
            if (oldImagePath != null && !oldImagePath.isEmpty()) {
                String fileName = oldImagePath.substring(oldImagePath.lastIndexOf("/") + 1);
                try {
                    imageFeignManager.deleteImage(fileName);
                } catch (Exception e) {
                    System.err.println("Eski showcase görseli silinemedi: " + fileName);
                }
            }

            // DB kaydını soft-delete
            existing.setIsActive(false);
            campaignToImageRepository.save(existing);
        }

        // Yeni showcase ekle
        return uploadImage(campaignId, true, imageFile);
    }
}
