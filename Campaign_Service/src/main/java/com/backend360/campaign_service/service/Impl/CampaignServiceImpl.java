package com.backend360.campaign_service.service.Impl;

import com.backend360.campaign_service.dto.CampaignDto;
import com.backend360.campaign_service.dto.CampaignIUDto;
import com.backend360.campaign_service.exception.AlreadyExistsException;
import com.backend360.campaign_service.exception.BadRequestException;
import com.backend360.campaign_service.exception.CustomNotFoundException;
import com.backend360.campaign_service.kafka.KafkaProducer;
import com.backend360.campaign_service.manager.ImageFeignManager;
import com.backend360.campaign_service.model.Campaign;
import com.backend360.campaign_service.model.Category;
import com.backend360.campaign_service.model.Customer;
import com.backend360.campaign_service.repository.CampaignRepository;
import com.backend360.campaign_service.repository.CategoryRepository;
import com.backend360.campaign_service.repository.CustomerRepository;
import com.backend360.campaign_service.service.ICampaignService;
import com.backend360.campaign_service.service.Impl.EmailServiceImpl;
import com.backend360.campaign_service.service.CrmCampaignIntegration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CampaignServiceImpl implements ICampaignService {
    @Autowired
    private CampaignRepository campaignRepository;

    @Autowired
    private CategoryRepository categoryRepository;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private ImageFeignManager imageFeignManager;

    @Autowired
    private EmailServiceImpl emailService;

    @Autowired
    private KafkaProducer kafkaProducer;

    @Autowired
    private CrmCampaignIntegration crmCampaignIntegration;

    @Override
    public CampaignDto save(CampaignIUDto campaignIUDto) {
        log.info("Kampanya kaydetme isteği başlatıldı: {}", campaignIUDto.getName());
        
        // Input validasyonları
        if (campaignIUDto.getName() == null || campaignIUDto.getName().isBlank()) {
            log.warn("Kampanya adı boş gönderildi");
            throw new BadRequestException("Kampanya adı boş olamaz.");
        }

        if (campaignIUDto.getTitle() == null || campaignIUDto.getTitle().isBlank()) {
            log.warn("Kampanya başlığı boş gönderildi");
            throw new BadRequestException("Kampanya başlığı boş olamaz.");
        }

        if (campaignIUDto.getCategoryId() == null) {
            log.warn("Kategori ID boş gönderildi");
            throw new BadRequestException("Kategori ID boş olamaz.");
        }

        if (campaignIUDto.getStartDate() == null) {
            log.warn("Başlangıç tarihi boş gönderildi");
            throw new BadRequestException("Başlangıç tarihi boş olamaz.");
        }

        if (campaignIUDto.getEndDate() == null) {
            log.warn("Bitiş tarihi boş gönderildi");
            throw new BadRequestException("Bitiş tarihi boş olamaz.");
        }

        // Tarihlerin mantıklı olup olmadığını kontrol et
        if (campaignIUDto.getStartDate().isAfter(campaignIUDto.getEndDate())) {
            log.warn("Geçersiz tarih aralığı: başlangıç {} bitiş {}", campaignIUDto.getStartDate(), campaignIUDto.getEndDate());
            throw new BadRequestException("Başlangıç tarihi bitiş tarihinden sonra olamaz.");
        }

        // Kampanya adının zaten var olup olmadığını kontrol et
        if (campaignRepository.existsByName(campaignIUDto.getName())) {
            log.warn("Mevcut kampanya adı ile kayıt girişimi: {}", campaignIUDto.getName());
            throw new AlreadyExistsException("Bu kampanya adı zaten kayıtlı: " + campaignIUDto.getName());
        }

        Category category = categoryRepository.findById(campaignIUDto.getCategoryId())
                .orElseThrow(() -> {
                    log.warn("Kategori bulunamadı. ID: {}", campaignIUDto.getCategoryId());
                    return new CustomNotFoundException("Kategori ID " + campaignIUDto.getCategoryId() + " bulunamadı");
                });

        Campaign campaign = Campaign.builder()
                .name(campaignIUDto.getName())
                .category(category)
                .title(campaignIUDto.getTitle())
                .description(campaignIUDto.getDescription())
                .title2(campaignIUDto.getTitle2())
                .description2(campaignIUDto.getDescription2())
                .title3(campaignIUDto.getTitle3())
                .description3(campaignIUDto.getDescription3())
                .details(campaignIUDto.getDetails())
                .startDate(campaignIUDto.getStartDate())
                .endDate(campaignIUDto.getEndDate())
                .isActive(campaignIUDto.getIsActive())
                .createdAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")))
                .updatedAt(null)
                .build();
        
        Campaign savedCampaign = campaignRepository.save(campaign);
        log.info("Kampanya başarıyla kaydedildi. ID: {}, Ad: {}", savedCampaign.getId(), savedCampaign.getName());
        
        // Kampanya DTO'sunu oluştur
        CampaignDto campaignDto = CampaignDto.entityToDto(savedCampaign);
        
        // CRM'e kampanya gönder (asynchronous)
        try {
            crmCampaignIntegration.sendCampaignToCrm(savedCampaign);
            log.info("CRM'e kampanya gönderildi. Kampanya: {}", savedCampaign.getName());
        } catch (Exception e) {
            log.error("CRM'e kampanya gönderilirken hata oluştu: {}", e.getMessage(), e);
            // CRM hatası kampanya oluşturma işlemini etkilememeli
        }
        
        // Kafka'ya kampanya oluşturma eventi gönder (asynchronous)
        try {
            kafkaProducer.sendCampaignCreatedEvent(campaignDto);
            log.info("Kafka'ya kampanya oluşturma eventi gönderildi. Kampanya: {}", savedCampaign.getName());
        } catch (Exception e) {
            log.error("Kafka event gönderilirken hata oluştu: {}", e.getMessage(), e);
            // Kafka hatası kampanya oluşturma işlemini etkilememeli
        }
        
        return campaignDto;
    }

    @Override
    public List<CampaignDto> findAll() {
        List<Campaign> campaigns = campaignRepository.findAll();
        log.debug("Toplam {} kampanya bulundu", campaigns.size());

        return campaigns.stream()
                .map(CampaignDto::entityToDto)
                .collect(Collectors.toList());
    }

    @Override
    public CampaignDto findById(Long id) {
        log.info("Kampanya aranıyor. ID: {}", id);
        Campaign campaign = campaignRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Kampanya bulunamadı. ID: {}", id);
                    return new CustomNotFoundException("Kampanya ID " + id + " bulunamadı");
                });
        log.debug("Kampanya bulundu: {}", campaign.getName());
        return CampaignDto.entityToDto(campaign);
    }

    @Override
    public CampaignDto update(Long id, CampaignIUDto campaignIUDto) {
        log.info("Kampanya güncelleme isteği başlatıldı. ID: {}", id);
        
        // Input validasyonları
        if (campaignIUDto.getName() == null || campaignIUDto.getName().isBlank()) {
            log.warn("Kampanya adı boş gönderildi");
            throw new BadRequestException("Kampanya adı boş olamaz.");
        }

        if (campaignIUDto.getTitle() == null || campaignIUDto.getTitle().isBlank()) {
            log.warn("Kampanya başlığı boş gönderildi");
            throw new BadRequestException("Kampanya başlığı boş olamaz.");
        }

        if (campaignIUDto.getCategoryId() == null) {
            log.warn("Kategori ID boş gönderildi");
            throw new BadRequestException("Kategori ID boş olamaz.");
        }

        if (campaignIUDto.getStartDate() == null) {
            log.warn("Başlangıç tarihi boş gönderildi");
            throw new BadRequestException("Başlangıç tarihi boş olamaz.");
        }

        if (campaignIUDto.getEndDate() == null) {
            log.warn("Bitiş tarihi boş gönderildi");
            throw new BadRequestException("Bitiş tarihi boş olamaz.");
        }

        // Tarihlerin mantıklı olup olmadığını kontrol et
        if (campaignIUDto.getStartDate().isAfter(campaignIUDto.getEndDate())) {
            log.warn("Geçersiz tarih aralığı: başlangıç {} bitiş {}", campaignIUDto.getStartDate(), campaignIUDto.getEndDate());
            throw new BadRequestException("Başlangıç tarihi bitiş tarihinden sonra olamaz.");
        }

        Campaign campaign = campaignRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Güncellenecek kampanya bulunamadı. ID: {}", id);
                    return new CustomNotFoundException("Kampanya ID " + id + " bulunamadı");
                });

        // Kampanya adının başka bir kayıtta zaten var olup olmadığını kontrol et
        if (!campaign.getName().equals(campaignIUDto.getName()) && 
            campaignRepository.existsByName(campaignIUDto.getName())) {
            log.warn("Mevcut kampanya adı ile güncelleme girişimi: {}", campaignIUDto.getName());
            throw new AlreadyExistsException("Bu kampanya adı zaten kayıtlı: " + campaignIUDto.getName());
        }

        Category category = categoryRepository.findById(campaignIUDto.getCategoryId())
                .orElseThrow(() -> {
                    log.warn("Kategori bulunamadı. ID: {}", campaignIUDto.getCategoryId());
                    return new CustomNotFoundException("Kategori ID " + campaignIUDto.getCategoryId() + " bulunamadı");
                });

        log.debug("Kampanya güncelleniyor: {} -> {}", campaign.getName(), campaignIUDto.getName());
        campaign.setName(campaignIUDto.getName());
        campaign.setCategory(category);
        campaign.setTitle(campaignIUDto.getTitle());
        campaign.setDescription(campaignIUDto.getDescription());
        campaign.setTitle2(campaignIUDto.getTitle2());
        campaign.setDescription2(campaignIUDto.getDescription2());
        campaign.setTitle3(campaignIUDto.getTitle3());
        campaign.setDescription3(campaignIUDto.getDescription3());
        campaign.setDetails(campaignIUDto.getDetails());
        campaign.setStartDate(campaignIUDto.getStartDate());
        campaign.setEndDate(campaignIUDto.getEndDate());
        campaign.setIsActive(campaignIUDto.getIsActive());
        campaign.setUpdatedAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")));
        campaign = campaignRepository.save(campaign);

        log.info("Kampanya başarıyla güncellendi. ID: {}, Ad: {}", campaign.getId(), campaign.getName());
        return CampaignDto.entityToDto(campaign);
    }

    @Override
    public void delete(Long id) {
        log.info("Kampanya silme isteği başlatıldı. ID: {}", id);
        
        Campaign campaign = campaignRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Silinecek kampanya bulunamadı. ID: {}", id);
                    return new CustomNotFoundException("Kampanya ID " + id + " bulunamadı");
                });

        campaign.setIsActive(false);
        campaign.setUpdatedAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")));
        campaignRepository.save(campaign);
        log.info("Kampanya başarıyla silindi (deaktive edildi). ID: {}, Ad: {}", campaign.getId(), campaign.getName());
    }

    @Override
    public List<String> getRemindMeCustomerEmails() {
        log.info("RemindMe müşterilerinin email listesi alınıyor");
        
        List<Customer> remindMeCustomers = customerRepository.findByRemindMeTrueAndIsActiveTrue();
        
        List<String> emails = remindMeCustomers.stream()
                .map(Customer::getEmail)
                .toList();
        
        log.info("Toplam {} remindMe müşterisi bulundu", emails.size());
        
        return emails;
    }
}
