package com.backend360.campaign_service.service.Impl;

import com.backend360.campaign_service.dto.JobDto;
import com.backend360.campaign_service.dto.JobIUDto;
import com.backend360.campaign_service.exception.AlreadyExistsException;
import com.backend360.campaign_service.exception.BadRequestException;
import com.backend360.campaign_service.exception.CustomNotFoundException;
import com.backend360.campaign_service.model.Job;
import com.backend360.campaign_service.repository.JobRepository;
import com.backend360.campaign_service.service.IJobService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class JobServiceImpl implements IJobService {
    @Autowired
    private JobRepository jobRepository;

    @Override
    public JobDto save(JobIUDto jobIUDto) {
        log.info("Meslek kaydetme isteği başlatıldı: {}", jobIUDto.getDescription());
        
        if (jobIUDto.getDescription() == null || jobIUDto.getDescription().isBlank()) {
            log.warn("Meslek açıklaması boş gönderildi");
            throw new BadRequestException("Meslek açıklaması boş olamaz.");
        }

        if (jobRepository.existsByDescription(jobIUDto.getDescription())) {
            log.warn("Mevcut meslek açıklaması ile kayıt girişimi: {}", jobIUDto.getDescription());
            throw new AlreadyExistsException("Bu meslek zaten kayıtlı: " + jobIUDto.getDescription());
        }

        Job job = Job.builder()
                .description(jobIUDto.getDescription())
                .isActive(jobIUDto.getIsActive())
                .build();

        Job savedJob = jobRepository.save(job);
        log.info("Meslek başarıyla kaydedildi. ID: {}, Açıklama: {}", savedJob.getId(), savedJob.getDescription());
        
        return JobDto.entityToDto(savedJob);
    }

    @Override
    public List<JobDto> findAll() {
        log.info("Tüm meslekler listeleniyor");
        List<Job> jobs = jobRepository.findAll();
        log.debug("Toplam {} meslek bulundu", jobs.size());

        return jobs.stream()
                .map(JobDto::entityToDto)
                .collect(Collectors.toList());
    }

    @Override
    public JobDto findById(Long id) {
        log.info("Meslek aranıyor. ID: {}", id);
        Job job = jobRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Meslek bulunamadı. ID: {}", id);
                    return new CustomNotFoundException("Meslek ID " + id + " bulunamadı");
                });
        log.debug("Meslek bulundu: {}", job.getDescription());
        return JobDto.entityToDto(job);
    }

    @Override
    public JobDto update(Long id, JobIUDto jobIUDto) {
        log.info("Meslek güncelleme isteği başlatıldı. ID: {}", id);
        
        if (jobIUDto.getDescription() == null || jobIUDto.getDescription().isBlank()) {
            log.warn("Meslek açıklaması boş gönderildi");
            throw new BadRequestException("Meslek açıklaması boş olamaz.");
        }

        Job job = jobRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Güncellenecek meslek bulunamadı. ID: {}", id);
                    return new CustomNotFoundException("Meslek ID " + id + " bulunamadı");
                });

        if (!job.getDescription().equals(jobIUDto.getDescription()) && 
            jobRepository.existsByDescription(jobIUDto.getDescription())) {
            log.warn("Mevcut meslek açıklaması ile güncelleme girişimi: {}", jobIUDto.getDescription());
            throw new AlreadyExistsException("Bu meslek zaten kayıtlı: " + jobIUDto.getDescription());
        }

        log.debug("Meslek açıklaması güncelleniyor: {} -> {}", job.getDescription(), jobIUDto.getDescription());
        job.setDescription(jobIUDto.getDescription());
        job.setActive(jobIUDto.getIsActive());
        job = jobRepository.save(job);
        
        log.info("Meslek başarıyla güncellendi. ID: {}, Açıklama: {}", job.getId(), job.getDescription());
        return JobDto.entityToDto(job);
    }

    @Override
    public void delete(Long id) {
        log.info("Meslek silme isteği başlatıldı. ID: {}", id);
        
        Job job = jobRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Silinecek meslek bulunamadı. ID: {}", id);
                    return new CustomNotFoundException("Meslek ID " + id + " bulunamadı");
                });

        job.setActive(false);
        jobRepository.save(job);
        log.info("Meslek başarıyla silindi (deaktive edildi). ID: {}, Açıklama: {}", job.getId(), job.getDescription());
    }
}
