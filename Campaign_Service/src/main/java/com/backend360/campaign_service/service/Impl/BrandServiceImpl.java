package com.backend360.campaign_service.service.Impl;

import com.backend360.campaign_service.dto.BrandDto;
import com.backend360.campaign_service.dto.BrandIUDto;
import com.backend360.campaign_service.exception.AlreadyExistsException;
import com.backend360.campaign_service.exception.BadRequestException;
import com.backend360.campaign_service.exception.CustomNotFoundException;
import com.backend360.campaign_service.manager.ImageFeignManager;
import com.backend360.campaign_service.model.Brand;
import com.backend360.campaign_service.repository.BrandRepository;
import com.backend360.campaign_service.service.IBrandService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BrandServiceImpl implements IBrandService {

    @Autowired
    private BrandRepository brandRepository;

    @Autowired
    private ImageFeignManager imageFeignManager;

    @Value("${file.upload-dir.brands}")
    private String brandsUploadPath;

    @Override
    public BrandDto save(BrandIUDto brandIUDto, MultipartFile imageFile) throws IOException {
        log.info("Brand kaydetme isteği başlatıldı: {}", brandIUDto.getName());
        
        if (brandIUDto.getName() == null || brandIUDto.getName().isBlank()) {
            log.warn("Brand adı boş gönderildi");
            throw new BadRequestException("Brand adı boş olamaz.");
        }

        if (brandRepository.existsByName(brandIUDto.getName())) {
            log.warn("Mevcut brand adı ile kayıt girişimi: {}", brandIUDto.getName());
            throw new AlreadyExistsException("Bu marka adı zaten kayıtlı: " + brandIUDto.getName());
        }

        if (imageFile == null || imageFile.isEmpty()) {
            log.warn("Brand resmi olmadan kayıt girişimi");
            throw new BadRequestException("Marka resmi eklenmedi.");
        }

        String imagePath = null;
        if (imageFile != null && !imageFile.isEmpty()) {
            log.debug("Brand resmi yükleniyor: {}", imageFile.getOriginalFilename());
            ResponseEntity<Map<String, Object>> response = imageFeignManager.uploadImage(brandsUploadPath, imageFile);
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> imageData = response.getBody();
                imagePath = imageData.get("path").toString();
                log.debug("Brand resmi başarıyla yüklendi: {}", imagePath);
            } else {
                log.error("Brand resmi yükleme başarısız: {}", response);
                throw new BadRequestException("Resim yükleme başarısız: " + response);
            }
        }

        Brand brand = Brand.builder()
                .name(brandIUDto.getName())
                .countryCode(brandIUDto.getCountryCode())
                .brandUrl(brandIUDto.getBrandUrl())
                .imagePath(imagePath)
                .isActive(brandIUDto.getIsActive())
                .createdAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")))
                .build();

        Brand savedBrand = brandRepository.save(brand);
        log.info("Brand başarıyla kaydedildi. ID: {}, Ad: {}", savedBrand.getId(), savedBrand.getName());
        
        return BrandDto.entityToDto(savedBrand);
    }

    @Override
    public List<BrandDto> findAll() {
        log.info("Tüm brandlar listeleniyor");
        List<Brand> brands = brandRepository.findAll();
        log.debug("Toplam {} brand bulundu", brands.size());

        return brands.stream()
                .map(BrandDto::entityToDto)
                .collect(Collectors.toList());
    }

    @Override
    public BrandDto findById(Long id) {
        log.info("Brand aranıyor. ID: {}", id);
        Brand brand = brandRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Brand bulunamadı. ID: {}", id);
                    return new CustomNotFoundException("Brand ID " + id + " bulunamadı");
                });
        log.debug("Brand bulundu: {}", brand.getName());
        return BrandDto.entityToDto(brand);
    }

    @Override
    public BrandDto update(Long id, BrandIUDto brandIUDto, MultipartFile imageFile) throws IOException {
        log.info("Brand güncelleme isteği başlatıldı. ID: {}", id);
        
        Brand brand = brandRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Güncellenecek brand bulunamadı. ID: {}", id);
                    return new CustomNotFoundException("Brand ID " + id + " bulunamadı");
                });

        if (brandIUDto.getName() != null && !brandIUDto.getName().isBlank()
            && !brand.getName().equals(brandIUDto.getName())
            && brandRepository.existsByName(brandIUDto.getName())) {
            log.warn("Mevcut brand adı ile güncelleme girişimi: {}", brandIUDto.getName());
            throw new AlreadyExistsException("Bu marka adı zaten kayıtlı: " + brandIUDto.getName());
        }

        if (brandIUDto.getName() != null && !brandIUDto.getName().isBlank()) {
            log.debug("Brand adı güncelleniyor: {} -> {}", brand.getName(), brandIUDto.getName());
            brand.setName(brandIUDto.getName());
        }
        brand.setCountryCode(brandIUDto.getCountryCode());
        brand.setBrandUrl(brandIUDto.getBrandUrl());

        if (imageFile != null && !imageFile.isEmpty()) {
            log.debug("Brand resmi güncelleniyor");
            String oldImagePath = brand.getImagePath();
            String oldFileName = null;

            if (oldImagePath != null && !oldImagePath.isEmpty()) {
                oldFileName = oldImagePath.substring(oldImagePath.lastIndexOf("/") + 1);
            }

            ResponseEntity<Map<String, Object>> response = imageFeignManager.uploadImage(brandsUploadPath, imageFile);
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> imageData = response.getBody();
                brand.setImagePath(imageData.get("path").toString());
                log.debug("Yeni resim yüklendi: {}", imageData.get("path").toString());

                if (oldFileName != null) {
                    log.debug("Eski resim siliniyor: {}", oldFileName);
                    imageFeignManager.deleteImage(oldFileName);
                }
            } else {
                log.error("Brand resmi güncelleme başarısız: {}", response);
                throw new BadRequestException("Resim yükleme başarısız: " + response);
            }
        }

        if (brandIUDto.getIsActive() != null) {
            log.debug("Brand aktiflik durumu güncelleniyor: {} -> {}", brand.getIsActive(), brandIUDto.getIsActive());
            brand.setIsActive(brandIUDto.getIsActive());
        }

        brand.setUpdatedAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")));
        Brand updatedBrand = brandRepository.save(brand);
        log.info("Brand başarıyla güncellendi. ID: {}, Ad: {}", updatedBrand.getId(), updatedBrand.getName());
        
        return BrandDto.entityToDto(updatedBrand);
    }

    @Override
    public void delete(Long id) {
        log.info("Brand silme isteği başlatıldı. ID: {}", id);
        
        Brand brand = brandRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Silinecek brand bulunamadı. ID: {}", id);
                    return new CustomNotFoundException("Brand ID " + id + " bulunamadı");
                });

        if (brand.getImagePath() != null && !brand.getImagePath().isEmpty()) {
            String fileName = brand.getImagePath().substring(brand.getImagePath().lastIndexOf("/") + 1);
            log.debug("Brand resmi siliniyor: {}", fileName);
            imageFeignManager.deleteImage(fileName);
        }

        brand.setIsActive(false);
        brand.setUpdatedAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")));
        brandRepository.save(brand);
        log.info("Brand başarıyla silindi (deaktive edildi). ID: {}, Ad: {}", brand.getId(), brand.getName());
    }
}
