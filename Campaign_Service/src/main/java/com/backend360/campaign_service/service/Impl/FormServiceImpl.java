package com.backend360.campaign_service.service.Impl;

import com.backend360.campaign_service.dto.FormDto;
import com.backend360.campaign_service.dto.FormIUDto;
import com.backend360.campaign_service.exception.AlreadyExistsException;
import com.backend360.campaign_service.exception.BadRequestException;
import com.backend360.campaign_service.exception.CustomNotFoundException;
import com.backend360.campaign_service.model.Form;
import com.backend360.campaign_service.repository.FormRepository;
import com.backend360.campaign_service.service.IFormService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FormServiceImpl implements IFormService {
    @Autowired
    private FormRepository formRepository;

    // Email regex pattern
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$"
    );

    @Override
    public FormDto save(FormIUDto formIUDto) {
        // Temel validasyonlar
        if (formIUDto.getName() == null || formIUDto.getName().isBlank()) {
            throw new BadRequestException("Ad boş olamaz.");
        }

        if (formIUDto.getSurname() == null || formIUDto.getSurname().isBlank()) {
            throw new BadRequestException("Soyad boş olamaz.");
        }

        if (formIUDto.getEmail() == null || formIUDto.getEmail().isBlank()) {
            throw new BadRequestException("Email adresi boş olamaz.");
        }

        // Email format kontrolü
        if (!EMAIL_PATTERN.matcher(formIUDto.getEmail()).matches()) {
            throw new BadRequestException("Geçerli bir email adresi giriniz.");
        }

        // Email unique kontrolü
        if (formRepository.existsByEmail(formIUDto.getEmail())) {
            throw new AlreadyExistsException("Bu email adresi ile zaten bir form kaydı bulunmaktadır: " + formIUDto.getEmail());
        }

        Form form = Form.builder()
                .name(formIUDto.getName())
                .surname(formIUDto.getSurname())
                .email(formIUDto.getEmail())
                .phoneNumber(formIUDto.getPhoneNumber())
                .country(formIUDto.getCountry())
                .city(formIUDto.getCity())
                .town(formIUDto.getTown())
                .gender(formIUDto.getGender())
                .birthday(formIUDto.getBirthday())
                .isActive(formIUDto.getIsActive())
                .createdAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")))
                .updatedAt(null)
                .build();

        return FormDto.entityToDto(formRepository.save(form));
    }

    @Override
    public List<FormDto> findAll() {
        List<Form> forms = formRepository.findAll();

        return forms.stream()
                .map(FormDto::entityToDto)
                .collect(Collectors.toList());
    }

    @Override
    public FormDto findById(Long id) {
        Form form = formRepository.findById(id)
                .orElseThrow(() -> new CustomNotFoundException("Form ID " + id + " bulunamadı"));

        return FormDto.entityToDto(form);
    }

    @Override
    public FormDto update(Long id, FormIUDto formIUDto) {
        // Temel validasyonlar
        if (formIUDto.getName() == null || formIUDto.getName().isBlank()) {
            throw new BadRequestException("Ad boş olamaz.");
        }

        if (formIUDto.getSurname() == null || formIUDto.getSurname().isBlank()) {
            throw new BadRequestException("Soyad boş olamaz.");
        }

        if (formIUDto.getEmail() == null || formIUDto.getEmail().isBlank()) {
            throw new BadRequestException("Email adresi boş olamaz.");
        }

        // Email format kontrolü
        if (!EMAIL_PATTERN.matcher(formIUDto.getEmail()).matches()) {
            throw new BadRequestException("Geçerli bir email adresi giriniz.");
        }

        Form form = formRepository.findById(id)
                .orElseThrow(() -> new CustomNotFoundException("Form ID " + id + " bulunamadı"));

        // Email unique kontrolü (sadece değiştirilmişse)
        if (!form.getEmail().equals(formIUDto.getEmail()) && 
            formRepository.existsByEmail(formIUDto.getEmail())) {
            throw new AlreadyExistsException("Bu email adresi ile zaten bir form kaydı bulunmaktadır: " + formIUDto.getEmail());
        }

        form.setName(formIUDto.getName());
        form.setSurname(formIUDto.getSurname());
        form.setEmail(formIUDto.getEmail());
        form.setPhoneNumber(formIUDto.getPhoneNumber());
        form.setCountry(formIUDto.getCountry());
        form.setCity(formIUDto.getCity());
        form.setTown(formIUDto.getTown());
        form.setGender(formIUDto.getGender());
        form.setBirthday(formIUDto.getBirthday());
        form.setIsActive(formIUDto.getIsActive());
        form.setUpdatedAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")));
        form = formRepository.save(form);

        return FormDto.entityToDto(form);
    }

    @Override
    public void delete(Long id) {
        Form form = formRepository.findById(id)
                .orElseThrow(() -> new CustomNotFoundException("Form ID " + id + " bulunamadı"));

        form.setIsActive(false);
        form.setUpdatedAt(LocalDateTime.now(ZoneId.of("Europe/Istanbul")));
        formRepository.save(form);
    }

    @Override
    public boolean existsByEmail(String email) {
        return formRepository.existsByEmail(email);
    }

    @Override
    public boolean existsByEmailAndIsActive(String email, Boolean isActive) {
        return formRepository.existsByEmailAndIsActive(email, isActive);
    }

    @Override
    public FormDto findByEmailAndIsActive(String email, Boolean isActive) {
        Form form = formRepository.findByEmailAndIsActive(email, isActive)
                .orElseThrow(() -> new CustomNotFoundException("Email " + email + " ile aktif form bulunamadı"));
        return FormDto.entityToDto(form);
    }
}
