package com.backend360.campaign_service.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class CrmTokenManager {

    @Value("${crm.api.url}")
    private String crmApiUrl;
    
    @Value("${crm.auth.username:<EMAIL>}")
    private String crmUsername;
    
    @Value("${crm.auth.password:admin123}")
    private String crmPassword;

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    // Token cache
    private String cachedToken;
    private LocalDateTime tokenExpiry;
    private final Object tokenLock = new Object();

    /**
     * Geç<PERSON>li bir CRM token'ı döndürür. 
     * Token expire olduysa otomatik olarak yeniler.
     */
    public String getValidToken() {
        synchronized (tokenLock) {
            if (isTokenExpired()) {
                refreshToken();
            }
            return cachedToken;
        }
    }

    /**
     * Token'ın expire olup olmadığını kontrol eder
     */
    private boolean isTokenExpired() {
        if (cachedToken == null || tokenExpiry == null) {
            return true;
        }
        
        // 5 dakika buffer bırak (token 24 saat geçerli, 23:55'te yenile)
        return LocalDateTime.now().isAfter(tokenExpiry.minusMinutes(5));
    }

    /**
     * CRM'den yeni token alır
     */
    private void refreshToken() {
        try {
            log.info("CRM token yenileniyor...");
            
            // Login request hazırla
            Map<String, String> loginRequest = new HashMap<>();
            loginRequest.put("username", crmUsername);
            loginRequest.put("password", crmPassword);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, String>> entity = new HttpEntity<>(loginRequest, headers);

            // CRM Auth API'ye istek at
            String loginUrl = crmApiUrl + "/api/auth/login";
            ResponseEntity<String> response = restTemplate.postForEntity(loginUrl, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                // Response'u parse et
                JsonNode responseJson = objectMapper.readTree(response.getBody());
                
                String newToken = responseJson.get("access_token").asText();
                int expiresIn = responseJson.get("expires_in").asInt(); // saniye cinsinden
                
                // Cache'i güncelle
                this.cachedToken = newToken;
                this.tokenExpiry = LocalDateTime.now().plusSeconds(expiresIn);
                
                log.info("CRM token başarıyla yenilendi. Expire: {}", tokenExpiry);
                
            } else {
                log.error("CRM token yenileme başarısız: {}", response.getStatusCode());
                throw new RuntimeException("CRM token yenileme başarısız");
            }

        } catch (Exception e) {
            log.error("CRM token yenileme hatası: {}", e.getMessage(), e);
            throw new RuntimeException("CRM token yenileme hatası: " + e.getMessage());
        }
    }

    /**
     * Authorization header formatında token döndürür
     */
    public String getAuthorizationHeader() {
        String token = getValidToken();
        return "Bearer " + token;
    }

    /**
     * Token cache'ini temizler (test amaçlı)
     */
    public void clearTokenCache() {
        synchronized (tokenLock) {
            this.cachedToken = null;
            this.tokenExpiry = null;
            log.info("CRM token cache temizlendi");
        }
    }

    /**
     * Mevcut token bilgilerini döndürür (debug amaçlı)
     */
    public Map<String, Object> getTokenInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("hasToken", cachedToken != null);
        info.put("tokenExpiry", tokenExpiry);
        info.put("isExpired", isTokenExpired());
        info.put("tokenPreview", cachedToken != null ? cachedToken.substring(0, 20) + "..." : null);
        return info;
    }

    /**
     * CRM bağlantısını test eder
     */
    public Map<String, Object> testCrmConnection() {
        Map<String, Object> result = new HashMap<>();
        try {
            log.info("CRM bağlantı testi başlatıldı");
            
            String authHeader = getAuthorizationHeader();
            
            // Test request to CRM
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", authHeader);
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            // Test with a simple GET request
            String testUrl = crmApiUrl + "/api/v1/contacts?size=1";
            ResponseEntity<String> response = restTemplate.exchange(testUrl, HttpMethod.GET, entity, String.class);
            
            result.put("success", true);
            result.put("statusCode", response.getStatusCode().value());
            result.put("message", "CRM bağlantısı başarılı");
            result.put("tokenInfo", getTokenInfo());
            
            log.info("CRM bağlantı testi başarılı: {}", response.getStatusCode());
            
        } catch (Exception e) {
            log.error("CRM bağlantı testi başarısız: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("message", "CRM bağlantısı başarısız");
            result.put("tokenInfo", getTokenInfo());
        }
        
        return result;
    }

    /**
     * CRM login endpoint'ini test eder
     */
    public Map<String, Object> testLoginEndpoint() {
        Map<String, Object> result = new HashMap<>();
        try {
            log.info("CRM login endpoint testi başlatıldı");
            
            // Login request hazırla
            Map<String, String> loginRequest = new HashMap<>();
            loginRequest.put("username", crmUsername);
            loginRequest.put("password", crmPassword);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, String>> entity = new HttpEntity<>(loginRequest, headers);

            // CRM Auth API'ye istek at
            String loginUrl = crmApiUrl + "/api/auth/login";
            ResponseEntity<String> response = restTemplate.postForEntity(loginUrl, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                result.put("success", true);
                result.put("statusCode", response.getStatusCode().value());
                result.put("message", "Login başarılı");
                result.put("responsePreview", response.getBody().substring(0, Math.min(100, response.getBody().length())) + "...");
                
                log.info("CRM login testi başarılı: {}", response.getStatusCode());
            } else {
                result.put("success", false);
                result.put("statusCode", response.getStatusCode().value());
                result.put("message", "Login başarısız");
                result.put("response", response.getBody());
                
                log.error("CRM login testi başarısız: {}", response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("CRM login testi başarısız: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("message", "Login testi başarısız");
        }
        
        return result;
    }
}
