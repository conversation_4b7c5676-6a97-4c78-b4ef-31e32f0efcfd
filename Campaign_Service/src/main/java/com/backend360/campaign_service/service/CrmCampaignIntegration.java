package com.backend360.campaign_service.service;

import com.backend360.campaign_service.crm_client.CrmClient;
import com.backend360.campaign_service.dto.crm.CampaignIUDto;
import com.backend360.campaign_service.dto.crm.CampaignDto;
import com.backend360.campaign_service.model.Campaign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CrmCampaignIntegration {

    @Autowired
    private CrmClient crmClient;

    @Autowired
    private CrmTokenManager crmTokenManager;

    public void sendCampaignToCrm(Campaign campaign) {
        try {
            log.info("Sending campaign to CRM: {}", campaign.getName());

            // Campaign bilgilerini CampaignIUDto'ya çevir
            CampaignIUDto campaignDto = convertCampaignToCrmCampaign(campaign);

            // CRM'e gönder
            ResponseEntity<CampaignDto> response = crmClient.createCampaign(campaignDto, crmTokenManager.getAuthorizationHeader());

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Campaign sent to CRM successfully: {}", response.getBody());
            } else {
                log.error("Failed to send campaign to CRM: {}", response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("Error sending campaign to CRM: {}", e.getMessage(), e);
        }
    }

    private CampaignIUDto convertCampaignToCrmCampaign(Campaign campaign) {
        return CampaignIUDto.builder()
                .name(campaign.getName())
                .description(campaign.getDescription())
                .status(campaign.getIsActive() != null && campaign.getIsActive() ? "active" : "draft")
                .campaignType("call") // Default olarak "call" tipi
                .startDate(campaign.getStartDate())
                .endDate(campaign.getEndDate())
                .build();
    }
} 