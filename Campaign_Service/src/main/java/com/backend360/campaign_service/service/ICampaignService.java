package com.backend360.campaign_service.service;

import com.backend360.campaign_service.dto.CampaignDto;
import com.backend360.campaign_service.dto.CampaignIUDto;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface ICampaignService {
    CampaignDto save(CampaignIUDto campaignIUDto);

    List<CampaignDto> findAll();

    CampaignDto findById(Long id);

    CampaignDto update(Long id, CampaignIUDto campaignIUDto);

    void delete(Long id);

    List<String> getRemindMeCustomerEmails();
}
