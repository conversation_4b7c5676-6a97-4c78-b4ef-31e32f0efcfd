package com.backend360.campaign_service.service;

import com.backend360.campaign_service.dto.FormToCampaignDto;
import com.backend360.campaign_service.dto.FormToCampaignIUDto;

import java.util.List;

public interface IFormToCampaignService {
    FormToCampaignDto save(FormToCampaignIUDto formToCampaignIUDto);

    List<FormToCampaignDto> findAll();

    FormToCampaignDto findById(Long id);

    FormToCampaignDto update(Long id, FormToCampaignIUDto formToCampaignIUDto);

    void delete(Long id);

    FormToCampaignDto findByFormId(Long formId);

    // Public form submission için yeni metodlar
    boolean existsByFormIdAndCampaignIdAndIsActive(Long formId, Long campaignId, Boolean isActive);
    FormToCampaignDto findByFormIdAndCampaignIdAndIsActive(Long formId, Long campaignId, Boolean isActive);
}
