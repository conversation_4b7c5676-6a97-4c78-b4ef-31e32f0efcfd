package com.backend360.campaign_service.service;

import com.backend360.campaign_service.dto.BrandToCategoryDto;
import com.backend360.campaign_service.dto.BrandToCategoryIUDto;

import java.util.List;

public interface IBrandToCategoryService {
    BrandToCategoryDto save(BrandToCategoryIUDto brandToCategoryIUDto);

    List<BrandToCategoryDto> findAll();

    BrandToCategoryDto findById(Long id);

    BrandToCategoryDto update(Long id, BrandToCategoryIUDto brandToCategoryIUDto);

    void delete(Long id);
}
