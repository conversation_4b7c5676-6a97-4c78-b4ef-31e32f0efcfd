package com.backend360.campaign_service.service;

import com.backend360.campaign_service.dto.CampaignDetailDto;
import com.backend360.campaign_service.dto.CampaignDetailIUDto;

import java.util.List;

public interface ICampaignDetailService {
    CampaignDetailDto save(CampaignDetailIUDto campaignDetailIUDto);

    List<CampaignDetailDto> findAll();

    CampaignDetailDto findById(Long id);

    CampaignDetailDto update(Long id, CampaignDetailIUDto campaignDetailIUDto);

    void delete(Long id);
}
