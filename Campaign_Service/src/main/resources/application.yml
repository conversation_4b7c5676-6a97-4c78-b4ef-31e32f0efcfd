spring:
  application:
    name: campaign-service

  cloud:
    config:
      uri: http://config-service:8888

  config:
    import: "configserver:"

logging:
  level:
    org.springframework.security: DEBUG
    com.backend360.campaign_service.config: DEBUG

  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 10MB

  kafka:
    bootstrap-servers: kafka:9092
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      acks: all
      retries: 3
      batch-size: 16384
      linger-ms: 1
      buffer-memory: 33554432

management:
  endpoints:
    web:
      exposure:
        include: "*"

# Image Service bağlantısı (opsiyonel, sabit URI ile çalışılıyorsa kaldırılabilir)
image-service:
  url: http://image-service:9002

image:
  service:
    base-url: https://360avantajli.com/api/Image_Service/image/

feign:
  client:
    config:
      default:
        loggerLevel: full
  multipart:
    enabled: true



# CRM Integration Configuration
crm:
  api:
    url: http://crm-api-gateway:6002
  auth:
    username: ${CRM_AUTH_USERNAME:<EMAIL>}
    password: ${CRM_AUTH_PASSWORD:admin123}