image: docker:latest

services:
  - docker:dind

stages:
  - package
  - build
  - deploy

variables:
  DOCKER_TLS_CERTDIR: ""

package_backend:
  stage: package
  image: maven:3.8.6-eclipse-temurin-17
  script:
    - cd Discovery_Service && mvn clean package -DskipTests -Dmaven.compiler.forceJavacCompilerUse=true && cd ..
    - cd Config_Server && mvn clean package -DskipTests -Dmaven.compiler.forceJavacCompilerUse=true && cd ..    
    - cd Api-Gateway_Service && mvn clean package -DskipTests -Dmaven.compiler.forceJavacCompilerUse=true && cd ..
    - cd Campaign_Service && mvn clean package -DskipTests -Dmaven.compiler.forceJavacCompilerUse=true && cd ..
    - cd Image_Service && mvn clean package -DskipTests -Dmaven.compiler.forceJavacCompilerUse=true && cd ..
    - cd Auth_Service && mvn clean package -DskipTests -Dmaven.compiler.forceJavacCompilerUse=true && cd ..
    - cd Mail_Service && mvn clean package -DskipTests -Dmaven.compiler.forceJavacCompilerUse=true && cd ..    
  only:
    - dev
  tags:
    - backend

# Discovery Service
build_discovery:
  stage: build
  before_script:
    - echo "$DOCKER_PASSWORD" | docker login -u "$DOCKER_USERNAME" --password-stdin
  script:
    - docker build -t docker-discovery-service -f Discovery_Service/Dockerfile .
  only:
    - dev
  tags:
    - backend

build_image:
  stage: build
  before_script:
    - echo "$DOCKER_PASSWORD" | docker login -u "$DOCKER_USERNAME" --password-stdin
  script:
    - docker build -t docker-image-service -f Image_Service/Dockerfile .
  only:
    - dev
  tags:
    - backend

# Mail service
build_mail:
  stage: build
  before_script:
    - echo "$DOCKER_PASSWORD" | docker login -u "$DOCKER_USERNAME" --password-stdin
  script:
    - docker build -t docker-mail-service -f Mail_Service/Dockerfile .
  only:
    - dev
  tags:
    - backend

# API Gateway
build_gateway:
  stage: build
  before_script:
    - echo "$DOCKER_PASSWORD" | docker login -u "$DOCKER_USERNAME" --password-stdin
  script:
    - docker build -t docker-api-gateway -f Api-Gateway_Service/Dockerfile .
  only:
    - dev
  tags:
    - backend

# Config Server
build_config:
  stage: build
  before_script:
    - echo "$DOCKER_PASSWORD" | docker login -u "$DOCKER_USERNAME" --password-stdin
  script:
    - docker build -t docker-config-service -f Config_Server/Dockerfile .
  only:
    - dev
  tags:
    - backend

# Campaign Service
build_campaign:
  stage: build
  before_script:
    - echo "$DOCKER_PASSWORD" | docker login -u "$DOCKER_USERNAME" --password-stdin
  script:
    - docker build -t docker-campaign-service -f Campaign_Service/Dockerfile .
  only:
    - dev
  tags:
    - backend

# Auth Service
build_auth:
  stage: build
  before_script:
    - echo "$DOCKER_PASSWORD" | docker login -u "$DOCKER_USERNAME" --password-stdin
  script:
    - docker build -t docker-auth-service -f Auth_Service/Dockerfile .
  only:
    - dev
  tags:
    - backend

# Deployment (sunucuya rsync ve docker compose)
deploy_backend:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk update && apk add openssh-client rsync
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - ssh-keyscan -H ************ >> ~/.ssh/known_hosts
  script:
    - rsync -avz --delete ./ root@************:~/docker/backend/
    - |
      ssh root@************ << 'EOF'
      set -e
      cd ~/docker
      echo "--- discovery-service build ---"
      docker compose build discovery-service
      docker compose up -d discovery-service
      sleep 3
      echo "--- config-service build ---"
      docker compose build config-service
      docker compose up -d config-service
      sleep 3
      echo "--- campaign-service build ---"
      docker compose build campaign-service
      docker compose up -d campaign-service
      sleep 3
      echo "--- api-gateway build ---"
      docker compose build api-gateway
      docker compose up -d api-gateway
      sleep 3
      echo "--- image-service build ---"
      docker compose build image-service
      docker compose up -d image-service
      sleep 3
      echo "--- mail-service build ---"
      docker compose build mail-service
      docker compose up -d mail-service
      sleep 3      
      echo "--- auth-service build ---"
      docker compose build auth-service
      docker compose up -d auth-service
      EOF
  environment:
    name: production
  only:
    - dev
  tags:
    - backend
