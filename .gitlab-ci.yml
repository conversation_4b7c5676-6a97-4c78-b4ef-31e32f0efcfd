image: docker:latest

services:
  - docker:dind

stages:
  - package
  - build
  - deploy

variables:
  DOCKER_TLS_CERTDIR: ""

package_backend:
  stage: package
  image: maven:3.8.6-eclipse-temurin-17
  script:
    - cd Discovery_Service && mvn clean package -DskipTests -Dmaven.compiler.forceJavacCompilerUse=true && cd ..
    - cd Config_Server && mvn clean package -DskipTests -Dmaven.compiler.forceJavacCompilerUse=true && cd ..    
    - cd Api-Gateway_Service && mvn clean package -DskipTests -Dmaven.compiler.forceJavacCompilerUse=true && cd ..
    - cd CRM_Service && mvn clean package -DskipTests -Dmaven.compiler.forceJavacCompilerUse=true && cd ..  
  only:
    - dev
  tags:
    - CRM-API
# Discovery Service
build_discovery:
  stage: build
  script:
    - docker build -t docker-crm-discovery-service -f Discovery_Service/Dockerfile .
  only:
    - dev
  tags:
    - CRM-API



# API Gateway
build_gateway:
  stage: build
  script:
    - docker build -t docker-crm-api-gateway -f Api-Gateway_Service/Dockerfile .
  only:
    - dev
  tags:
    - CRM-API

# Config Server
build_crm_config:
  stage: build
  script:
    - docker build -t docker-crm-config-server -f Config_Server/Dockerfile .
  only:
    - dev
  tags:
    - CRM-API

# Crm Service
build_crm:
  stage: build
  script:
    - docker build -t docker-crm-main-service -f CRM_Service/Dockerfile .
  only:
    - dev
  tags:
    - CRM-API




# Deployment (sunucuya rsync ve docker compose)
deploy_backend:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk update && apk add openssh-client rsync
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - ssh-keyscan -H ************ >> ~/.ssh/known_hosts
  script:
    - rsync -avz --delete ./ root@************:~/docker/CRM-API/
    - |
      ssh root@************ << 'EOF'
      set -e
      cd ~/docker
      echo "--- crm-discovery-service build ---"
      docker compose build crm-discovery-service
      docker compose up -d crm-discovery-service
      sleep 3
      echo "--- crm-config-service build ---"
      docker compose build crm-config-server
      docker compose up -d crm-config-server
      sleep 3
      echo "--- crm-main-service build ---"
      docker compose build crm-main-service
      docker compose up -d crm-main-service
      sleep 3
      echo "--- api-gateway build ---"
      docker compose build crm-api-gateway
      docker compose up -d crm-api-gateway
      sleep 3
      EOF
  environment:
    name: production
  only:
    - dev
  tags:
    - CRM-API
