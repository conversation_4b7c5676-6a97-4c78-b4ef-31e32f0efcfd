FROM maven:3.8.6-eclipse-temurin-17 AS build
WORKDIR /app

# Gerekli klasör ve dosyaları kopyala
COPY pom.xml /app/pom.xml
COPY Service_Parent /app/Service_Parent
COPY Api-Gateway_Service /app/Api-Gateway_Service

# Parent pom'u yükle
RUN mvn install -f Service_Parent/pom.xml

# Api Gateway'i build et
WORKDIR /app/Api-Gateway_Service
RUN mvn clean package -DskipTests

# Çalıştırma aşaması
FROM openjdk:17-jdk-slim
WORKDIR /app
COPY --from=build /app/Api-Gateway_Service/target/*.jar app.jar
ENTRYPOINT ["java", "-jar", "/app/app.jar"]
EXPOSE 6002
