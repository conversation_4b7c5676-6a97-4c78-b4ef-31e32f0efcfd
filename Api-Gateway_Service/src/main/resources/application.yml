server:
  port: 6002

spring:
  application:
    name: crm-api-gateway

  main:
    web-application-type: reactive

  cloud:
    config:
      uri: http://crm-config-server:6001
    compatibility-verifier:
      enabled: false

  config:
    import: "configserver:"

management:
  endpoints:
    web:
      exposure:
        include: "*"

eureka:
  client:
    service-url:
      defaultZone: http://crm-discovery-service:6000/eureka/