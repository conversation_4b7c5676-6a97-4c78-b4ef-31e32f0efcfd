<configuration>

    <!-- Console <PERSON>ppender -->
    <appender name="CONSOL<PERSON>" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %highlight(%-5level) %cyan(%logger{36}) - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Graylog Appender (optional, only if graylog is available) -->
    <appender name="GRAYLOG" class="de.siegmar.logbackgelf.GelfUdpAppender">
        <graylogHost>graylog</graylogHost>
        <graylogPort>12201</graylogPort>
        <facility>api-gateway</facility>
    </appender>

    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <!-- Uncomment when graylog is available -->
        <!-- <appender-ref ref="GRAYLOG" /> -->
    </root>

</configuration>

